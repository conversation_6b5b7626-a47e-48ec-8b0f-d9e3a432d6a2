plugins {
	id 'java'
	id 'org.springframework.boot' version '3.3.1'
	id 'io.spring.dependency-management' version '1.1.5'
	id 'war'
}

war { enabled = true } // gradle build的時候Task :war才不會被SKIPPED

group = 'com.twport'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
	sourceCompatibility = '17'
}

repositories {
	mavenCentral()
}

dependencies {
	implementation (
		'org.springframework.boot:spring-boot-starter-data-jpa',
		'org.springframework.boot:spring-boot-starter-thymeleaf',
		'org.springframework.boot:spring-boot-starter-web',
		'org.springframework.boot:spring-boot-starter-log4j2',
		'org.springframework.boot:spring-boot-starter-validation',
		'org.springframework.boot:spring-boot-starter-security',
		'org.springframework.boot:spring-boot-starter-actuator',
		'org.slf4j:slf4j-api:2.0.12',
		'nz.net.ultraq.thymeleaf:thymeleaf-layout-dialect:3.0.0',
		'org.thymeleaf.extras:thymeleaf-extras-springsecurity6:3.1.2.RELEASE',
		'org.apache.commons:commons-lang3:3.15.0',
		'com.itextpdf:kernel:8.0.5',
		'org.xhtmlrenderer:flying-saucer-core:9.9.0',
		'org.xhtmlrenderer:flying-saucer-pdf:9.9.0',
		'commons-io:commons-io:2.16.1',
		'org.apache.poi:poi-ooxml:5.2.3',
		'org.springframework.boot:spring-boot-starter-data-ldap:3.3.3',
		'org.springframework.ldap:spring-ldap-core:3.2.6',
		'org.springframework.security:spring-security-ldap:6.3.3'
	)
	modules {
		module ('org.springframework.boot:spring-boot-starter-logging') {
			replacedBy 'org.springframework.boot:spring-boot-starter-log4j2'
		}
	}
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	runtimeOnly 'org.postgresql:postgresql'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
	providedRuntime("org.springframework.boot:spring-boot-starter-tomcat") //移除springboot的內建tomcat
}

tasks.named('test') {
	useJUnitPlatform()
}
