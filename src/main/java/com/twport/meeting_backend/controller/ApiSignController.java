package com.twport.meeting_backend.controller;

import java.io.IOException;
import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.service.ApiSignService;
import com.twport.meeting_backend.vo.request.ApiSignDataRequest;
import com.twport.meeting_backend.vo.request.ApiSignMeetingQueryRequest;
import com.twport.meeting_backend.vo.request.ApiSignUploadSignDataRequest;
import com.twport.meeting_backend.vo.request.MeetingEndAndExtendRequest;
import com.twport.meeting_backend.vo.response.ApiSignDataResponse;
import com.twport.meeting_backend.vo.response.ApiSignMeetingQueryResponse;
import com.twport.meeting_backend.vo.response.ApiSignRoomResponse;
import com.twport.meeting_backend.vo.response.base.AjaxRestResponse;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/signApi")
@Slf4j
public class ApiSignController {

	private final ApiSignService apiService;

	public ApiSignController(ApiSignService apiService) {
		this.apiService = apiService;
	}

	@PostMapping("/query")
	public AjaxRestResponse<ApiSignMeetingQueryResponse> nowReserveQuery(
			@RequestBody ApiSignMeetingQueryRequest request) {
		return AjaxRestResponse.rest(apiService.nowReserveQuery(request), ResponseStatusCode.SEARCH_SUCCESS);
	}

	@PostMapping("/sign/upload")
	public AjaxRestResponse<Void> signDataUpload(@RequestBody ApiSignUploadSignDataRequest request) throws IOException {
		apiService.signDataUpload(request);
		return AjaxRestResponse.rest(ResponseStatusCode.INSERT);
	}

	/*
	 * 查看簽到名單
	 */
	@PostMapping("/sign")
	public AjaxRestResponse<List<ApiSignDataResponse>> signDataQuery(@RequestBody ApiSignDataRequest request) {
		return AjaxRestResponse.rest(apiService.signDataQuery(request), ResponseStatusCode.SEARCH_SUCCESS);
	}
	
	/*
	 * 會議室清單
	 */
	@GetMapping("/roomlist")
	public AjaxRestResponse<List<ApiSignRoomResponse>> meetingRoomQuery() {
		return AjaxRestResponse.rest(apiService.meetingRoomQuery(), ResponseStatusCode.SEARCH_SUCCESS);
	}
	
	@PostMapping("/meetingended")
	public AjaxRestResponse<Void> StatusUpdate(@RequestBody MeetingEndAndExtendRequest request) {
		apiService.StatusUpdate(request);
		return AjaxRestResponse.rest(ResponseStatusCode.UPDATE);	
	}
	
	@PostMapping("/meetingextended")
	public AjaxRestResponse<Void> extended(@RequestBody MeetingEndAndExtendRequest request) {
		apiService.extended(request);
		return AjaxRestResponse.rest(ResponseStatusCode.UPDATE);
		
	}
	
}
