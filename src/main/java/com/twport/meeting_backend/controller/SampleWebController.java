package com.twport.meeting_backend.controller;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.Errors;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.repository.AppUserRepository;
import com.twport.meeting_backend.repository.CompanyListRepository;
import com.twport.meeting_backend.repository.DepartmentListRepository;
import com.twport.meeting_backend.service.RecordService;
import com.twport.meeting_backend.vo.dto.SelectOption;
import com.twport.meeting_backend.vo.dto.sample.AppUserDto;
import com.twport.meeting_backend.vo.response.RecordInitResponse;

import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年7月22日 
 * @class SampleWebController
 * @description Web Controller 範例
 */
@Controller
@RequestMapping("/sample/web")
@Slf4j
public class SampleWebController {
	
	private final AppUserRepository appUserRepository;
	
	private final CompanyListRepository companyListRepository;
	
	private final DepartmentListRepository departmentListRepository;
	
	private final PasswordEncoder passwordEncoder;
	
	private final RecordService recordService;
	
	public SampleWebController(AppUserRepository appUserRepository, 
			CompanyListRepository companyListRepository,
			DepartmentListRepository departmentListRepository, 
			PasswordEncoder passwordEncoder,
			RecordService recordService) {
		this.appUserRepository = appUserRepository;
		this.companyListRepository = companyListRepository;
		this.departmentListRepository = departmentListRepository;
		this.passwordEncoder = passwordEncoder;
		this.recordService = recordService;
	}

	@GetMapping("/register")
	public String register(Model model, AppUserDto userDto) {
		log.info("sample register");
		
		List<SelectOption> companyOptionList = companyListRepository.findAll()
				.stream().map(company -> {
					SelectOption companyOpt = new SelectOption(String.valueOf(company.getId()), company.getName());
					
					List<SelectOption> departList = departmentListRepository
							.findByCompanyId(company.getId()).stream().map(depart -> {
								return new SelectOption(String.valueOf(depart.getId()), depart.getName());
							}).collect(Collectors.toList());
					
					companyOpt.setSubOptions(departList);
					return companyOpt;
				}).collect(Collectors.toList());
				
		model.addAttribute("user", userDto);
		model.addAttribute("companySelect", companyOptionList);
		return "sample/register";
	}
	
	@PostMapping("/register")
	public String registerForm(@ModelAttribute("user") @Valid AppUserDto userDto, 
			BindingResult result, Errors errors) {
		
		if(errors.hasErrors()) {
			for(FieldError err : errors.getFieldErrors()) {
				log.info("error field : {}", err.getField());
				log.info("error message : {}", err.getDefaultMessage());
			}
		}
		
		if(result.hasErrors()) {
			for(FieldError error : result.getFieldErrors()) {
				log.info("error field : {}", error.getField());
				log.info("error message : {}", error.getDefaultMessage());
			}
			
			return "sample/register";
		}else {
			AppUser user = new AppUser();
			user.setAccount(userDto.getUsername());
			user.setPassword(passwordEncoder.encode(userDto.getPassword()));
			user.setFirstName(userDto.getName().substring(0,1));
			user.setLastName(userDto.getName().substring(1));
			user.setFullName(userDto.getName());
			user.setCompanyId(userDto.getCompany());
			user.setDepartmentId(userDto.getDepartment());
			user.setRole("ROLE_USER");
			
			appUserRepository.save(user);
		}
		
		return "redirect:/user/login?register";
	}
	
	@GetMapping("/dataTable")
	public String dataTable(Model model) {
		RecordInitResponse resp = new RecordInitResponse();
		resp.setMeetingRoomSelects(recordService.getAllDefaultSelect().getMeetingRoomSelects());
		model.addAttribute("resp", resp);
		
		return "sample/datatable";
	}
}
