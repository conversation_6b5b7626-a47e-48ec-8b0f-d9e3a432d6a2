package com.twport.meeting_backend.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.service.RecordService;
import com.twport.meeting_backend.vo.dto.RecordQueryPageModel;
import com.twport.meeting_backend.vo.request.RecordQueryRequest;
import com.twport.meeting_backend.vo.request.RecordSignFileRequest;
import com.twport.meeting_backend.vo.request.RecordSignedDetailRequest;
import com.twport.meeting_backend.vo.request.RecordSignedEditInitRequest;
import com.twport.meeting_backend.vo.request.RecordSignedEditRequest;
import com.twport.meeting_backend.vo.response.RecordSignedDetailResponse;
import com.twport.meeting_backend.vo.response.RecordSignedEditInitResponse;
import com.twport.meeting_backend.vo.response.RecordSignedPdfResponse;
import com.twport.meeting_backend.vo.response.base.AjaxRestResponse;
import com.twport.meeting_backend.vo.response.base.PageResponse;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年7月26日 
 * @class RecordController
 * @description 會議室查詢Controller
 */
@RestController
@RequestMapping("/meeting/record")
@Slf4j
public class RecordController {

	private final RecordService recordService;
	
	public RecordController(RecordService recordService) {
		this.recordService = recordService;
	}
	
	@GetMapping("")
	public ModelAndView init() {
		ModelAndView mv = new ModelAndView("record");
		mv.addObject("resp", recordService.getAllDefaultSelect());
		return mv;
	}
	
	@PostMapping("/query")
	public AjaxRestResponse<PageResponse<RecordQueryPageModel>> query(@RequestBody RecordQueryRequest request) 
	{
		return AjaxRestResponse.rest(recordService.getRecordTableData(request), ResponseStatusCode.SEARCH_SUCCESS);
	}
	
	@PostMapping("/signIn")
	public AjaxRestResponse<List<RecordSignedDetailResponse>> getSignedDetails(@RequestBody RecordSignedDetailRequest request) {
		return AjaxRestResponse.rest(recordService.getSignedDetails(request), ResponseStatusCode.SEARCH_SUCCESS);
	}
	
	/**
	 * 
	 * <AUTHOR> Lin
	 * @datetime 2024年9月18日
	 * @method 
	 * @return
	 * @description 顯示PDF檔案排版(TODO 上線後需刪除)
	 */
	@GetMapping("/sign-record")
	public ModelAndView signRecordFile() {
		ModelAndView mv = new ModelAndView("signRecord");
		
		RecordSignFileRequest request = new RecordSignFileRequest();
		//request.setReserveId(Long.valueOf(21));
		RecordSignedPdfResponse response = recordService.signRecordFile(request);
		mv.addObject("theme", response.getTheme());
		mv.addObject("reserveTime", response.getReserveTime());
		mv.addObject("reserveLocate", response.getReserveLocate());
		mv.addObject("hostName", response.getHostName());
		mv.addObject("signRecord", response.getSignRecord());
		
		return mv;
	}
	
	@PostMapping("/signFile")
	public void exportSignRecordFile(@RequestBody RecordSignFileRequest request, HttpServletResponse response) {
		recordService.createSignInRecordFile(request, response);
	}
	
	@PostMapping("/signEditInit")
	public AjaxRestResponse<RecordSignedEditInitResponse> signInEditInit(@RequestBody RecordSignedEditInitRequest request) {
		return AjaxRestResponse.rest(recordService.signRecordEditInit(request), ResponseStatusCode.SUCCESS);
	}
	
	@PostMapping("/signInEdit")
	public AjaxRestResponse<Void> signInEdit(@RequestBody RecordSignedEditRequest request) {
		recordService.signInEdit(request);
		return AjaxRestResponse.rest(ResponseStatusCode.UPDATE);
	}
}
