package com.twport.meeting_backend.controller;

import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.service.ReserveService;
import com.twport.meeting_backend.vo.request.*;
import com.twport.meeting_backend.vo.response.ReserveCompanyChangeResponse;
import com.twport.meeting_backend.vo.response.ReserveDepartmentChangeResponse;
import com.twport.meeting_backend.vo.response.ReserveDetailResponse;
import com.twport.meeting_backend.vo.response.ReserveQueryDayReserveResponse;
import com.twport.meeting_backend.vo.response.ReserveQueryResponse;
import com.twport.meeting_backend.vo.response.ReserveTimeQueryResponse;
import com.twport.meeting_backend.vo.response.base.AjaxRestResponse;


/**
 * <AUTHOR>
 * @datetime 2024年7月22日 
 * @class ApiController
 * @description 會議室預約
 */
@RestController
@RequestMapping("/meeting/reserve")
@Slf4j
public class ReserveController {    
    private final ReserveService reserveService;
    
    public ReserveController(ReserveService reserveService) {
        this.reserveService = reserveService;
    }

    /**
     * reserve view
     * 會議預約畫面
     */
    @GetMapping("")
    public ModelAndView init() {
        ModelAndView mv = new ModelAndView("reserve");
        mv.addObject("resp", reserveService.getAllDefaultSelect());
        return mv;
    }
    
    @PostMapping("/companyChange")
    public AjaxRestResponse<ReserveCompanyChangeResponse> companySelectChange(@RequestBody ReserveCompanyChangeRequest request) {
    	return AjaxRestResponse.rest(reserveService.companySelectChange(request), ResponseStatusCode.SEARCH_SUCCESS);
    }
    
    @PostMapping("/departmentChange")
    public AjaxRestResponse<ReserveDepartmentChangeResponse> departmentSelectChange(@RequestBody ReserveDepartmentChangeRequest request) {
    	return AjaxRestResponse.rest(reserveService.departmentSelectChange(request), ResponseStatusCode.SEARCH_SUCCESS);
    }

    /**
     *
     * ajax
     * 查詢所有會議室預約資訊
     * type = 查詢種類 月/周/日
     */
    @PostMapping ("/query")
    public AjaxRestResponse<ReserveQueryResponse> query(@RequestBody ReserveQueryRequest request) {
        return AjaxRestResponse.rest(reserveService.query(request), ResponseStatusCode.SEARCH_SUCCESS);
    }
    
    
    /**
     * 
     * <AUTHOR> Lin
     * @datetime 2024年8月26日
     * @method 
     * @param request
     * @return
     * @description 查詢指定日期的所有會議室預約資訊
     */
    @PostMapping("/queryDayReserveDetails")
    public AjaxRestResponse<ReserveQueryDayReserveResponse> queryDayReserveDetails(@RequestBody ReserveQueryDayReserveRequest request) {
    	return AjaxRestResponse.rest(reserveService.queryOneDayofReserveDetails(request), ResponseStatusCode.SEARCH_SUCCESS);
    }

/**
     *
     * 新增預約會議
     * 【條件】
     * 登入人員須有權限
     * 會議室要為啟用狀態
     * 會議室預約時間不得衝突
     *
     */
    @PostMapping("/add")
    public AjaxRestResponse<Void> add(@RequestBody @Valid ReserveAddRequest request) {
        reserveService.saveReserve(request);
        return AjaxRestResponse.rest(ResponseStatusCode.INSERT);
    }

    /**
     *
     * ajax
     * 刪除預約會議
     * 【條件】
     * 登入人員須有權限
     * 必須是該創建會議人員
     * 該會議狀態必須為「預約中」
     *
     */
    @PostMapping("/delete")
    public AjaxRestResponse<Void> delete(@RequestBody ReserveDeleteRequest request) {
        reserveService.delete(request);
        return AjaxRestResponse.rest(ResponseStatusCode.DELETE);
    }

    /**
     *
     * ajax
     * 更新預約會議
     * 【條件】
     * 登入人員需要權限
     * 必須是該創建會議人員
     * 該會議狀態必須為「預約中」
     *
     */
    @PostMapping("/update")
    public AjaxRestResponse<Void> update(@RequestBody ReserveUpdateRequest request) {
        reserveService.update(request);
        return AjaxRestResponse.rest(ResponseStatusCode.UPDATE);
    }
    
    @PostMapping("/reservetime/query")
    public AjaxRestResponse<ReserveTimeQueryResponse> reserveTimeQuery(@RequestBody ReserveTimeQueryRequest request) {
    	return AjaxRestResponse.rest(reserveService.reserveTimeQuery(request), ResponseStatusCode.SEARCH_SUCCESS);
    }

    /**
     *
     * 查詢會議細節
     *
     */
    @PostMapping("/details")
    public AjaxRestResponse<ReserveDetailResponse> details(@RequestBody ReserveDetailRequest request) {
    	log.info(" details meetingId : {}",  request.getMeetingId());
        return AjaxRestResponse.rest(reserveService.details(request),ResponseStatusCode.SEARCH_SUCCESS);
    }

}