package com.twport.meeting_backend.controller;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import com.twport.meeting_backend.security.AppUserDetails;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Controller
@RequestMapping("/user")
@Slf4j
public class UserController {

	@GetMapping("/login")
	public String login() {
		System.out.println("get login");
		
		return "login";
	}
	
	@RequestMapping("/logout")
	public String logout(Authentication authentication, HttpServletRequest request, HttpServletResponse response) {
		SecurityContextLogoutHandler securityContextLogoutHandler = new SecurityContextLogoutHandler();
		securityContextLogoutHandler.logout(request, response, authentication);
		return "redirect:/user/login?logout";
	}
	
	@GetMapping("/index")
	public String index() {
		Authentication au = SecurityContextHolder.getContext().getAuthentication();
		
		if(au != null) {
			log.info("user name : {}", au.getName());
			AppUserDetails user = (AppUserDetails)au.getPrincipal();
			log.info("user principal : {}", user);
		}
		
		return "search";
	}
}
