package com.twport.meeting_backend.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.service.ApiInfoService;
import com.twport.meeting_backend.vo.request.ApiInfoMeetingQueryRequest;
import com.twport.meeting_backend.vo.request.ApiInfoMeetingSearchQueryRequest;
import com.twport.meeting_backend.vo.request.CardSignRequest;
import com.twport.meeting_backend.vo.response.ApiInfoMeetingQueryResponse;
import com.twport.meeting_backend.vo.response.ApiInfoMeetingSearchQueryResponse;
import com.twport.meeting_backend.vo.response.ApiInfoSettingRoomResponse;
import com.twport.meeting_backend.vo.response.CardSignResponse;
import com.twport.meeting_backend.vo.response.base.AjaxRestResponse;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/infoApi")
@Slf4j
public class ApiInfoController {

	private final ApiInfoService apiService;

	public ApiInfoController(ApiInfoService apiService) {
		this.apiService = apiService;
	}

	/**
	 * 會議詳情查詢
	 */
	@PostMapping("/query")
	public AjaxRestResponse<ApiInfoMeetingQueryResponse> nowReserveQuery(
			@RequestBody ApiInfoMeetingQueryRequest request) {
		log.info("nowReserveQuery controller");
		return AjaxRestResponse.rest(apiService.nowReserveQuery(request), ResponseStatusCode.SEARCH_SUCCESS);
	}

	/*
	 * 搜尋會議
	 */
	@PostMapping("/search")
	public AjaxRestResponse<List<ApiInfoMeetingSearchQueryResponse>> nowSearchReserveQuery(
			@RequestBody ApiInfoMeetingSearchQueryRequest request) {
		log.info("nowSearchReserveQuery controller");
		return AjaxRestResponse.rest(apiService.nowSearchReserveQuery(request), ResponseStatusCode.SEARCH_SUCCESS);
	}

	/**
	 * 設定會議室
	 */
	@GetMapping("/settingRoom")
	public AjaxRestResponse<List<ApiInfoSettingRoomResponse>> meetingRoomQuery() {
		log.debug("meetingRoomQuery controller");
		return AjaxRestResponse.rest(apiService.meetingRoomQuery(), ResponseStatusCode.SEARCH_SUCCESS);
	}

	/**
	 * 卡片登入
	 */
	@PostMapping("/card/sign")
	public AjaxRestResponse<CardSignResponse> cardSign(@RequestBody CardSignRequest request) {
		log.debug("cardSign controller");
		return AjaxRestResponse.rest(apiService.cardSign(request), ResponseStatusCode.SEARCH_SUCCESS);
	}

}
