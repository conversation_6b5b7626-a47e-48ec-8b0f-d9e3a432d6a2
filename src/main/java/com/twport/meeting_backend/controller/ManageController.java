package com.twport.meeting_backend.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.service.ManageService;
import com.twport.meeting_backend.vo.request.ManageAddRequest;
import com.twport.meeting_backend.vo.request.ManageDeleteRequest;
import com.twport.meeting_backend.vo.request.ManageQueryRequest;
import com.twport.meeting_backend.vo.request.ManageUpdateRequest;
import com.twport.meeting_backend.vo.response.ManageAddResponse;
import com.twport.meeting_backend.vo.response.ManageQueryResponse;
import com.twport.meeting_backend.vo.response.base.AjaxRestResponse;
import com.twport.meeting_backend.vo.response.base.PageResponse;

import lombok.extern.slf4j.Slf4j;

@Controller
@RequestMapping("/meeting/manage")
@Slf4j
public class ManageController {
	
	private final ManageService service;
	
	public ManageController(ManageService service) {
		this.service = service;
	}

	@GetMapping("")
	public String init(Model model) {
		model.addAttribute("options", service.getSelectOption());
		return "manage";
	}
	
	@PostMapping("/query")
	public @ResponseBody AjaxRestResponse<PageResponse<ManageQueryResponse>> pageQuery(@RequestBody ManageQueryRequest request) {
		return AjaxRestResponse.rest(service.query(request), ResponseStatusCode.SEARCH_SUCCESS);
	}
	
	@PostMapping("/add")
	public @ResponseBody AjaxRestResponse<ManageAddResponse> add(@RequestBody ManageAddRequest request) {
		return AjaxRestResponse.rest(service.add(request), ResponseStatusCode.INSERT);
	}
	
	@PostMapping("/delete")
	public @ResponseBody AjaxRestResponse<ManageAddResponse> delete(@RequestBody ManageDeleteRequest request) {
		service.delete(request);
		return AjaxRestResponse.rest(ResponseStatusCode.DELETE);
	} 

	@PostMapping("/update")
	public @ResponseBody AjaxRestResponse<Void> update(@RequestBody ManageUpdateRequest request) {
		service.update(request);
		return AjaxRestResponse.rest(ResponseStatusCode.UPDATE);
	}
}
