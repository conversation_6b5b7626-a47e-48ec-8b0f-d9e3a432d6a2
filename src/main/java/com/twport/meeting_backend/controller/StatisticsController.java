package com.twport.meeting_backend.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.service.StatisticsService;
import com.twport.meeting_backend.vo.request.StatisticsQueryRequest;
import com.twport.meeting_backend.vo.response.StatisticsChartResponse;
import com.twport.meeting_backend.vo.response.StatisticsQueryPageModel;
import com.twport.meeting_backend.vo.response.base.AjaxRestResponse;
import com.twport.meeting_backend.vo.response.base.PageResponse;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/meeting/statistics")
@Slf4j
public class StatisticsController {
	private final StatisticsService statisticsService;

	public StatisticsController(StatisticsService statisticsService) {
		this.statisticsService = statisticsService;
	}

	@GetMapping("")
	public ModelAndView init() {
		ModelAndView mv = new ModelAndView("statistics");
		mv.addObject("resp", statisticsService.getReserveData());
		mv.addObject("select", statisticsService.getAllDefaultSelect());
		return mv;
	}

	@PostMapping("/query")
	public AjaxRestResponse<PageResponse<StatisticsQueryPageModel>> query(@RequestBody StatisticsQueryRequest request) 
	{
		return AjaxRestResponse.rest(statisticsService.getRecordTableData(request), ResponseStatusCode.SEARCH_SUCCESS);
	}

	@GetMapping("/echart")
	public  StatisticsChartResponse getReserveData(){
		StatisticsChartResponse chartDatas = statisticsService.getReserveData();
		log.info("chartDatas : {}", chartDatas);
		return chartDatas;
	}

	/**
	 * 匯出excel
	 */
	@PostMapping("/exportXlsx")
	public void exportXlsx(HttpServletResponse response) {
		statisticsService.exportXlsx(response);
	}
}
