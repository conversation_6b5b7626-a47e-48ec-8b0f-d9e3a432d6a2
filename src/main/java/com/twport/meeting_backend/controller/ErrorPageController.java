package com.twport.meeting_backend.controller;

import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年7月26日 
 * @class ErrorPageController
 * @description 錯誤頁面處理
 */

@Controller
@Slf4j
public class ErrorPageController implements ErrorController {
	
	@RequestMapping("/error")
	public ModelAndView handleErrorPage(HttpServletRequest request) {
		ModelAndView mv = new ModelAndView("error/error");
		Object statusCode = request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE);
		log.info("error request status code : {}", statusCode);
		
		HttpSession session = request.getSession();
		if(statusCode != null) {
			Integer code = Integer.valueOf(statusCode.toString());
						
			if(code == HttpStatus.UNAUTHORIZED.value()) { //401
				mv.addObject("message", "登入身份無效，請重新登入(3秒後自動執行)");
				mv.addObject("redirectUrl", session.getAttribute("redirectUrl"));
			}else if(code == HttpStatus.FORBIDDEN.value()) { //403
				mv.addObject("message", "您無權限瀏覽此網址");
			}else if(code == HttpStatus.NOT_FOUND.value()) { //404
				mv.addObject("message", "找不到該網址，請聯繫管理員");
			}else if(code == HttpStatus.REQUEST_TIMEOUT.value()) { //408
				mv.addObject("message", "網址請求連線超時，請稍候再試");
			}else if(code == HttpStatus.INTERNAL_SERVER_ERROR.value()) { //500
				mv.addObject("message", "網站發生未預期錯誤，請稍後再試");
			}
		}else {
			mv.addObject("message", "網站發生錯誤，請稍後再試");
		}
		
		return mv;
	}
}
