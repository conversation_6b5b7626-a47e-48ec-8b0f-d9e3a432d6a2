package com.twport.meeting_backend.controller;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.boot.actuate.health.HealthEndpoint;
import org.springframework.boot.actuate.health.Status;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.query.LdapQuery;
import org.springframework.ldap.query.LdapQueryBuilder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.entity.CompanyList;
import com.twport.meeting_backend.entity.DepartmentList;
import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.entity.MeetingReserveDepartment;
import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.entity.ldap.AdUserNoMatch;
import com.twport.meeting_backend.entity.ldap.Person;
import com.twport.meeting_backend.enums.MeetingMode;
import com.twport.meeting_backend.enums.RecordStatus;
import com.twport.meeting_backend.enums.SituationMode;
import com.twport.meeting_backend.repository.AppUserRepository;
import com.twport.meeting_backend.repository.CompanyListRepository;
import com.twport.meeting_backend.repository.DepartmentListRepository;
import com.twport.meeting_backend.repository.MeetingReserveDepartmentRepository;
import com.twport.meeting_backend.repository.MeetingReserveRepository;
import com.twport.meeting_backend.repository.MeetingRoomRepository;
import com.twport.meeting_backend.repository.OfficeListRepository;
import com.twport.meeting_backend.repository.PortListRepository;
import com.twport.meeting_backend.repository.ldap.AdUserNoMatchRepository;
import com.twport.meeting_backend.repository.ldap.LdapPersonRepository;
import com.twport.meeting_backend.vo.response.HeartbeatResponse;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年7月22日 
 * @class ApiController
 * @description api接口
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class ApiController {
	
	private final AppUserRepository appUserRepository;
	
	private final MeetingRoomRepository meetingRoomRepository;
	
	private final MeetingReserveRepository meetingReserveRepository;
	
	private final MeetingReserveDepartmentRepository meetingReserveDepartmentRepository;
	
	private final DepartmentListRepository departmentListRepository;
	
	private final CompanyListRepository companyListRepository;
	
	private final AdUserNoMatchRepository adUserNoMathRepository;

	private final HealthEndpoint healthEndpoint;
	
	private final HttpServletResponse resp;
	
	private final LdapPersonRepository personRepository;
	
	public ApiController(
			AppUserRepository appUserRepository,
			MeetingRoomRepository meetingRoomRepository,
			MeetingReserveRepository meetingReserveRepository,
			MeetingReserveDepartmentRepository meetingReserveDepartmentRepository,
			DepartmentListRepository departmentListRepository,
			PortListRepository portListRepository,
			OfficeListRepository officeListRepository,
			CompanyListRepository companyListRepository,
			AdUserNoMatchRepository adUserNoMathRepository,
			LdapPersonRepository personRepository,
			PasswordEncoder passwordEncoder,
			HealthEndpoint healthEndpoint,
			HttpServletResponse resp,
			LdapTemplate ldapTemplate) {
		this.appUserRepository = appUserRepository;
		this.meetingRoomRepository = meetingRoomRepository;
		this.meetingReserveRepository = meetingReserveRepository;
		this.meetingReserveDepartmentRepository = meetingReserveDepartmentRepository;
		this.departmentListRepository = departmentListRepository;
		this.companyListRepository = companyListRepository;
		this.adUserNoMathRepository = adUserNoMathRepository;
		this.personRepository = personRepository;
		this.healthEndpoint = healthEndpoint;
		this.resp = resp;
	}
	
	@GetMapping("/addTestUser")
	public String addAppUser() {		
		DepartmentList depart = new DepartmentList();
		depart.setName("系統開發部");
		depart.setCompanyId(UUID.randomUUID().toString());
		DepartmentList createDepart = departmentListRepository.save(depart);
		
		AppUser user = new AppUser();
		user.setAccount("FETMeet");
		user.setCompanyId(createDepart.getCompanyId());
		user.setDepartmentId(createDepart.getId());
		user.setFullName("遠傳測試帳號");
		user.setRole("ROLE_USER");
		appUserRepository.save(user);
		
		return "success";
	}
	
	@GetMapping("/addMeetingRoom")
	public String addMeetingRoom() {
		MeetingRoom room = new MeetingRoom();
		room.setRoomName("801會議室");
		room.setFloor(8);
		room.setPortId("KLP");
		room.setOfficeId("LHB");
		room.setOnlineMeeting(true);
		room.setEnvDevice(true);
		
		MeetingRoom createRoom = meetingRoomRepository.save(room);
		createRoom.setRoomCode("KHPKHC" + createRoom.getId());
		meetingRoomRepository.save(createRoom);
		
		return "success";
	}
	
	@GetMapping("/addMeetingReserve")
	public String addMeetingReserve() {
		MeetingReserve reserve = new MeetingReserve();	
		reserve.setReserveId("7a706ec0-286e-4921-8f3f-2352d46843a2");
		reserve.setHostId("14246f26-cd06-40cb-88b7-10e97e6b5761");
		
		reserve.setRoomId("0001");
		reserve.setContactId("abcd");
		reserve.setReserveDateStart(LocalDateTime.now());
		reserve.setReserveDateEnd(LocalDateTime.now());
		reserve.setDepartmentId("1");
		reserve.setTheme("第二次需求訪談會議");
		reserve.setStatus(RecordStatus.RESERVE);
		reserve.setMembers(10);
		reserve.setMeetingMode(MeetingMode.NORMAL);
		reserve.setSituationMode(SituationMode.NORMAL);
		
		MeetingReserve reserveCreate = meetingReserveRepository.save(reserve);
		log.info("reserve info : {}", reserveCreate);
		
		String[] depart = new String[] {"研發部", "企劃部"};
		for(int i = 0;i < 2;i++) {
			MeetingReserveDepartment dep = new MeetingReserveDepartment();
			dep.setReserveId(reserveCreate.getId());
			dep.setDepartmentId(Long.valueOf(i + 1));
			dep.setDepartmentName(depart[i]);
			meetingReserveDepartmentRepository.save(dep);
		}
		
		return "success";
	}
	
	@GetMapping("/v1/heartbeat")
	public HeartbeatResponse heartbeat() {
		HeartbeatResponse response = new HeartbeatResponse();
		Status heartStatus = healthEndpoint.health().getStatus();
		Instant instant = Instant.now().plusMillis(TimeUnit.HOURS.toMillis(8));
		String timestamp = DateTimeFormatter.ISO_INSTANT.format(instant);
		if (Status.UP.equals(heartStatus)) {
			response.setStatus("OK");
			response.setTimestamp(timestamp);
			response.setMessage("Service is running");
		} else {
			resp.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
			response.setStatus("ERROR");
			response.setTimestamp(timestamp);
			response.setMessage("Service is not available");
		}

		return response;
	}
	
	@GetMapping("/testldap/findall")
	public void ldapFindAllTest() {
		LdapQuery query = LdapQueryBuilder.query().where("initials").isPresent().and("userAccountControl").is("512");
		List<Person> ldapUserList = personRepository.findAll(query).stream().map(person -> {
			Person newPerson = person;
			List<String> dnList = new ArrayList<>();
			dnList = Collections.list(newPerson.getDn().getAll());
			
			List<String> fliterDnList = new ArrayList<>();
			Iterator<String> dnIterator = dnList.iterator();
			while(dnIterator.hasNext()) {
				String dn = dnIterator.next();
				if(!dn.contains("CN")) {
					fliterDnList.add(dn);
				}
			}
			
			newPerson.setDnList(fliterDnList);
			return newPerson;
		}).collect(Collectors.toList());
		
		Map<String, Map<String, List<Person>>> companyMap = new LinkedHashMap<>();
		companyMap.put("總公司", new LinkedHashMap<>());
		companyMap.put("基隆分公司", new LinkedHashMap<>());
		companyMap.put("台中分公司", new LinkedHashMap<>());
		companyMap.put("高雄分公司", new LinkedHashMap<>());
		companyMap.put("花蓮分公司", new LinkedHashMap<>());
		String ouBranchName = "OU=各分公司總經理室";
		List<String> ouBranchLocateList = new ArrayList<>();
		ouBranchLocateList.add("OU=基隆");
		ouBranchLocateList.add("OU=台中");
		ouBranchLocateList.add("OU=高雄");
		ouBranchLocateList.add("OU=花蓮");
		String ouDepartmentKeyword = "處";
		String ouManagerKeyword = "室";
		String ouCommitteeKeyword = "委員會";
		
		//整理AD使用者資訊結構
		// 第一層map key : 公司名稱
		// 第一層map value : 以單位分類好的使用者資訊Map
		// 第二層map key : 單位名稱
		// 第二層map value : 單位下的使用者資訊
		ldapUserList.forEach(person -> {
			//分公司員工
			if(person.getDnList().contains(ouBranchName)) {
				List<String> filterDnList = new ArrayList<>();
				Iterator<String> dnIterator = person.getDnList().iterator();
				while(dnIterator.hasNext()) {
					String dn = dnIterator.next();
					if(!dn.equals(ouBranchName)) {
						filterDnList.add(dn);
					}
				}
				
				ouBranchLocateList.forEach(locate -> {
					if(filterDnList.contains(locate)) {
						String companyKey = null;
						
						switch (locate) {
							case "OU=基隆":
								companyKey = "基隆分公司";
								break;
							case "OU=台中":
								companyKey = "台中分公司";
								break;
							case "OU=高雄":
								companyKey = "高雄分公司";
								break;
							case "OU=花蓮":
								companyKey = "花蓮分公司";
								break;
							default:
								throw new IllegalArgumentException("Unexpected value: " + locate);
						}
						
						Map<String, List<Person>> departmentMap = companyMap.get(companyKey);
						
						String departmentName = null;
						for(String department: filterDnList) {
							if(department.contains(ouDepartmentKeyword) || 
									department.contains(ouManagerKeyword) || 
									department.contains(ouCommitteeKeyword)) {
								departmentName = department.replace("OU=", "");
								break;
							}
						}
						
						List<Person> personList = new ArrayList<>();
						if(departmentMap.containsKey(departmentName)) {
							personList = departmentMap.get(departmentName);
						}
						
						person.setDnList(filterDnList);
						personList.add(person);
						departmentMap.put(departmentName, personList);
						
						companyMap.put(companyKey, departmentMap);
					}
				});
			}else {
				Map<String, List<Person>> departmentMap = companyMap.get("總公司");
				String departmentName = null;
				for(String department: person.getDnList()) {
					if(department.contains(ouDepartmentKeyword) || 
							department.contains(ouManagerKeyword) || 
							department.contains(ouCommitteeKeyword)) {
						departmentName = department.replace("OU=", "");
						break;
					}
				}
								
				List<Person> personList = new ArrayList<>();
				if(departmentMap.containsKey(departmentName)) {
					personList = departmentMap.get(departmentName);
				}
				
				personList.add(person);
				departmentMap.put(departmentName, personList);
				
				companyMap.put("總公司", departmentMap);
			}
		});
		
		for(Entry<String, Map<String, List<Person>>> company: companyMap.entrySet()) {
			log.info("company key : {}", company.getKey());	
			CompanyList companyList = getCompanyList(company.getKey());
			
			for(Entry<String, List<Person>> depart : company.getValue().entrySet()) {
				if(depart.getKey() == null) {
					saveAdUserNoMatch(depart.getValue());
				}else {
					log.info("department key : {}", depart.getKey());					
					DepartmentList departmentList = getDepartmentList(depart.getKey(), companyList.getId());
					
					createOrSaveAppUser(depart.getValue(), companyList.getId(), departmentList.getId());
				}
			}
		}
	}
	
	@GetMapping("/testldap/findone")
	public void ldapFindOneTest(@RequestParam(name = "account") String account) {
		LdapQuery query = LdapQueryBuilder.query().where("sAMAccountName").is(account);
		Optional<Person> personOpt = personRepository.findOne(query);
		
		if(personOpt.isPresent()) {
			log.info("single person : {}", personOpt.get());
		}else {
			log.info("not find person");
		}
	}
	
	@Transactional
	private CompanyList getCompanyList(String companyName) {
		Optional<CompanyList> companyListOpt = companyListRepository.findByName(companyName);
		CompanyList companyList = null;
		
		if(companyListOpt.isPresent()) {
			companyList = companyListOpt.get();
		}else {
			CompanyList createCompany = new CompanyList();
			createCompany.setName(companyName);
			companyList = companyListRepository.save(createCompany);
		}
		
		return companyList;
	}
	
	@Transactional
	private DepartmentList getDepartmentList(String departmentName, String companyId) {
		Optional<DepartmentList> departmentOpt = departmentListRepository.findByNameAndCompanyId(departmentName, companyId);
		
		DepartmentList departmentList = null;
		
		if(departmentOpt.isPresent()) {
			departmentList = departmentOpt.get();
		}else {
			DepartmentList createDepart = new DepartmentList();
			createDepart.setName(departmentName);
			createDepart.setCompanyId(companyId);
			departmentList = departmentListRepository.save(createDepart);
		}
		
		return departmentList;
	}
	
	@Transactional
	private void createOrSaveAppUser(List<Person> personList, String companyId, String departmentId) {
		for(Person person : personList) {
			Optional<AppUser> userOpt = appUserRepository.findByJobNum(person.getJobNumber());
			
			AppUser user = null;
			if(userOpt.isPresent()) {
				user = userOpt.get();
			}else {
				user = new AppUser();
			}
			
			user.setAccount(person.getSAMAccountName());
			user.setCompanyId(companyId);
			user.setDepartmentId(departmentId);
			user.setEmail(person.getEmail());
			user.setFirstName(person.getFirstName());
			user.setLastName(person.getLastName());
			user.setFullName(person.getName());
			user.setJobNum(person.getJobNumber());
			user.setJonTitle(person.getJobTitle());
			user.setPhone(person.getPhone());
			user.setRole("ROLE_USER");
			
			appUserRepository.save(user);
			
			log.info("user info : {}", user);
		}
	}
	
	@Transactional
	private void saveAdUserNoMatch(List<Person> noMatchUserList) {
		noMatchUserList.forEach(person -> {
			log.info("not match person info : {}", person);
			AdUserNoMatch adUser = new AdUserNoMatch();
			adUser.setSAMAccountName(person.getSAMAccountName());
			adUser.setCommonName(person.getCommonName());
			adUser.setJobNumber(person.getJobNumber());
			adUser.setCompany(person.getCompany());
			adUser.setDepartment(person.getDepartment());
			adUser.setFilterDnList(person.getDnList());
			adUserNoMathRepository.save(adUser);
		});
	}
}
