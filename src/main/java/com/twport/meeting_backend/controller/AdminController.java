package com.twport.meeting_backend.controller;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.twport.meeting_backend.service.AdminService;


@Controller
public class AdminController {
	
	private final AdminService adminService;
	
	public AdminController(AdminService adminService) {
		this.adminService = adminService;
	}

	@GetMapping("/admin/login")
	public String adminLogin(Model model, @RequestParam(value = "token", required = false) String token) {
		
		// 沒Token參數導向SSO登入畫面
		if (StringUtils.isBlank(token)) {
			return getSsoUrl();
		}
		
		// 驗證Token
		String account = adminService.validateAuth(token);
		if (StringUtils.isBlank(account)) {
			// 驗證錯誤導向SSO登入畫面
			return getSsoUrl();
		}
		
		model.addAttribute("account", account);
		
		return "index-maintenance.html";
	}
	
	private String getSsoUrl() {
		String formatterSsoUrl = adminService.getSsoUrl();
		
		// TODO 帶詢問機關SSO都打不通該如何處理
		if (StringUtils.isBlank(formatterSsoUrl)) {
			return "404";
		}
		
		return "redirect:" + formatterSsoUrl;
	} 
	
}
