package com.twport.meeting_backend.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.vo.response.base.AjaxRestResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 測試 AOP 功能的 Controller
 * 
 * <AUTHOR>
 * @datetime 2024年12月19日
 * @class TestAopController
 * @description 用於測試 AOP 監控功能是否正常運作
 */
@RestController
@RequestMapping("/test/aop")
@Slf4j
public class TestAopController {

    /**
     * 測試 GET 請求
     */
    @GetMapping("/get")
    public AjaxRestResponse<String> testGet(@RequestParam(required = false) String message) {
        log.info("執行 testGet 方法，參數: {}", message);
        return AjaxRestResponse.rest("GET 請求成功，參數: " + message, ResponseStatusCode.SEARCH_SUCCESS);
    }

    /**
     * 測試 POST 請求
     */
    @PostMapping("/post")
    public AjaxRestResponse<String> testPost(@RequestBody(required = false) String body) {
        log.info("執行 testPost 方法，請求體: {}", body);
        return AjaxRestResponse.rest("POST 請求成功，請求體: " + body, ResponseStatusCode.INSERT);
    }

    /**
     * 測試異常情況
     */
    @GetMapping("/error")
    public AjaxRestResponse<String> testError() {
        log.info("執行 testError 方法");
        throw new RuntimeException("這是一個測試異常");
    }

    /**
     * 測試長時間執行的方法
     */
    @GetMapping("/slow")
    public AjaxRestResponse<String> testSlow() throws InterruptedException {
        log.info("執行 testSlow 方法");
        Thread.sleep(2000); // 模擬長時間執行
        return AjaxRestResponse.rest("慢速請求完成", ResponseStatusCode.SEARCH_SUCCESS);
    }
}
