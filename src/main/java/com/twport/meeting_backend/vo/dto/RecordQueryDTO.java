package com.twport.meeting_backend.vo.dto;

import java.time.LocalDateTime;

import com.twport.meeting_backend.enums.RecordStatus;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RecordQueryDTO {

	/**
	 * 會議主題
	 */
	private String theme;
	
	/**
	 * 會議室名稱
	 */
	private String roomName;

	/**
	 * 樓層
	 */
	private Integer floor;
	
	/**
	 * 主持人(姓氏)
	 */
	private String hostFirstName;
	
	/**
	 * 主持人(名字)
	 */
	private String hostLastName;
	
	private String hostFullName;
	
	/**
	 * 預約人(姓氏)
	 */
	private String reserveFirstName;
	
	/**
	 * 預約人(名字)
	 */
	private String reserveLastName;
	
	private String reserveFullName;
	
	/**
	 * 預約人電話
	 */
	private String reservePhoneNumber;
	
	/**
	 * 會議預約人Id
	 */
	private String reserveId;
	
	/**
	 * 預約單位
	 */
	private String department;
	
	/**
	 * 預約會議室時間 - 起
	 */
	private LocalDateTime reserveDateStart;
	
	/**
	 * 預約會議室時間 - 迄
	 */
	private LocalDateTime reserveDateEnd;
	
	/**
	 * 使用狀態
	 */
	private RecordStatus status;
	
	/**
	 * 是否公開
	 */
	private boolean personal;

    /**
     * 會議流水號id
     */
    private Long meetingId;
	
}
