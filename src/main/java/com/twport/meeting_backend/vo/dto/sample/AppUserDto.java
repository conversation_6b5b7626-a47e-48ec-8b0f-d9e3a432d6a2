package com.twport.meeting_backend.vo.dto.sample;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AppUserDto {
	@NotBlank(message = "帳號不可為空")
	private String username;
	
	@NotBlank(message = "密碼不可為空")
	private String password;
	
	@NotBlank(message = "姓名不可為空")
	private String name;
	
//	@NotBlank(message = "港別不可為空")
//	private String port;
//	
//	@NotBlank(message = "辦公室別不可為空")
//	private String office;
	
	@NotBlank(message = "所屬公司不可為空")
	private String company;
	
	@NotBlank(message = "所屬單位不可為空")
	private String department;
}
