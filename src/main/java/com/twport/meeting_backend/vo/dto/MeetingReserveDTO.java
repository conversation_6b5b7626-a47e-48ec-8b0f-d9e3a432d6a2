package com.twport.meeting_backend.vo.dto;

import java.time.LocalDateTime;

import com.twport.meeting_backend.enums.RecordStatus;

public interface MeetingReserveDTO {

    // 會議室
    String getRoom();

    // 會議主題
    String getTheme();

    // 會議開始時間
    LocalDateTime getReserveStart();

    // 會議結束時間
    LocalDateTime getReserveEnd();

    // 主持人
    String getHostName();

    // 聯絡人
    String getContactName();

    // 聯絡電話
    String getContactPhone();

    // 會議預約狀態
    RecordStatus getStatus();

}
