package com.twport.meeting_backend.vo.dto;

import com.twport.meeting_backend.entity.AppUser;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserProfile {
	
	private String id;
	private String account;
	private String name;
	private String companyId;
	private String departmentId;
	private String phone;
	
	public UserProfile(AppUser user) {
		this.id = user.getId();
		this.account = user.getAccount();
		this.name = user.getFullName();
		this.companyId = user.getCompanyId();
		this.departmentId = user.getDepartmentId();
		this.phone = user.getPhone();
	}
	
}
