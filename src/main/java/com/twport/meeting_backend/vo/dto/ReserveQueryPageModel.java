package com.twport.meeting_backend.vo.dto;

import com.twport.meeting_backend.enums.MeetingMode;
import com.twport.meeting_backend.enums.SituationMode;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ReserveQueryPageModel {

	/**
	 * 流水號ID
	 */
	private Long meetingId;
	
	/**
	 * 會議主題
	 */
	private String theme;
	
	/**
	 * 會議預約人
	 */
	private String reserve;

	/**
	 * 會議預約人Id
	 */
	private String reserveId;

	/**
	 * 聯絡人id
	 */
	private String contactId;
	
	private String contactName;
	
	private String contactPhone;

	/**
	 * 會議室Id
	 */
	private String roomId;
	
	/**
	 * 會議室名稱
	 */
	private String roomName;
	
	/**
	 * 預約日期
	 */
	private String reserveDate;
	
	/**
	 * 預約時間 - 起
	 */
	private String reserveStartTime;
	
	/**
	 * 預約時間 - 迄
	 */
	private String reserveEndTime;

	/**
	 * 是否擁有預約
	 */
	private String own;

	/**
	 * 是否公開
	 */
	private boolean personal;

	/**
	 * 單位
	 */
//	private String department;

	/**
	 * 與會人數
	 */
	private Integer members;

	/**
	 * 會議模式(NORMAL: 一般會議、ONLINE: 線上會議、MIX: 混合會議)
	 */
	private MeetingMode meetingMode;

	/**
	 * 會議情境(NORMAL: 一般會議、PPT: 簡報會議、VIDEO: 視訊會議)
	 */
	private SituationMode situationMode;


	/**
	 * 與會單位id
	 */
	private Long departmentId;

	/**
	 * 與會單位名稱
	 */
	private String departmentName;

	/**
	 * 預約單位id
	 */
	private Long reserveDeptId;

	/**
	 * 線上會議連結
	 */
	private String url;
}
