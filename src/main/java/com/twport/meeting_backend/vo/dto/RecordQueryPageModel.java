package com.twport.meeting_backend.vo.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RecordQueryPageModel {
	
	/**
	 * 會議流水號id
	 */
	private Long meetingId;

	/**
	 * 會議主題
	 */
	private String theme;

	/**
	 * 會議室
	 */
	private String roomNameDisplay;
	
	/**
	 * 主持人
	 */
	private String hostUser;
	
	/**
	 * 預約單位
	 */
	private String department;
	
	/**
	 * 預約人
	 */
	private String reserveUser;
	
	/**
	 * 預約人電話
	 */
	private String reserveUserPhone;
	
	/**
	 * 會議預約人Id
	 */
	private String reserveId;
	
	/**
	 * 預約日期
	 */
	private String reserveDate;
	
	/**
	 * 預約時段
	 */
	private String reserveTime;
	
	/**
	 * 使用狀態
	 */
	private String statusDisplay;
	
}
