package com.twport.meeting_backend.vo.dto;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class MeetingReserveNowQueryDto {
	
	private String roomName;
	
	private String hostName;
	
//	private String hostLastName;
	
	private String reserveId;
	
	private String theme;
	
	/**
	 * 預約會議室時間 - 起
	 */
	private LocalDateTime reserveDateStart;

	/**
	 * 預約會議室時間 - 迄
	 */
	private LocalDateTime reserveDateEnd;
	
	private Long members;
	private Boolean personal;
	
	private String contactName;
	
	private String contactPhone;
}
