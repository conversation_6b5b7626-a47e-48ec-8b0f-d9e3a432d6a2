package com.twport.meeting_backend.vo.dto;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 下拉選單選項
 * <AUTHOR>
 * @datetime 2024年7月23日 
 * @class SelectOption
 * @description
 */
@Getter
@Setter
@ToString
public class SelectOption {
		
	/**
	 * 下拉選單值
	 */
	private String value;
	
	private String secValue;
	
	private String thrValue;
		
	/**
	 * 下拉選單文字
	 */
	private String label;
	
	/**
	 * 游標提示文字
	 */
	private String tooltipTitle;
	
	private Boolean disabled;
	
	/**
	 * 連動選單
	 */
	private List<SelectOption> subOptions = new ArrayList<>();
	
	public SelectOption(String value, String label) {
		this.value = value;
		this.label = label;
	}
}
