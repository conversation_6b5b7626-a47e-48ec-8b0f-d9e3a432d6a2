package com.twport.meeting_backend.vo.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

import com.twport.meeting_backend.enums.RecordStatus;

/**
 *
 * <AUTHOR>
 * @datetime 2024年7月31日
 * @class ReserveQueryDTO
 * @description 會議室預約DTO
 */
@Getter
@Setter
@ToString
public class ReserveQueryDTO {
	
	private String meetingId;
	
	/**
	 * 預約人id
	 */
	private String reserveId;
	
	/**
	 * 姓氏(預約人)
	 */
	private String reserveFirstName;

	/**
	 * 名字(預約人)
	 */
	private String reserveLastName;
	
	private String reserveFullName;
	
	private String contactId;
	
	private String contactName;
	
	private String contactPhone;

	/**
	 * 預約會議室ID
	 */
	private String roomId;

	/**
	 * 預約會議室時間 - 起
	 */
	private LocalDateTime reserveDateStart;

	/**
	 * 預約會議室時間 - 迄
	 */
	private LocalDateTime reserveDateEnd;

	/**
	 * CANCEL、RESERVE、END
	 */
	private RecordStatus status;

	/**
	 * 會議室主題
	 */
	private String theme;

	/**
	 * 單位名稱
	 */
	private String departmentName;

	/**
	 * 是否公開
	 */
	private boolean personal;

	/**
	 * 會議室名稱
	 */
	private String roomName;

	/**
	 * 樓層
	 */
	private Integer floor;

}
