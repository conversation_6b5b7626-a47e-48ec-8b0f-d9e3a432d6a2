package com.twport.meeting_backend.vo.request;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.twport.meeting_backend.enums.MeetingMode;
import com.twport.meeting_backend.enums.SituationMode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @datetime 2024年7月30日
 * @class ReserveAddRequest
 * @description api Request
 */
@Getter
@Setter
@ToString
public class ReserveAddRequest {
	
	/**
	 * 會議室代號
	 */
	@NotEmpty(message = "會議室不可為空")
    private String roomId;
	
	/**
	 * 會議室樓層
	 */
    private String floor;

    /**
     * 主持人id
     */
    private String hostId;
    
    @NotBlank(message = "主持人不可為空")
    private String hostName;
    
    /**
     * 聯絡人id
     */
    private String contactId;
    
    @NotBlank(message = "聯絡人資訊都不可為空")
    private String contactName;
    
    @NotBlank(message = "聯絡人資訊都不可為空")
    private String contactPhone;

    /**
     * 預約單位id
     */
    private String reserveDeptId;
    
    @NotBlank(message = "預約單位不可為空")
    private String reserveDeptName;

    /**
     * 會議主題
     */
    @NotBlank(message = "會議主題不可為空")
    private String theme;
    
    /**
     * 與會單位 ID
     */
    @NotEmpty(message = "與會單位至少要有一個")
    private List<String> attendDept;
    
    /**
     * 與會單位 名稱
     */
    @NotEmpty(message = "與會單位至少要有一個")
    private List<String> nameValue;
    
    @NotNull(message = "會議模式不可為空")
    private MeetingMode meetingMode;
    
    @NotNull(message = "情境模式不可為空")
    private SituationMode situationMode;

    /**
     * 預估人數
     */
    @NotNull(message = "預估人數不可為空")
    @Positive(message = "預估人數必須為正整數")
    private Integer members;

    /**
     * 是否公開
     */
    @NotNull(message = "必須選擇會議是否公開")
    private Boolean personal;
    
    /**
     * 預約-起
     */
    @NotNull(message = "會議開始時間不可為空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    /**
     * 預約-迄
     */
    @NotNull(message = "會議結束時間不可為空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 週期性預約-起
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cycleBeginTime;

    /**
     * 週期性預約-迄
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cycleEndTime;

    /**
     * 線上會議連結
     */
    private String url;
    
    /**
     * 預約型態(daily、week、month)
     */
    private String type;
    
    /**
     * 星期幾
     */
    private String dayOfWeek;
    
    /**
     * 第幾周
     */
    private String weekOfMonth;
}
