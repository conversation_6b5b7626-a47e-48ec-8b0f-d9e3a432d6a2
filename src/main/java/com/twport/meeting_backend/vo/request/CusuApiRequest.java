/**
 * 
 */
package com.twport.meeting_backend.vo.request;


import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @datetime 2024年11月22日 
 * @class CusuApiRequest
 * @description 
 */

@Getter
@Setter
@ToString
public class CusuApiRequest {

	@JsonProperty(value = "TriggeredTime")
	String triggeredTime;
	
	@JsonProperty(value = "CardCount")
	int cardCount;
	
	@JsonProperty(value = "DeviceID")
	String deviceID;
	
	@JsonProperty(value = "ExtensionNumber")
	String extensionNumber;
	
	@JsonProperty(value = "Token")
	String token;
	
}
