package com.twport.meeting_backend.vo.request;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ReserveQueryDayReserveRequest {
	private List<String> floor;

	private List<String> room;

	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate date;
	
	@JsonFormat(pattern = "HH:mm:ss")
	private LocalTime startTime;
	
	@JsonFormat(pattern = "HH:mm:ss")
	private LocalTime endTime;
}
