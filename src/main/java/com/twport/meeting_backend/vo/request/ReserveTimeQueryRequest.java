package com.twport.meeting_backend.vo.request;

import java.time.LocalDate;
import java.time.LocalTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ReserveTimeQueryRequest {
	private String queryType;
	
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate date;
	
	@JsonFormat(pattern = "HH:mm:ss")
	private LocalTime startTime;
	
	@JsonFormat(pattern = "HH:mm:ss")
	private LocalTime endTime;
}
