package com.twport.meeting_backend.vo.request;

import java.time.LocalDate;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @datetime 2024/07/31
 * @class ReserveQueryRequest
 * @description
 */
@Getter
@Setter
@ToString
public class ReserveQueryRequest {

    private List<String> floor;

    private List<String> room;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;

    private String type;

}
