package com.twport.meeting_backend.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.twport.meeting_backend.enums.MeetingMode;
import com.twport.meeting_backend.enums.SituationMode;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2024年7月30日
 * @class ReserveUpdateRequest
 * @description Api UpdateRequest
 */
@Getter
@Setter
@ToString
public class ReserveUpdateRequest {
    /**
     * 會議流水代號
     */
    private String meetingId;

    /**
	 * 會議室代號
	 */
    private String roomId;

    /**
     * 主持人id
     */
    private String hostId;
    
    private String hostName;
    
    /**
     * 聯絡人id
     */
    private String contactId;
    
    private String contactName;
    
    private String contactPhone;

    /**
     * 預約單位id
     */
    private String reserveDeptId;
    
    private String reserveDeptName;

    /**
     * 會議主題
     */
    private String theme;
    
    /**
     * 與會單位序號
     */
    private List<String> attendDept;

    /**
     * 與會單位名稱
     */
    private List<String> nameValue;
    
    private MeetingMode meetingMode;
    
    private SituationMode situationMode;

    /**
     * 預估人數
     */
    private Integer members;

    /**
     * 是否公開
     */
    private Boolean personal;
    
    /**
     * 預約-起
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    /**
     * 預約-迄
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 線上會議連結
     */
    private String url;
}
