package com.twport.meeting_backend.vo.response;

import java.util.List;

import com.twport.meeting_backend.enums.SignInType;
import com.twport.meeting_backend.vo.dto.SelectOption;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RecordSignedEditInitResponse {
	private List<SelectOption> companyOptions;
	private String signInId;
	private String companyId;
	private String departmentId;
	private String departmentName;
	private String jobTitle;
	private SignInType type;
	private String signInImageUrl;
	private String signInUserName;
}
