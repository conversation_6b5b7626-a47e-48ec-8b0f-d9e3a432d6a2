package com.twport.meeting_backend.vo.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

import com.twport.meeting_backend.vo.dto.SelectOption;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年7月26日
 * @class ReserveInitResponse
 * @description 會議室預約 - 初始化物件
 */
@Getter
@Setter
@ToString
public class ReserveInitResponse {
	
	// 會議室篩選
	private List<SelectOption> meetingRoomSelects;
	
	// 公司下拉選單
	private List<SelectOption> companySelects;
	
	// 與會單位下拉選單
	private List<SelectOption> departmentSelects;
	
	// 會議模式下拉選單
	private List<SelectOption> meetingModeSelects;
	
	// 會議情境下拉選單
	private List<SelectOption> situationModeSelects;
}
