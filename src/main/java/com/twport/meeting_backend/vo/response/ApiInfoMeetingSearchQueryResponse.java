package com.twport.meeting_backend.vo.response;

import java.time.LocalDateTime;

import com.twport.meeting_backend.vo.dto.MeetingReserveDTO;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ApiInfoMeetingSearchQueryResponse {

	// 會議室
	private String room;

	// 會議主題
	private String theme;

	// 會議開始時間
	private LocalDateTime reserveStart;

	// 會議結束時間
	private LocalDateTime reserveEnd;

	// 主持人
	private String hostName;

	// 聯絡人
	private String contactName;

	// 聯絡電話
	private String contactPhone;

	// 會議狀態
	private String status;

	// 配合會議查詢
	public ApiInfoMeetingSearchQueryResponse(MeetingReserveDTO dto) {
		this.room = dto.getRoom();
		this.theme = dto.getTheme();
		this.reserveStart = dto.getReserveStart();
		this.reserveEnd = dto.getReserveEnd();
		this.hostName = dto.getHostName();
		this.contactName = dto.getContactName();
		this.contactPhone = dto.getContactPhone();
		this.status = dto.getStatus().getMessage();
	}

}
