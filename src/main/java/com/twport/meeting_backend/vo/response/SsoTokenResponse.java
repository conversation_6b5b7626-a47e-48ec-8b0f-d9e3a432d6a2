package com.twport.meeting_backend.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年7月22日 
 * @class SsoTokenResponse
 * @description SSO token驗證API接口回傳的資料結構
 */

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SsoTokenResponse {
	// 回傳的參數開頭為大寫，故需加上@JsonProperty識別
	@JsonProperty(value = "AccountInfo")
	private AccountInfo AccountInfo;
	
}
