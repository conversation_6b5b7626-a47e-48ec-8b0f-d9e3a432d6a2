package com.twport.meeting_backend.vo.response;

import java.util.List;
import java.util.Map;

import com.twport.meeting_backend.vo.dto.PdfSignedRecord;
import com.twport.meeting_backend.vo.dto.PdfSignedRecordConfig;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class RecordSignedPdfResponse {
	private String theme;
	private String reserveTime;
	private String reserveLocate;
	private String hostName;
	private Map<PdfSignedRecordConfig, List<PdfSignedRecord>> signRecord;
}
