package com.twport.meeting_backend.vo.response.base;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.twport.meeting_backend.enums.ResponseStatusCode;

import lombok.Getter;
import lombok.ToString;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @datetime 2024年7月26日
 * @class AjaxRestResponse
 * @description 共用AjaxRestResponse
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@ToString
public class AjaxRestResponse<Response> implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("statusCode")
    private String statusCode;

    @JsonProperty("statusMessage")
    private String  message;

    @JsonProperty("result")
    private Response result;
 
    /**
     * 此建構子只供ExceptionHandler使用，
     * 開發者初始化此類別的時候，只可使用另外兩個建構子
     * @param code
     * @param message
     */
    public AjaxRestResponse(String code, String message) {
    	this.statusCode = code;
    	this.message = message;
    }
    
    public AjaxRestResponse(Response result, String code) {
    	this.statusCode = code;
    	this.result = result;
    }
    
    public AjaxRestResponse(ResponseStatusCode statusCode) {
        this.statusCode = statusCode.getCode();
        this.message = statusCode.getMessage();
    }
    
    public AjaxRestResponse(Response result, ResponseStatusCode statusCode) {
        this.statusCode = statusCode.getCode();
        this.message = statusCode.getMessage();
        this.result = result;        
    }
    
    public static <Response> AjaxRestResponse<Response> rest(ResponseStatusCode statusCode) {
        return new AjaxRestResponse<Response>(statusCode);
    }

    public static <Response> AjaxRestResponse<Response> rest(Response result, ResponseStatusCode statusCode) {
        return new AjaxRestResponse<Response>(result, statusCode);
    }
    
    public static <Response> AjaxRestResponse<Response> rest(Response result, String statusCode) {
        return new AjaxRestResponse<Response>(result, statusCode);
    }
}
