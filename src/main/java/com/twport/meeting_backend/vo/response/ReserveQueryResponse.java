package com.twport.meeting_backend.vo.response;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

import com.twport.meeting_backend.vo.dto.ReserveQueryPageModel;
import com.twport.meeting_backend.vo.dto.ReserveTimeDetails;

/**
 * <AUTHOR>
 * @datetime 2024年7月26日
 * @class ReserveQueryResponse
 * @description 會議預約查詢 - 查詢物件
 */
@Getter
@Setter
public class ReserveQueryResponse {
	
	private String queryType;
	
    private List<ReserveQueryPageModel> pageContent;
    
    private Map<String, List<ReserveTimeDetails>> reserveMap;

}
