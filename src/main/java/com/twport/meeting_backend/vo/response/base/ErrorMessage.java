package com.twport.meeting_backend.vo.response.base;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年9月26日 
 * @class ErrorMessage
 * @description 錯誤訊息物件
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ErrorMessage {
	// 錯誤訊息key(可用於對應錯誤訊息的元素標籤id)
	private String errorKey;
	// 錯訊息內容
	private String errorMessage;
}
