package com.twport.meeting_backend.vo.response;

import java.util.List;

import com.twport.meeting_backend.enums.MeetingMode;
import com.twport.meeting_backend.enums.SituationMode;
import com.twport.meeting_backend.vo.dto.SelectOption;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ReserveDetailResponse {
	/**
	 * 流水號ID
	 */
	private String meetingId;
	
	/**
     * 會議類型(ownMeeting : 自己預約的會議，otherMeeting : 他人預約的會議)
     */
    private String reserveType;
	
	/**
	 * 會議主題
	 */
	private String theme;

	/**
	 * 聯絡人id
	 */
	private String contactId;
	
	private String contactName;
	
	private String contactPhone;
	
	private String hostId;
	
	private String hostName;

	/**
	 * 會議室Id
	 */
	private String roomId;
	
	private String floor;
	
	/**
	 * 會議室名稱
	 */
	private String roomName;
	
	/**
	 * 預約日期
	 */
	private String reserveDate;
	
	/**
	 * 預約時間 - 起
	 */
	private String reserveStartTime;
	
	/**
	 * 預約時間 - 迄
	 */
	private String reserveEndTime;

	/**
	 * 是否公開
	 */
	private boolean personal;

	/**
	 * 與會人數
	 */
	private String members;

	/**
	 * 會議模式(NORMAL: 一般會議、ONLINE: 線上會議、MIX: 混合會議)
	 */
	private MeetingMode meetingMode;
	
	private String meetingModeName;

	/**
	 * 會議情境(NORMAL: 一般會議、PPT: 簡報會議、VIDEO: 視訊會議)
	 */
	private SituationMode situationMode;
	
	private String situationModeName;

	private String companyId;
	
	/**
	 * 預約單位id
	 */
	private String departmentId;

	/**
	 * 預約單位名稱
	 */
	private String departmentName;

	/**
	 * 線上會議連結
	 */
	private String url;
	
	/**
	 * QRcode
	 */
	private String qrCode;
	
	/**
	 * 邀請的與會單位id
	 */
	private List<Long> reserveDepartments;
	
	/**
	 * 邀請的與會單位名稱
	 */
	private List<String> reserveDepartmentsName;
	
	/**
	 * 樓層和會議室的連動下拉式選單(只有reserveType = ownMeeting才會有值)
	 */
	private List<SelectOption> floorOptions;
}
