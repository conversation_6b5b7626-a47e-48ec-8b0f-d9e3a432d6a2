package com.twport.meeting_backend.vo.response;

import java.util.List;

import com.twport.meeting_backend.vo.dto.SelectOption;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ApiSignMeetingQueryResponse {
	
	// 會議 ID
	private String reserveId;

	// 使否有會議
	private boolean meeting;

	// 會議室
	private String roomName;

	// 主持人
	private String hostName;

	// 主題
	private String theme;
	
	// 預約日期
	private String reserveDate;
	
	// 預約開始時間
	private String reserveStartTime;
	
	// 預約結束時間
	private String reserveEndTime;
	
	// 與會單位
	private List<SelectOption> departmentsList;
	
	// 成員
	private Long members;
	
	// 簽到次數
	private Long signedCounts;
}
