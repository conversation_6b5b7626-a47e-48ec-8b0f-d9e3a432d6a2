package com.twport.meeting_backend.vo.response.base;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class PageResponse<T> {

	/**
	 * 目前頁面，開始頁數為 0
	 */
	private Integer currentPage;

	/**
	 * 頁面大小
	 */
	private Integer pageSize;

	/**
	 * 資料數量
	 */
	private Integer totalSize;

	/**
	 * 總頁數
	 */
	private Integer totalPage;

	/**
	 * 當前頁數資料
	 */
	private List<T> contents;

	public Integer getNextPageIndex() {
		if (getIsLastPage()) {
			return currentPage;
		}
		return currentPage + 1;
	}

	public Integer getPreviousPageIndex() {
		if (getIsFirstPage()) {
			return 0;
		}
		return currentPage - 1;
	}

	public Boolean getIsFirstPage() {
		return currentPage <= 0;
	}

	public Boolean getIsLastPage() {
		return currentPage + 1 >= getTotalPage();
	}

}
