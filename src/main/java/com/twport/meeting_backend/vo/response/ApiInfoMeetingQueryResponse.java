package com.twport.meeting_backend.vo.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ApiInfoMeetingQueryResponse {
	
	private String reserveId;

	private boolean meeting;

	private String roomName;
	
	private String hostName;
	
	private String theme;
	
	/**
	 * 預約日期
	 */
	private String reserveDate;
	
	/**
	 * 預約開始時間
	 */
	private String reserveStartTime;
	
	/**
	 * 預約結束時間
	 */
	private String reserveEndTime;
	
	private Boolean personal;
	
	private String contactName;
	
	private String contactPhone;

	private NextMeeting next;

	// 下場會議
	@Getter
	@Setter
	public static class NextMeeting {
		private String theme;
		private String reserveDate;
		private String contactName;
	 	private String contactPhone;	
		private String reserveStartTime;
		private String reserveEndTime;
	}
}
