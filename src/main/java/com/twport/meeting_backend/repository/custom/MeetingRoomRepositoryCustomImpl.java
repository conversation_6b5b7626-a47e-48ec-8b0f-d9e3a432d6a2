package com.twport.meeting_backend.repository.custom;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.repository.BaseRepository;
import com.twport.meeting_backend.repository.specification.MeetingRoomSpecification;
import com.twport.meeting_backend.vo.response.ManageQueryResponse;

public class MeetingRoomRepositoryCustomImpl extends BaseRepository<MeetingRoom> implements MeetingRoomRepositoryCustom {

	@Override
	public Page<ManageQueryResponse> paginationQuery(Pageable pageable, String portId, String officeId, String floor, String roomId, String keyword, String sort) {
				
		setSpecification(new MeetingRoomSpecification(portId, officeId, floor, roomId, keyword, sort));
				
		return pagingQuery(pageable, MeetingRoom.class, ManageQueryResponse.class);
	}

}
