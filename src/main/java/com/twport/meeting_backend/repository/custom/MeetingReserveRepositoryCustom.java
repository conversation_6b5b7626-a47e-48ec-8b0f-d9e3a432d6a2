package com.twport.meeting_backend.repository.custom;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.vo.dto.MeetingReserveNowQueryDto;
import com.twport.meeting_backend.vo.dto.RecordQueryDTO;
import com.twport.meeting_backend.vo.dto.ReserveQueryDTO;
import com.twport.meeting_backend.vo.dto.ReserveTimeQueryDTO;
import com.twport.meeting_backend.vo.dto.StatisticsQueryDTO;

public interface MeetingReserveRepositoryCustom {

    List<ReserveQueryDTO> findAllReserveLists(List<String> floors, List<String> rooms, LocalDateTime firstDay,
            LocalDateTime lastDay);

    List<ReserveTimeQueryDTO> findAllReserveRecords(LocalDateTime fistDay, LocalDateTime lastDay);

    List<MeetingReserveNowQueryDto> findNowMeetingReserve(String roomId);

    List<MeetingReserve> findIfMeetingExist(LocalDateTime bTime, LocalDateTime eTime, String roomId, String meetingId);

    Page<RecordQueryDTO> reservePaginationQuery(Pageable pageable, Long departmentId, String floor, String room,
            String startDate, String endDate, String keyword, String sort);

    Page<StatisticsQueryDTO> reservePaginationQuery(Pageable pageable, String room, String startDate, String endDate,
            String keyword, String sort);
}
