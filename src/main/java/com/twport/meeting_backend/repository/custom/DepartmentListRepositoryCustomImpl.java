package com.twport.meeting_backend.repository.custom;

import java.util.List;

import com.twport.meeting_backend.entity.DepartmentList;
import com.twport.meeting_backend.repository.BaseRepository;
import com.twport.meeting_backend.repository.specification.DepartmentListFindByCompanySpecification;
import com.twport.meeting_backend.vo.dto.DepartmentListOptionDto;

public class DepartmentListRepositoryCustomImpl extends BaseRepository<DepartmentList> implements DepartmentListRepositoryCustom {

	@Override
	public List<DepartmentListOptionDto> findAllDepartmentLists(String companyId) {
		setSpecification(new DepartmentListFindByCompanySpecification(companyId));
		return listQuery(DepartmentList.class, DepartmentListOptionDto.class);
	}

}
