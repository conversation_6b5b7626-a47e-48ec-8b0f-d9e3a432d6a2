package com.twport.meeting_backend.repository.custom;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.twport.meeting_backend.vo.response.ManageQueryResponse;

public interface MeetingRoomRepositoryCustom {
	Page<ManageQueryResponse> paginationQuery(Pageable pageable, String portId, String officeId, String floor, String roomId, String keyword, String sort);
}
