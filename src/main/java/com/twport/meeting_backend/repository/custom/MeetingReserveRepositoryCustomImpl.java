package com.twport.meeting_backend.repository.custom;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.repository.BaseRepository;
import com.twport.meeting_backend.repository.specification.MeetingReserveNowQuerySpecification;
import com.twport.meeting_backend.repository.specification.RecordQuerySpecification;
import com.twport.meeting_backend.repository.specification.ReserveFindRoomSpecification;
import com.twport.meeting_backend.repository.specification.ReserveQuerySpecification;
import com.twport.meeting_backend.repository.specification.ReserveTimeQuerySpecification;
import com.twport.meeting_backend.repository.specification.StatisticsQuerySpecification;
import com.twport.meeting_backend.vo.dto.MeetingReserveNowQueryDto;
import com.twport.meeting_backend.vo.dto.RecordQueryDTO;
import com.twport.meeting_backend.vo.dto.ReserveQueryDTO;
import com.twport.meeting_backend.vo.dto.ReserveTimeQueryDTO;
import com.twport.meeting_backend.vo.dto.StatisticsQueryDTO;

public class MeetingReserveRepositoryCustomImpl extends BaseRepository<MeetingReserve>
		implements MeetingReserveRepositoryCustom {

	@Override
	public List<ReserveQueryDTO> findAllReserveLists(List<String> floors, List<String> rooms, LocalDateTime firstDay,
			LocalDateTime lastDay) {
		setSpecification(new ReserveQuerySpecification(floors, rooms, firstDay, lastDay));
		return listQuery(MeetingReserve.class, ReserveQueryDTO.class);
	}

	@Override
	public List<MeetingReserve> findIfMeetingExist(LocalDateTime bTime, LocalDateTime eTime, String roomId,
			String meetingId) {
		setSpecification(new ReserveFindRoomSpecification(bTime, eTime, roomId, meetingId));
		return listQuery(MeetingReserve.class, MeetingReserve.class);
	}

	@Override
	public List<MeetingReserveNowQueryDto> findNowMeetingReserve(String roomId) {
		setSpecification(new MeetingReserveNowQuerySpecification(roomId));
		return listQuery(MeetingReserve.class, MeetingReserveNowQueryDto.class);
	}

	@Override
	public Page<RecordQueryDTO> reservePaginationQuery(Pageable pageable, Long departmentId, String floor, String room,
			String startDate,
			String endDate, String keyword, String sort) {
		setSpecification(new RecordQuerySpecification(departmentId, floor, room, startDate, endDate, keyword, sort));
		return pagingQuery(pageable, MeetingReserve.class, RecordQueryDTO.class);
	}

	@Override
	public List<ReserveTimeQueryDTO> findAllReserveRecords(LocalDateTime fistDay, LocalDateTime lastDay) {
		setSpecification(new ReserveTimeQuerySpecification(fistDay, lastDay));
		return listQuery(MeetingReserve.class, ReserveTimeQueryDTO.class);
	};

	@Override
	public Page<StatisticsQueryDTO> reservePaginationQuery(Pageable pageable, String room, String startDate,
			String endDate, String keyword, String sort) {
		setSpecification(new StatisticsQuerySpecification(room, startDate, endDate, keyword, sort));
		return pagingQuery(pageable, MeetingReserve.class, StatisticsQueryDTO.class);
	}
}
