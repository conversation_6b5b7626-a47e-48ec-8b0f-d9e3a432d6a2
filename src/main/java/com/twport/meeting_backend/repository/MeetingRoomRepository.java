package com.twport.meeting_backend.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.repository.custom.MeetingRoomRepositoryCustom;

@Repository
public interface MeetingRoomRepository extends JpaRepository<MeetingRoom, String>, MeetingRoomRepositoryCustom {

	/**
	 * @param roomEnable 會議室啟用
	 */
	List<MeetingRoom> findByRoomEnable(boolean roomEnable);

	List<MeetingRoom> findAllByOrderByFloorAscRoomNameAsc();

	List<MeetingRoom> findByOfficeId(String officeId);

	List<MeetingRoom> findByOfficeIdAndRoomEnable(String officeId, boolean enable);
	
	MeetingRoom findByRoomCode(String roomCode);

	MeetingRoom findTopByOrderByIdDesc();
}
