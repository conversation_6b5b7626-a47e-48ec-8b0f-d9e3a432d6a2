package com.twport.meeting_backend.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.twport.meeting_backend.entity.DepartmentList;
import com.twport.meeting_backend.repository.custom.DepartmentListRepositoryCustom;

@Repository
public interface DepartmentListRepository extends JpaRepository<DepartmentList, String>, DepartmentListRepositoryCustom {
	Optional<DepartmentList> findByName(String name);
	
	Optional<DepartmentList> findByNameAndCompanyId(String name, String companyId);
	
	List<DepartmentList> findByCompanyId(String companyId);
}
