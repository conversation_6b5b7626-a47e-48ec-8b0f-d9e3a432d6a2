package com.twport.meeting_backend.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.twport.meeting_backend.entity.MeetingReserveDepartment;

@Repository
public interface MeetingReserveDepartmentRepository extends JpaRepository<MeetingReserveDepartment, Long> {
    void deleteAllByReserveId(String reserveId);
	Long countByReserveId(String reserveId);
	List<MeetingReserveDepartment> findByReserveId(String reserveId);
}
