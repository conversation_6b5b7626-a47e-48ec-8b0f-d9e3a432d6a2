package com.twport.meeting_backend.repository;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Setter
@Slf4j
public class BaseRepository<E> {
	
	@PersistenceContext
	@Autowired
	private EntityManager entityManager;
	
	private Specification<E> specification;
	
	public  <R> Page<R> pagingQuery(Pageable pageable, Class<E> entity, Class<R> responseType) {
		CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
		CriteriaQuery<R> criteriaQuery = criteriaBuilder.createQuery(responseType);
		Root<E> entityRoot = criteriaQuery.from(entity);
		Predicate predicate = specification.toPredicate(entityRoot, criteriaQuery, criteriaBuilder);
		criteriaQuery.where(predicate);

		entityManager.createQuery(criteriaQuery).getResultList();
		
		Integer totalSize = entityManager.createQuery(criteriaQuery).getResultList().size();
		
        TypedQuery<R> typedQuery = entityManager.createQuery(criteriaQuery)
        		.setFirstResult(Long.valueOf(pageable.getOffset()).intValue())
        		.setMaxResults(pageable.getPageSize());
        List<R> resultList = typedQuery.getResultList();
                
        return new PageImpl<>(resultList, pageable, totalSize);
	}
	
	public <R> List<R> listQuery(Class<E> entity, Class<R> responseType) {
		CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
		CriteriaQuery<R> criteriaQuery = criteriaBuilder.createQuery(responseType);
		Root<E> entityRoot = criteriaQuery.from(entity);
		Predicate predicate = specification.toPredicate(entityRoot, criteriaQuery, criteriaBuilder);
		criteriaQuery.where(predicate);
		
		return entityManager.createQuery(criteriaQuery).getResultList();
	}
	
	public <R> R singleQuery(Class<E> entity, Class<R> responseType) {
		CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
		CriteriaQuery<R> criteriaQuery = criteriaBuilder.createQuery(responseType);
		Root<E> entityRoot = criteriaQuery.from(entity);
		Predicate predicate = specification.toPredicate(entityRoot, criteriaQuery, criteriaBuilder);
		criteriaQuery.where(predicate);
		
		List<R> result = entityManager.createQuery(criteriaQuery).getResultList();
		
		if(result.size() == 0) {
			return null;
		}else {
			return result.get(0);
		}		
	}
}
