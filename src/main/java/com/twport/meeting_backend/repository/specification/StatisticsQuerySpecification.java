/**
 * 
 */
package com.twport.meeting_backend.repository.specification;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.entity.MeetingRoom;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

/**
 * <AUTHOR>
 * @datetime 2024年10月15日 
 * @class StatisticsQuerySpecification
 * @description 會議室查詢 - 組合查詢條件
 */
public class StatisticsQuerySpecification implements Specification<MeetingReserve>{

	private static final long serialVersionUID = 1L;
	
	private String room;
	
	private String startDate;
	
	private String endDate;
	
	private String keyword;

	private String sort;
	
	private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	public StatisticsQuerySpecification(String room, String startDate, String endDate, String keyword, String sort) {
		this.room = room;
		this.startDate = startDate;
		this.endDate = endDate;
		this.keyword = keyword;
		this.sort = sort;
	}

	@Override
	public Predicate toPredicate(Root<MeetingReserve> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
		List<Predicate> predicates = new ArrayList<>();
		List<Order> orders = new ArrayList<>();
		
		Root<MeetingRoom> meetingRoomRoot = query.from(MeetingRoom.class);
		predicates.add(builder.equal(root.get("roomId"), meetingRoomRoot.get("id")));
		
		Root<AppUser> reserveUserRoot = query.from(AppUser.class);
		predicates.add(builder.equal(root.get("reserveId"), reserveUserRoot.get("id")));
		
		// 會議室
		if (StringUtils.isNotBlank(room)) {
			Expression<String> floorExp = meetingRoomRoot.get("id").as(String.class);
			predicates.add(builder.equal(floorExp, room));
		}
		
		// 關鍵字
		if (StringUtils.isNotBlank(keyword)) {
			Expression<String> themeExp = root.get("theme").as(String.class);
			predicates.add(builder.like(themeExp, "%" + keyword + "%"));
			Expression<Boolean> personalExp = root.get("personal").as(Boolean.class);
			predicates.add(builder.equal(personalExp, false));
		}
		
		// 查詢起日
		if (StringUtils.isNotBlank(startDate)) {
			Expression<LocalDateTime> reserveDateStartExp = root.get("reserveDateStart").as(LocalDateTime.class);
			predicates.add(builder.greaterThanOrEqualTo(reserveDateStartExp, LocalDate.parse(startDate, formatter).atStartOfDay()));
		}
		
		// 查詢迄日
		if (StringUtils.isNotBlank(endDate)) {
			Expression<LocalDateTime> reserveDateEndExp = root.get("reserveDateEnd").as(LocalDateTime.class);
			predicates.add(builder.lessThanOrEqualTo(reserveDateEndExp, LocalDate.parse(endDate, formatter).atTime(23, 59,59)));
		}
		
		if (StringUtils.equals("roomName", sort) || StringUtils.equals("floor", "sort")) {
			Expression<String> orderExp = meetingRoomRoot.get(sort).as(String.class);
			orders.add(builder.asc(orderExp));
		} else if (StringUtils.equals("firstName", sort) || StringUtils.equals("lastName", sort) || StringUtils.equals("fullName", sort)) {
			Expression<String> orderExp = reserveUserRoot.get(sort).as(String.class);
			orders.add(builder.asc(orderExp));
		} else {
			Expression<String> orderExp = root.get(sort).as(String.class);
			orders.add(builder.asc(orderExp));
		}
		
		query.where(builder.and(predicates.toArray(new Predicate[0])));
		query.orderBy(orders);
		
		query.multiselect(
				meetingRoomRoot.get("roomName").alias("roomName"),
                meetingRoomRoot.get("floor").alias("floor"),
                reserveUserRoot.get("firstName").alias("reserveFirstName"),
                reserveUserRoot.get("lastName").alias("reserveLastName"),
                reserveUserRoot.get("fullName").alias("reserveFullName"),
                root.get("departmentName").alias("department"),
                root.get("hostName").alias("hostFullName"),
                root.get("theme").alias("theme"),
                root.get("reserveDateStart").alias("reserveDateStart"),
                root.get("reserveDateEnd").alias("reserveDateEnd"),
                root.get("status").alias("status"),
                root.get("personal").alias("personal"),
                root.get("contactPhone").alias("reservePhoneNumber"),
                root.get("reserveId").alias("reserveId"),
                root.get("members").alias("members"),
                root.get("id").alias("id")
        );
		
		return query.getRestriction();
	}

}
