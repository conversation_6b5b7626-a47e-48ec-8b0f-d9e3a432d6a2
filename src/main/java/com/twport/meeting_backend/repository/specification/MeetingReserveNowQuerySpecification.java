package com.twport.meeting_backend.repository.specification;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.springframework.data.jpa.domain.Specification;

import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.enums.RecordStatus;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

public class MeetingReserveNowQuerySpecification implements Specification<MeetingReserve> {

	private static final long serialVersionUID = 1L;
		
	private String roomId;
	
	public MeetingReserveNowQuerySpecification(String roomId) {
		this.roomId = roomId;
	}

	@Override
	public Predicate toPredicate(Root<MeetingReserve> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
		List<Predicate> predicates = new ArrayList<>();
		LocalDateTime nowTime = LocalDateTime.now();
		
		Root<MeetingRoom> meetingRoomRoot = query.from(MeetingRoom.class);
		predicates.add(builder.equal(root.get("roomId"), meetingRoomRoot.get("id")));
		
		predicates.add(builder.equal(root.get("roomId"), roomId));
		Expression<LocalDateTime> reserveDateStartExp = root.get("reserveDateStart").as(LocalDateTime.class);
		Expression<LocalDateTime> reserveDateEndExp = root.get("reserveDateEnd").as(LocalDateTime.class);
		
		predicates.add(builder.lessThanOrEqualTo(reserveDateStartExp, nowTime));
		predicates.add(builder.greaterThan(reserveDateEndExp, nowTime));
		
		predicates.add(builder.notEqual(root.get("status"), RecordStatus.CANCEL));
		predicates.add(builder.notEqual(root.get("status"), RecordStatus.END));
		
		query.where(builder.and(predicates.toArray(new Predicate[0])));

		query.orderBy(builder.asc(root.get("reserveDateStart")));
		
		query.multiselect(
				meetingRoomRoot.get("roomName").alias("roomName"),
				root.get("id").alias("reserveId"),
				root.get("theme").alias("theme"),
				root.get("reserveDateStart").alias("reserveDateStart"),
                root.get("reserveDateEnd").alias("reserveDateEnd"),
                root.get("personal").alias("personal"),
                root.get("members").as(Long.class).alias("members"),
                root.get("hostName").alias("hostName"),
                root.get("contactName").alias("contactName"),
                root.get("contactPhone").alias("contactPhone")
				);
		
		return query.getRestriction();
	}

}
