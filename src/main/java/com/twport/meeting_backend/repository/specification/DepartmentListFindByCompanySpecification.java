package com.twport.meeting_backend.repository.specification;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.jpa.domain.Specification;

import com.twport.meeting_backend.entity.CompanyList;
import com.twport.meeting_backend.entity.DepartmentList;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

public class DepartmentListFindByCompanySpecification implements Specification<DepartmentList> {

	private static final long serialVersionUID = 1L;

	private String companyId;
	
	public DepartmentListFindByCompanySpecification(String companyId) {
		this.companyId = companyId;
	}

	@Override
	public Predicate toPredicate(Root<DepartmentList> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
		List<Predicate> predicates = new ArrayList<>();
		
//		Root<OfficeList> officeRoot = query.from(OfficeList.class);
//		predicates.add(builder.equal(root.get("officeId"), officeRoot.get("id")));
		
		predicates.add(builder.equal(root.get("companyId"), companyId));
		
		Root<CompanyList> companyRoot = query.from(CompanyList.class);
		predicates.add(builder.equal(root.get("companyId"), companyRoot.get("id")));
		
		query.where(builder.and(predicates.toArray(new Predicate[0])));
		
		query.multiselect(
				root.get("id").alias("id"),
				root.get("name").alias("departmentName"),
				root.get("companyId").alias("companyId"),
				root.get("portId").alias("portId"),
				root.get("officeId").alias("officeId"),
				companyRoot.get("name").alias("companyName")
//				officeRoot.get("name").alias("officeName")
				);
		return query.getRestriction();
	}

}
