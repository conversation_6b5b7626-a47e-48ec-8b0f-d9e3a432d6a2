package com.twport.meeting_backend.repository.specification;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.springframework.data.jpa.domain.Specification;

import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.enums.RecordStatus;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

public class ReserveTimeQuerySpecification implements Specification<MeetingReserve> {

	private static final long serialVersionUID = 1L;
		
	private LocalDateTime fistDay;

	private LocalDateTime lastDay;

	public ReserveTimeQuerySpecification(LocalDateTime fistDay, LocalDateTime lastDay) {
		this.fistDay = fistDay;
		this.lastDay = lastDay;
	}

	@Override
	public Predicate toPredicate(Root<MeetingReserve> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
		List<Predicate> predicates = new ArrayList<>();
		List<Order> orders = new ArrayList<>();
		
		Root<MeetingRoom> meetingRoomRoot = query.from(MeetingRoom.class);
		predicates.add(builder.equal(root.get("roomId"), meetingRoomRoot.get("id")));
		
		predicates.add(builder.notEqual(root.get("status"), RecordStatus.CANCEL));
		predicates.add(builder.notEqual(root.get("status"), RecordStatus.END));
		
		Expression<LocalDateTime> reserveDateStartExp = root.get("reserveDateStart").as(LocalDateTime.class);
		predicates.add(builder.greaterThanOrEqualTo(reserveDateStartExp, fistDay));
		
		Expression<LocalDateTime> reserveDateEndExp = root.get("reserveDateEnd").as(LocalDateTime.class);
		predicates.add(builder.lessThanOrEqualTo(reserveDateEndExp, lastDay));
		
		orders.add(builder.asc(root.get("roomId")));
		
		query.where(builder.and(predicates.toArray(new Predicate[0])));
		query.orderBy(orders);
		
		query.multiselect(
				meetingRoomRoot.get("floor").alias("floor"),
				root.get("id").alias("meetingId"),
				root.get("roomId").alias("roomId"),
				root.get("reserveDateStart").alias("reserveDateStart"),
				root.get("reserveDateEnd").alias("reserveDateEnd")
				);
		
		return query.getRestriction();
	}

}
