package com.twport.meeting_backend.repository.specification;

import jakarta.persistence.criteria.*;
import org.springframework.data.jpa.domain.Specification;

import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.enums.RecordStatus;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


public class ReserveFindRoomSpecification implements Specification<MeetingReserve> {

    private static final long serialVersionUID = 1L;

    private LocalDateTime bTime;

    private LocalDateTime eTime;

    private String roomId;
    
    private String meetingId;

    public ReserveFindRoomSpecification(LocalDateTime bTime, LocalDateTime eTime, String roomId, String meetingId) {
        this.bTime = bTime;
        this.eTime = eTime;
        this.roomId = roomId;
        this.meetingId = meetingId;
    }

    @Override
    public Predicate toPredicate(Root<MeetingReserve> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
        List<Predicate> predicates = new ArrayList<>();
        List<Order> orders = new ArrayList<>();
        
//        predicates.add(builder.between(root.get("reserveDateStart"), bTime, eTime));
//
//        predicates.add(builder.between(root.get("reserveDateEnd"), bTime.plusMinutes(1), eTime));
        
        predicates.add(builder.equal(root.get("roomId"), roomId));
        
        predicates.add(builder.equal(root.get("status"), RecordStatus.RESERVE.name()));
        
        if(meetingId != null) {
        	predicates.add(builder.notEqual(root.get("id"), meetingId));
        }
        
        Expression<LocalDateTime> reserveDateStartExp = root.get("reserveDateStart").as(LocalDateTime.class);
		Expression<LocalDateTime> reserveDateEndExp = root.get("reserveDateEnd").as(LocalDateTime.class);
        
        Predicate startTimePredicate = builder.greaterThan(reserveDateEndExp, bTime);
        Predicate endTimePredicate = builder.lessThan(reserveDateStartExp, eTime);
        predicates.add(builder.and(startTimePredicate, endTimePredicate));

        Expression<LocalDateTime> orderEndDateExp = root.get("reserveDateEnd").as(LocalDateTime.class);
        orders.add(builder.desc(orderEndDateExp));

        query.where(builder.and(predicates.toArray(new Predicate[0])));
        query.orderBy(orders);

        query.multiselect(
        		root.get("id").alias("id"),
                root.get("reserveDateStart").alias("reserveDateStart"),
                root.get("reserveDateEnd").alias("reserveDateEnd"),
                root.get("theme").alias("theme"),
                root.get("hostId").alias("hostId"),
                root.get("status").alias("status"),
                root.get("personal").alias("personal"),
                root.get("roomId").alias("roomId")
        );

        return query.getRestriction();
    }
}
