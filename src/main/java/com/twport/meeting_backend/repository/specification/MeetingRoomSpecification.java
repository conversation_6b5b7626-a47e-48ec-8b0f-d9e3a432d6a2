package com.twport.meeting_backend.repository.specification;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.entity.OfficeList;
import com.twport.meeting_backend.entity.PortList;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

public class MeetingRoomSpecification implements Specification<MeetingRoom> {

	private static final long serialVersionUID = 1L;

	private String portId;

	private String officeId;

	private String floor;

	private String roomId;

	private String keyword;

	private String sort;

	public MeetingRoomSpecification(String portId, String officeId, String floor, String roomId,String keyword, String sort) {
		this.portId = portId;
		this.officeId = officeId;
		this.floor = floor;
		this.roomId = roomId;
		this.keyword = keyword;
		this.sort = sort;
	}

	@Override
	public Predicate toPredicate(Root<MeetingRoom> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
		List<Predicate> predicates = new ArrayList<>();

		Root<PortList> portListRoot = query.from(PortList.class);
		Root<OfficeList> officeListRoot = query.from(OfficeList.class);

		Expression<String> portIdExp = portListRoot.get("id").as(String.class);
		predicates.add(builder.equal(portIdExp, root.get("portId")));
		if(StringUtils.isNoneBlank(portId)) {
			predicates.add(builder.equal(root.get("portId"), portId));
		}

		Expression<String> officeIdExp = officeListRoot.get("id").as(String.class);
		predicates.add(builder.equal(officeIdExp, root.get("officeId")));
		if(StringUtils.isNoneBlank(officeId)) {
			predicates.add(builder.equal(root.get("officeId"), officeId));
		}

		if(StringUtils.isNoneBlank(floor)) {
			Expression<Long> floorExp = root.get("floor").as(Long.class);
			predicates.add(builder.equal(floorExp, floor));
		}

		if(StringUtils.isNoneBlank(roomId)) {
			Expression<String> roomIdExp = root.get("id").as(String.class);
			predicates.add(builder.equal(roomIdExp, roomId));
		}

		// 關鍵字查找會議室名稱
		if(StringUtils.isNoneBlank(keyword)) {
			Expression<String> roomNameExp = root.get("roomName").as(String.class);
			predicates.add(builder.like(roomNameExp, "%" + keyword + "%"));
		}

		// 過濾掉已刪除的記錄
		predicates.add(builder.or(
			builder.isNull(root.get("isDel")),
			builder.equal(root.get("isDel"), false)
		));
		
		
		
		List<Order> orders = new ArrayList<>();
		if (StringUtils.equals("port", sort)) {
			Expression<String> orderPortExp = portListRoot.get("name").as(String.class);
			orders.add(builder.asc(orderPortExp));
		} else if (StringUtils.equals("office", sort)) {
			Expression<String> orderOfficeExp = officeListRoot.get("name").as(String.class);
			orders.add(builder.asc(orderOfficeExp));
		} else if (StringUtils.equals("floor", sort)) {
			Expression<Integer> orderRoomExp = root.get(sort).as(Integer.class);
			orders.add(builder.asc(orderRoomExp));
		} else {
			Expression<String> orderRoomExp = root.get(sort).as(String.class);
			orders.add(builder.asc(orderRoomExp));
		}

		query.where(builder.and(predicates.toArray(new Predicate[0])));
		query.orderBy(orders);

		query.multiselect(
				root.get("id").alias("id"),
				portListRoot.get("name").alias("portName"),
				officeListRoot.get("name").alias("officeName"),
				root.get("roomName").alias("name"),
				root.get("floor").alias("floor"),
				root.get("roomEnable").alias("enabled"),
				root.get("onlineMeeting").alias("onlineMeeting"),
				root.get("envDevice").alias("envDevice"),
				root.get("capacity").alias("capacity")
				);

		return query.getRestriction();
	}

}
