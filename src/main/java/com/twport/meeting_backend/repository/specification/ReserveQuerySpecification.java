package com.twport.meeting_backend.repository.specification;

import jakarta.persistence.criteria.*;
import org.springframework.data.jpa.domain.Specification;

import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.enums.RecordStatus;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @datetime 2024年8月1日
 * @class Specification
 * @description 會議室預約查詢組合語法
 */
public class ReserveQuerySpecification implements Specification<MeetingReserve> {

	private static final long serialVersionUID = 1L;

	private List<String> floors;

	private List<String> rooms;

	private LocalDateTime fistDay;

	private LocalDateTime lastDay;

	public ReserveQuerySpecification(List<String> floors, List<String> rooms, LocalDateTime fistDay, LocalDateTime lastDay) {
		this.floors = floors;
		this.rooms =  rooms;
		this.fistDay = fistDay;
		this.lastDay = lastDay;
	}

	@Override
	public Predicate toPredicate(Root<MeetingReserve> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
		List<Predicate> predicates = new ArrayList<>();
		List<Order> orders = new ArrayList<>();
		
		predicates.add(builder.notEqual(root.get("status"), RecordStatus.CANCEL));
		
		Root<MeetingRoom> meetingRoomRoot = query.from(MeetingRoom.class);
		predicates.add(builder.equal(root.get("roomId"), meetingRoomRoot.get("id")));
		
		Root<AppUser> reserveUserRoot = query.from(AppUser.class);
		predicates.add(builder.equal(root.get("reserveId"), reserveUserRoot.get("id")));
		
		// 樓層
		if (floors != null && !floors.isEmpty()) {
			Expression<Long> floorExp = meetingRoomRoot.get("floor").as(Long.class);
			predicates.add(builder.and(floorExp.in(floors)));
		}

		if (rooms != null && !rooms.isEmpty()) {
			Expression<String> roomExp = meetingRoomRoot.get("id").as(String.class);
			predicates.add(builder.and(roomExp.in(rooms)));
		}
		
		// 查詢起日、迄日
		if (fistDay != null && lastDay != null) {
			Expression<LocalDateTime> reserveDateStartExp = root.get("reserveDateStart").as(LocalDateTime.class);
			Expression<LocalDateTime> reserveDateEndExp = root.get("reserveDateEnd").as(LocalDateTime.class);

			Predicate startInRange = builder.and(builder.greaterThanOrEqualTo(reserveDateStartExp, fistDay),
					builder.lessThan(reserveDateStartExp, lastDay));

			Predicate endInRange = builder.and(builder.greaterThan(reserveDateEndExp, fistDay),
					builder.lessThanOrEqualTo(reserveDateEndExp, lastDay));
			predicates.add(builder.or(startInRange, endInRange));
		}
		
		query.where(builder.and(predicates.toArray(new Predicate[0])));
		query.orderBy(orders);

		query.multiselect(
				meetingRoomRoot.get("roomName").alias("roomName"),
				meetingRoomRoot.get("floor").alias("floor"),
				reserveUserRoot.get("id").alias("reserveId"),
				reserveUserRoot.get("firstName").alias("reserveFirstName"),
				reserveUserRoot.get("lastName").alias("reserveLastName"),
				reserveUserRoot.get("fullName").alias("reserveFullName"),
				root.get("departmentName").alias("departmentName"),
				root.get("id").alias("meetingId"),
                root.get("reserveDateStart").alias("reserveDateStart"),
				root.get("reserveDateEnd").alias("reserveDateEnd"),
				root.get("theme").alias("theme"),
				root.get("status").alias("status"),
                root.get("personal").alias("personal"),
                root.get("roomId").alias("roomId"),
                root.get("contactId").alias("contactId"),
                root.get("contactName").alias("contactName"),
                root.get("contactPhone").alias("contactPhone")
        );

		return query.getRestriction();
	}

}
