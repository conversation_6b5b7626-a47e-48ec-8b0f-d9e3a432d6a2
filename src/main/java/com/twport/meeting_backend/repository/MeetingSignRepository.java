package com.twport.meeting_backend.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.twport.meeting_backend.entity.MeetingSignIn;

@Repository
public interface MeetingSignRepository extends JpaRepository<MeetingSignIn, String> {
	Long countByReserveId(String reserveId);
	
	List<MeetingSignIn> findByReserveId(String reserveId);

	List<MeetingSignIn> findByReserveIdAndUserId(String reserveId, String userId);
}
