package com.twport.meeting_backend.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.twport.meeting_backend.entity.AppUser;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年7月22日 
 * @class AppUserRepository
 * @description 操作使用者資料表類別
 */

@Repository
public interface AppUserRepository extends JpaRepository<AppUser, String> {
	Optional<AppUser> findByAccount(String account);
	
	Optional<AppUser> findByJobNum(String jobNum);
	
	List<AppUser> findByDepartmentId(String departmentId);
	
	List<AppUser> findByCompanyId(String companyId);
	
	List<AppUser> findByDepartmentIdOrderByFullNameAsc(String departmentId);

}
