package com.twport.meeting_backend.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.repository.custom.MeetingReserveRepositoryCustom;
import com.twport.meeting_backend.vo.dto.MeetingReserveDTO;
import com.twport.meeting_backend.vo.dto.MeetingReserveNowQueryDto;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年7月26日
 * @class MeetingReserveRepository
 * @description 操作預約資料表類別
 */

@Repository
public interface MeetingReserveRepository extends JpaRepository<MeetingReserve, String>, MeetingReserveRepositoryCustom {

        /**
         * 會議預約查詢
         */
        String SQL_MEETING_RESERVE_LIST = """
                        select
                            r.room_name as room,
                            mr.subject as theme,
                            mr.reserve_date_start as reserveStart,
                            mr.reserve_date_end as reserveEnd,
                            mr.host_name as hostName,
                            mr.contact_name as contactName,
                            mr.contact_phone as contactPhone,
                            mr.status as status
                        from
                        	meeting_reserve mr
                        left join
                        	meeting_room r
                        on
                        	mr.room_id = r.id
                        where
                        	reserve_date_start > :start and
                        	reserve_date_start < :end and
                        	(:code is null or room_code = :code)
                        order by
                            reserve_date_start
                                    """;

        @Query(value = SQL_MEETING_RESERVE_LIST, nativeQuery = true)
        List<MeetingReserveDTO> getReserve(
                        @Param("start") LocalDateTime start,
                        @Param("end") LocalDateTime end,
                        @Param("code") String code);

        MeetingReserve findByReserveDateStartAndReserveDateEndAndRoomId(LocalDateTime Bdate, LocalDateTime Edate,
                        String roomId);

        List<MeetingReserve> findByReserveDateStartBetween(LocalDateTime startDate, LocalDateTime endDate);

        Long countByReserveDateStartBetweenAndRoomId(LocalDateTime startDate, LocalDateTime endDate, String roomId);

        long countByRoomId(String roomId);

        @Query(value = "select * from meeting_reserve where room_id = :roomId and reserve_date_start > now() order by reserve_date_start limit 1", nativeQuery = true)
        Optional<MeetingReserve> findNextMeetingReserve(@Param("roomId") String roomId);
}
