package com.twport.meeting_backend.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

@Getter
public enum RecordStatus {

	RESERVE("已預約"),
	
	CONDUCT("進行中"),
	
	END("已結束"),
	
	CANCEL("已取消");
	
	String message;
	
	private RecordStatus(String message) {
		this.message = message;
	}

	/**
	 * 是否為已預約
	 */
	public boolean isReserve() {
		return StringUtils.equals(this.name(), "RESERVE");
	}

	/**
	 * 是否為進行中
	 */
	public boolean isConduct() {
		return StringUtils.equals(this.name(), "CONDUCT");
	}

	/**
	 * 是否為已結束
	 */
	public boolean isEnd() {
		return StringUtils.equals(this.name(), "END");
	}

	/**
	 * 是否為已取消
	 */
	public boolean isCancel() {
		return StringUtils.equals(this.name(), "CANCEL");
	}


}
