package com.twport.meeting_backend.enums;

import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @datetime 2024年7月26日
 * @class ENUM
 * @description 成功訊息集合
 */
@Getter
public enum ResponseStatusCode {

    SUCCESS("0000", "處理成功"),

    SEARCH_SUCCESS("0001", "查詢成功"),

    INSERT("0002", "新增成功"),

    UPDATE("0003", "更新成功"),

    DELETE("0004", "刪除成功"),

    FAILED("9990", "處理失敗"),

    SEARCH_FAILED("9991", "查詢失敗"),

    INSERT_FAILED("9992", "新增失敗"),

    UPDATE_FAILED("9993", "更新失敗"),

    DELETE_FAILED("9994", "刪除失敗"),

    NOTHING("9995", "查無資料"),

    INVALIDATE("9996", "未具有該權限"),

    TIME_OUT("9997", "連線逾時"),

    CHECK_FAILED("9998", "檢核失敗"),

    ERROR("9999", "系統錯誤");

	private String code;
    private String message;

    private ResponseStatusCode(String code, String message) {
    	this.code = code;
        this.message = message;
    }
}
