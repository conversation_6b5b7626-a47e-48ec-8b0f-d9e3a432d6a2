package com.twport.meeting_backend.security;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.context.annotation.Profile;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import com.twport.meeting_backend.entity.AdminMenu;
import com.twport.meeting_backend.repository.AdminMenuRepository;
import com.twport.meeting_backend.vo.dto.AdminMenuModel;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

/**
 * Security登入成功 將使用者資訊存入session
 * 
 * <AUTHOR>
 * @datetime 2024年7月22日 
 * @class SecurityAuthSuccessHandler
 * @description
 */
@Component
@Profile({"dev", "frontend"})
public class SecurityAuthSuccessHandler implements AuthenticationSuccessHandler {
	
	private final AdminMenuRepository adminMenuRepository;
	
	public SecurityAuthSuccessHandler(AdminMenuRepository adminMenuRepository) {
		this.adminMenuRepository = adminMenuRepository;
	}

	@Override
	public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
		// 取得menu資訊
		// TODO 目前全部選單都開放，日後需加上權限控制
		List<AdminMenu> adminMenus = adminMenuRepository.findAll();
		List<AdminMenuModel> models = new ArrayList<>(); 
		for (AdminMenu adminMenu : adminMenus) {
			AdminMenuModel model = new AdminMenuModel();
			model.setTaskId(adminMenu.getTaskId());
			model.setTaskName(adminMenu.getTaskName());
			model.setTaskImage(adminMenu.getTaskImage());
			model.setTaskUrl(adminMenu.getTaskUrl());
			models.add(model);
		}
		
		models.sort((menu1, menu2) -> menu1.getTaskId().compareTo(menu2.getTaskId()));
		
		// 存入session資訊
		HttpSession session = request.getSession();
		session.setAttribute("menus", models);
		
		response.sendRedirect(request.getContextPath() + "/meeting/reserve");
	}

}
