package com.twport.meeting_backend.security;

import java.util.Collection;

import org.springframework.context.annotation.Profile;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.entity.ldap.Person;
import com.twport.meeting_backend.vo.dto.UserProfile;

import lombok.ToString;

@ToString
@Profile({"frontend"})
public class LdapUserDetails implements UserDetails {

	private final Person person;
	
	private final AppUser user;

	private transient UserProfile userProfile;
	
	public LdapUserDetails(Person person, AppUser user) {
		this.person = person;
		this.user = user;
		this.userProfile = new UserProfile(user);
	}

	@Override
	public Collection<? extends GrantedAuthority> getAuthorities() {
		return null;
	}

	@Override
	public String getPassword() {
		return "PROTECTED";
	}

	@Override
	public String getUsername() {
		return user.getAccount();
	}
	
	public String getUserPrincipalName() {
		return person.getUserPrincipalName();
	}
	
	public String getName() {
		return user.getFullName();
	}
	
	public UserProfile getCurrentUser() {
		return userProfile;
	}
}
