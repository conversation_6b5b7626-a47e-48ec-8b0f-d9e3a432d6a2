package com.twport.meeting_backend.security;

import java.util.Collection;

import org.springframework.context.annotation.Profile;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.vo.dto.UserProfile;

@Profile("dev")
public class AppUserDetails implements UserDetails {

	private final AppUser user;

	private transient UserProfile userProfile;

	public AppUserDetails(AppUser user) {
		this.user = user;
		this.userProfile = new UserProfile(user);
	}

	@Override
	public Collection<? extends GrantedAuthority> getAuthorities() {
		return null;
	}

	@Override
	public String getPassword() {
		return user.getPassword();
	}

	@Override
	public String getUsername() {
		return user.getAccount();
	}

	public String getName() {
		return user.getFullName();
	}

	public UserProfile getCurrentUser() {
		return userProfile;
	}

}
