package com.twport.meeting_backend.security;

import org.springframework.context.annotation.Profile;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.filter.EqualsFilter;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@Profile({"frontend"})
public class LdapCustomAuthenticationProvider implements AuthenticationProvider {

	private final UserDetailsService userDetailsService;
	
	private final LdapTemplate ldapTemplate;
	
	public LdapCustomAuthenticationProvider(UserDetailsService userDetailsService, LdapTemplate ldapTemplate) {
		this.userDetailsService = userDetailsService;
		this.ldapTemplate = ldapTemplate;
	}

	@Override
	public Authentication authenticate(Authentication authentication) throws AuthenticationException {
		String username = authentication.getName();
		String password = authentication.getCredentials().toString();
		log.info("username : {}", username);
		log.info("password : {}", password);
		ldapTemplate.setIgnorePartialResultException(true);

		EqualsFilter filter = new EqualsFilter("sAMAccountName", username);
		
		boolean result = ldapTemplate.authenticate("", filter.toString(), password);
		log.info("result : {}", result);
		
		if(!result) {
			throw new AuthenticationException("帳號或密碼錯誤") {};
		}
		
		UserDetails userDetails = userDetailsService.loadUserByUsername(username);
		log.info("user details : {}", userDetails);
				
		Authentication authenticated = new UsernamePasswordAuthenticationToken(userDetails, password, userDetails.getAuthorities());
		log.info("provider Authentication : {}", authenticated.getPrincipal());
		return authenticated;
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return authentication.equals(UsernamePasswordAuthenticationToken.class);
	}

}
