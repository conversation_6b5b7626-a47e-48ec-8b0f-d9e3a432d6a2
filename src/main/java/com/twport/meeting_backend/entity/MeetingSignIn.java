package com.twport.meeting_backend.entity;

import java.time.LocalDateTime;

import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.twport.meeting_backend.enums.SignInType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * <AUTHOR> Lin
 * @datetime 2024年8月2日 
 * @class MeetingSignIn
 * @description 會議簽到紀錄表
 */
@Getter
@Setter
@ToString
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "meeting_sign_in")
@Comment("會議簽到紀錄表")
public class MeetingSignIn {
	
	/**
	 * 簽到id
	 */
	@Id
//	@GeneratedValue(strategy = GenerationType.IDENTITY)
	//@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "meeting_sign_in_seq")
	//@SequenceGenerator(name = "meeting_sign_in_seq", sequenceName = "meeting_sign_in_seq", allocationSize = 1)
	@Column(name = "id")
	@Comment("簽到id")
	private String id;
	
	/**
	 * 會議id
	 */
	@Column(name = "reserve_id", nullable = false)
	@Comment("會議id")
	private String reserveId;
	
	/**
	 * 單位id
	 */
	@Column(name = "department_id")
	@Comment("單位id")
	private String departmentId;
	
	/**
	 * 與會單位名稱
	 */
	@Column(name = "department_name", nullable = false)
	@Comment("與會單位名稱")
	private String departmentName;
	
	/**
	 * 與會人員職稱
	 */
	@Column(name = "job_title")
	@Comment("與會人員職稱")
	private String jobTitle;
	
	/**
	 * 簽到方式(SIGNIN : 手動， IDCARD : 識別證)
	 */
	@Column(name = "sign_in_type", nullable = false)
	@Enumerated(EnumType.STRING)
	@Comment("簽到方式")
	private SignInType signInType;
	
	/**
	 * 手動簽到的簽名圖檔url
	 */
	@Column(name = "sign_in_image_url")
	@Comment("手動簽到的簽名圖檔url")
	private String signInImageUrl;
	
	/**
	 * 識別證簽到的使用者id
	 */
	@Column(name = "user_id")
	@Comment("識別證簽到的使用者id")
	private String userId;
	
	/**
	 * 創建時間
	 */
	@Column(name = "created_time")
	@Comment("創建時間")
	@CreatedDate
	private LocalDateTime createdTime;

	/**
	 * 最後更新時間
	 */
	@Column(name = "updated_time")
	@Comment("最後更新時間")
	@LastModifiedDate
	private LocalDateTime updatedTime;
	
	/**
	 * 是否已刪除
	 */
	@Column(name = "is_deleted", nullable = false)
	@Comment("是否已刪除")
	private boolean deleted;
}
