package com.twport.meeting_backend.entity;


import java.util.List;

import org.hibernate.annotations.Comment;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "admin_menu")
@Comment("功能列表")
public class AdminMenu {

	@Id
	@Column(name = "task_id")
	@Comment("功能id")
	private String taskId;

	/**
	 * 名稱
	 */
	@Column(name = "task_name")
	@Comment("功能名稱")
	private String taskName;

	/**
	 * Icon 路徑
	 */
	@Column(name = "task_img")
	@Comment("功能圖示路徑")
	private String taskImage;

	/**
	 * 路徑
	 */
	@Column(name = "task_url")
	@Comment("功能url")
	private String taskUrl;
	
	@Column(name = "role_list")
	@Comment("可操作功能的權限列表")
	private List<String> roleList;
}
