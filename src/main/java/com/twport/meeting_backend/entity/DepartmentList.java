package com.twport.meeting_backend.entity;

import java.time.LocalDateTime;

import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年8月2日 
 * @class DepartmentList
 * @description 單位資訊資料表
 */
@Getter
@Setter
@ToString
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "department_list")
@Comment("單位資訊紀錄表")
public class DepartmentList {
	
	/**
	 * 單位id
	 */
	@Id
//	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "department_list_seq")
	@SequenceGenerator(name = "department_list_seq", sequenceName = "department_list_seq", allocationSize = 1)
	@Column(name = "id")
	@Comment("單位id")
	private String id;
	
	/*
	 * 單位名稱
	 */
	@Column(name = "name")
	@Comment("單位名稱")
	private String name;
	
	/**
	 * 單位代號
	 */
	@Column(name = "code")
	@Comment("單位代號")
	private String code;
	
	/**
	 * 單位所屬的港別id
	 */
	@Column(name = "port_id")
	@Comment("單位所屬的港別id")
	private String portId;
	
	/**
	 * 單位所屬的辦公室別id
	 */
	@Column(name = "office_id")
	@Comment("單位所屬的辦公室id")
	private String officeId;
	
	/**
	 * 單位所屬的公司id
	 */
	@Column(name = "company_id", nullable = false)
	@Comment("單位所屬的公司id")
	private String companyId;
	
	/**
	 * 創建時間
	 */
	@Column(name = "created_time")
	@Comment("創建時間")
	@CreatedDate
	private LocalDateTime createdTime;

	/**
	 * 最後更新時間
	 */
	@Column(name = "updated_time")
	@Comment("最後更新時間")
	@LastModifiedDate
	private LocalDateTime updatedTime;

}
