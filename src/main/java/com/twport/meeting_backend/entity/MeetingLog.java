package com.twport.meeting_backend.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.twport.meeting_backend.enums.LogType;

import java.time.LocalDateTime;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年8月5日 
 * @class MeetingLog
 * @description 操作紀錄表
 */
@Getter
@Setter
@ToString
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "meeting_log")
@Comment("操作紀錄表")
public class MeetingLog {

	@Id
//	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "meeting_log_seq")
	@SequenceGenerator(name = "meeting_log_seq", sequenceName = "meeting_log_seq", allocationSize = 1)
	@Column(name = "id")
	@Comment("操作紀錄id")
	private Long id;
	
	/**
	 * 異動人員id
	 */
	@Column(name = "user_id", nullable = false)
	@Comment("異動人員id")
	private String userId;
	
	/**
	 * 操作請求的客戶ip
	 */
	@Column(name = "client_ip")
	@Comment("操作請求的客戶ip")
	private String clientIp;
	
	/**
	 * 請求url
	 */
	@Column(name = "request_url")
	@Comment("請求url")
	private String requestUrl;
	
	/**
	 * log類型(請求 : REQUEST、回應 : RESPONSE、ERROR : 錯誤)
	 */
	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	@Comment("log類型(請求 : REQUEST、回應 : RESPONSE、ERROR : 錯誤)")
	private LogType type;
	
	/**
	 * 狀態碼(對應response的status參數)
	 */
	@Column(name = "status_code")
	@Comment("狀態碼(對應response的status參數)")
	private String statusCode;
	
	/**
	 * 回應訊息(對應response的message參數)
	 */
	@Column(name = "message")
	@Comment("回應訊息(對應response的message參數)")
	private String message;
	
	@Column(name = "request_body", columnDefinition = "text")
	@Comment("request內容")
	private String requestBody;
	
	/**
	 * response回應內容
	 */
	@Column(name = "response_body", columnDefinition = "text")
	@Comment("response回應內容")
	private String responseBody;
	
	/**
	 * 錯誤訊息描述
	 */
	@Column(name = "error_info", columnDefinition = "text")
	@Comment("錯誤訊息描述")
	private String errorInfo;

	/**
	 * 創建時間
	 */
	@Column(name = "created_time")
	@Comment("創建時間")
	@CreatedDate
	private LocalDateTime createdTime;
	
	/**
	 * 最後更新時間
	 */
	@Column(name = "updated_time")
	@Comment("最後更新時間")
	@LastModifiedDate
	private LocalDateTime updatedTime;
}
