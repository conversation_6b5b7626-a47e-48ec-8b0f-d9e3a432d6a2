package com.twport.meeting_backend.entity;


import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.twport.meeting_backend.enums.MeetingMode;
import com.twport.meeting_backend.enums.RecordStatus;
import com.twport.meeting_backend.enums.SituationMode;

import jakarta.persistence.CollectionTable;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * <AUTHOR> Lin
 * @datetime 2024年8月2日 
 * @class MeetingReserve
 * @description 會議資訊資料表
 */
@Getter
@Setter
@ToString
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "meeting_reserve")
@Comment("會議資訊紀錄表")
public class MeetingReserve {

	/**
	 * 會議id
	 */
	@Id
	// postgresql 10版本以後才可用IDENTITY
//	@GeneratedValue(strategy = GenerationType.IDENTITY)
	// postgresql 10版本以前只能用sequence建立ID自動遞增規則
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "meeting_reserve_seq")
	@SequenceGenerator(name = "meeting_reserve_seq", sequenceName = "meeting_reserve_seq", allocationSize = 1)
	@Column(name = "id")
	@Comment("會議id")
	private String id;

	/**
	 * 會議室id
	 */
	@Column(name = "room_id", nullable = false)
	@Comment("會議室id")
	private String roomId;

	/**
	 * 預約會議室時間 - 起
	 */
	@Column(name = "reserve_date_start", nullable = false)
	@Comment("預約會議室時間 - 起")
	private LocalDateTime reserveDateStart;
	
	/**
	 * 預約會議室時間 - 迄
	 */
	@Column(name = "reserve_date_end", nullable = false)
	@Comment("預約會議室時間 - 迄")
	private LocalDateTime reserveDateEnd;

	/**
	 * 預約人id
	 */
	@Column(name = "reserve_id", nullable = false)
	@Comment("預約人id")
	private String reserveId;

	/**
	 * 主持人id
	 */
	@Column(name = "host_id")
	@Comment("主持人id")
	private String hostId;
	
	/**
	 * 主持人姓名(自行輸入)
	 */
	@Column(name = "host_name")
	@Comment("主持人姓名(自行輸入)")
	private String hostName;
	
	/**
	 * 聯絡人id
	 */
	@Column(name = "contact_id")
	@Comment("聯絡人id")
	private String contactId;
	
	/**
	 * 聯絡人姓名(自行輸入)
	 */
	@Column(name = "contact_name")
	@Comment("聯絡人姓名(自行輸入)")
	private String contactName;
	
	/**
	 * 聯絡人電話(自行輸入)
	 */
	@Column(name = "contact_phone")
	@Comment("聯絡人電話(自行輸入)")
	private String contactPhone;
	
	/**
	 * 預約單位id
	 */
	@Column(name = "department_id")
	@Comment("預約單位id")
	private String departmentId;
	
	/**
	 * 預約單位名稱(自行輸入)
	 */
	@Column(name = "department_name")
	@Comment("預約單位名稱(自行輸入)")
	private String departmentName;

	/**
	 * 狀態(CANCEL: 已取消、RESERVE: 已預約、END: 已結束、CONDUCT: 進行中)
	 */
	@Column(name = "status", nullable = false)
	@Enumerated(EnumType.STRING)
	@Comment("狀態(CANCEL: 已取消、RESERVE: 已預約、END: 已結束、CONDUCT: 進行中)")
	private RecordStatus status;

	/**
	 * 會議主題
	 */
	@Column(name = "subject", nullable = false)
	@Comment("會議主題")
	private String theme;
	
	/**
	 * 與會單位
	 */
	@ElementCollection(targetClass = Long.class, fetch = FetchType.EAGER)
	@CollectionTable(name = "meeting_reserve_department", joinColumns = @JoinColumn(name="reserve_id"))
	@Column(name = "department_id")
	private List<Long> attendDepartment = new ArrayList<>();
	
	@ElementCollection(targetClass = String.class, fetch = FetchType.EAGER)
	@CollectionTable(name = "meeting_reserve_department", joinColumns = @JoinColumn(name="reserve_id"))
	@Column(name = "department_name")
	private List<String> attendDepartmentNames = new ArrayList<>();
	
	/**
	 * 預估人數
	 */
	@Column(name = "attendees", nullable = false)
	@Comment("預估人數")
	private Integer members;

	/**
	 * 是否公開
	 */
	@Column(name = "is_public")
	@Comment("是否公開")
	private boolean personal;
	
	/**
	 * 會議模式(NORMAL: 一般會議、ONLINE: 線上會議、MIX: 混合會議)
	 */
	@Column(name = "meeting_mode", nullable = false)
	@Enumerated(EnumType.STRING)
	@Comment("會議模式(NORMAL: 一般會議、ONLINE: 線上會議、MIX: 混合會議)")
	private MeetingMode meetingMode;

	/**
	 * 會議情境(NORMAL: 一般會議、PPT: 簡報會議、VIDEO: 視訊會議)
	 */
	@Column(name="situation_mode", nullable = false)
	@Enumerated(EnumType.STRING)
	@Comment("會議情境(NORMAL: 一般會議、PPT: 簡報會議、VIDEO: 視訊會議)")
	private SituationMode situationMode;
	
	/**
	 * 線上會議連結
	 */
	@Column(name = "online_meeting_url")
	@Comment("線上會議連結")
	private String onlineMeetingUrl;
	
	/**
	 * 創建時間
	 */
	@Column(name = "created_time", nullable = false)
	@CreatedDate
	@Comment("創建時間")
	private LocalDateTime createdTime;

	/**
	 * 最後更新時間
	 */
	@Column(name = "updated_time", nullable = false)
	@LastModifiedDate
	@Comment("最後更新時間")
	private LocalDateTime updatedTime;
	
	/**
	 * QRCode
	 */
	@Column(name = "qr_code")
	@Comment("QRCode")
	private String qrCode;
}
