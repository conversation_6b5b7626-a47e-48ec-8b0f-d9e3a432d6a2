package com.twport.meeting_backend.entity.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.hibernate.annotations.IdGeneratorType;

import com.twport.meeting_backend.config.MeetingRoomIdGeneratorConfig;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年10月7日 
 * @class MeetingRoomIdSequence
 * @description 自訂meeting_room資料表的ID自動遞增規則的自訂註解
 */
@IdGeneratorType(MeetingRoomIdGeneratorConfig.class)
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface MeetingRoomIdSequence {

}
