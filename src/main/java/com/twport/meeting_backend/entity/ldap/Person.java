package com.twport.meeting_backend.entity.ldap;

import java.util.List;

import javax.naming.Name;

import org.springframework.ldap.odm.annotations.Attribute;
import org.springframework.ldap.odm.annotations.Entry;
import org.springframework.ldap.odm.annotations.Id;
import org.springframework.ldap.odm.annotations.Transient;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entry(objectClasses = {"user", "organizationalPerson", "person", "top"})
public class Person {
	@Id
	private Name dn;
	
	@Attribute(name = "cn")
	private String commonName;
	
	@Attribute(name = "displayName")
	private String name;
	
	@Attribute(name = "initials")
	private String jobNumber;
	
	@Attribute(name = "mail")
	private String email;
	
	@Attribute(name = "sn")
	private String firstName;
	
	@Attribute(name = "givenName")
	private String lastName;
	
	@Attribute(name = "title")
	private String jobTitle;
	
	@Attribute(name = "sAMAccountName")
	private String sAMAccountName;
	
	@Attribute(name = "userPrincipalName")
	private String userPrincipalName;
	
	@Attribute(name = "telephoneNumber")
	private String phone;
	
	/**
	 * 帳號是否停用(512 : 啟用，514 : 停用)
	 */
	@Attribute(name = "userAccountControl")
	private Integer enable;
	
	@Attribute(name = "department")
	private String department;
	
	@Attribute(name = "company")
	private String company;
	
	@Transient
	private List<String> dnList;
}
