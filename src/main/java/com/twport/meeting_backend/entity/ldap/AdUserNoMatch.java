package com.twport.meeting_backend.entity.ldap;

import java.time.LocalDateTime;
import java.util.List;

import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年9月22日 
 * @class LdapUserNoMatch
 * @description 沒有符合條件的AD帳號資訊紀錄表
 */

@Getter
@Setter
@ToString
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "AD_USER_NOMATCH")
@Comment("不符合條件的AD使用者資訊紀錄表")
public class AdUserNoMatch {
	/**
	 * 紀錄id
	 */
	@Id
//	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ad_user_nomatch_seq")
	@SequenceGenerator(name = "ad_user_nomatch_seq", sequenceName = "ad_user_nomatch_seq", allocationSize = 1)
	@Column(name = "id")
	@Comment("紀錄id")
	private Long id;
	
	@Column(name = "sam_account_name")
	@Comment("AD使用者帳號(AD欄位 : sAMAccountName)")
	private String sAMAccountName;
	
	@Column(name = "common_name")
	@Comment("AD使用者識別值(AD欄位 : CN)")
	private String commonName;
	
	@Column(name = "job_number")
	@Comment("AD使用者員工工號(AD欄位 : initials)")
	private String jobNumber;
	
	@Column(name = "company")
	@Comment("AD使用者所屬公司(AD欄位 : company)")
	private String company;
	
	@Column(name = "department")
	@Comment("AD使用者所屬單位(AD欄位 : department)")
	private String department;
	
	@Column(name = "filter_dn_list")
	@Comment("AD使用者過濾後的DN")
	private List<String> filterDnList;
	
	/**
	 * 建立時間
	 */
	@Column(name = "created_time")
	@Comment("建立時間")
	@CreatedDate
	private LocalDateTime createdTime;
	
	/**
	 * 修改時間
	 */
	@Column(name = "updated_time")
	@Comment("修改時間")
	@LastModifiedDate
	private LocalDateTime updatedTime;
}
