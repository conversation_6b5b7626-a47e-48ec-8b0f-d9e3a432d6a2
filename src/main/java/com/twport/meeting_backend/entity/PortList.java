package com.twport.meeting_backend.entity;

import java.time.LocalDateTime;

import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年8月2日 
 * @class PortList
 * @description 港別資訊資料表
 */
@Getter
@Setter
@ToString
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "port_list")
@Comment("港別資訊紀錄表")
public class PortList {
	
	/**
	 * 港別id(用於與環控系統溝通用的會議室ID編碼)
	 */
	@Id
	@Column(name = "id")
	@Comment("港別id(用於與環控系統溝通用的會議室ID編碼)")
	private String id;
	
	/**
	 * 港別名稱
	 */
	@Column(name = "name")
	@Comment("港別名稱")
	private String name;
	
	/**
	 * 創建時間
	 */
	@Column(name = "created_time")
	@Comment("創建時間")
	@CreatedDate
	private LocalDateTime createdTime;

	/**
	 * 最後更新時間
	 */
	@Column(name = "updated_time")
	@Comment("最後更新時間")
	@LastModifiedDate
	private LocalDateTime updatedTime;
}
