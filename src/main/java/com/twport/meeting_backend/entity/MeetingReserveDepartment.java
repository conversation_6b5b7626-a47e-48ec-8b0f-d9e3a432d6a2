package com.twport.meeting_backend.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年8月2日 
 * @class MeetingReserveDepartment
 * @description 會議與會單位資訊資料表
 */
@Getter
@Setter
@ToString
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "meeting_reserve_department")
@Comment("會議邀請單位紀錄表")
public class MeetingReserveDepartment {
	
	/**
	 * 會議邀請單位id
	 */
	@Id
//	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "meeting_reserve_department_seq")
	@SequenceGenerator(name = "meeting_reserve_department_seq", sequenceName = "meeting_reserve_department_seq", allocationSize = 1)
	@Column(name = "id")
	@Comment("會議邀請單位id")
	private Long id;
	
	/**
	 * 會議id
	 */
	@Column(name = "reserve_id", nullable = false)
	@Comment("會議id")
	private String reserveId;
	
	/**
	 * 與會單位id
	 */
	@Column(name = "department_id")
	@Comment("與會單位id")
	private Long departmentId;
	
	/**
	 * 與會單位名稱
	 */
	@Column(name = "department_name", nullable = false)
	@Comment("與會單位名稱")
	private String departmentName;
	
	/**
	 * 與會單位人員id
	 */
	@Column(name = "user_id")
	@Comment("與會單位人員id")
	private String userId;
	
	/**
	 * 與會單位人員姓名
	 */
	@Column(name = "user_name")
	@Comment("與會單位人員姓名")
	private String userName;
	
	/**
	 * 與會單位人員電話
	 */
	@Column(name = "user_phone")
	@Comment("與會單位人員電話")
	private String userPhone;
	
	/**
	 * 與會單位人員郵件
	 */
	@Column(name = "user_email")
	@Comment("與會單位人員郵件")
	private String userEmail;
	
	/**
	 * 是否為聯絡人
	 */
	@Column(name = "contact")
	@Comment("是否為聯絡人")
	private boolean contact;
	
	/**
	 * 創建時間
	 */
	@Column(name = "created_time")
	@Comment("創建時間")
	@CreatedDate
	private LocalDateTime createdTime;

	/**
	 * 最後更新時間
	 */
	@Column(name = "updated_time")
	@Comment("最後更新時間")
	@LastModifiedDate
	private LocalDateTime updatedTime;
}
