package com.twport.meeting_backend.entity;

import java.time.LocalDateTime;

import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.twport.meeting_backend.entity.annotation.MeetingRoomIdSequence;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年8月2日 
 * @class MeetingRoom
 * @description 會議室資訊資料表
 */

@Getter
@Setter
@ToString
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "meeting_room")
@Comment("會議室資訊紀錄表")
public class MeetingRoom {
	
	/**
	 * 會議室id(用於與環控系統溝通用的會議室ID編碼)
	 */
	@Id
	@MeetingRoomIdSequence
	@Column(name = "id")
	@Comment("會議室id(用於與環控系統溝通用的會議室ID編碼)")
	private String id;
	
	/**
	 * 會議室代號(用於與環控系統溝通用的會議室ID)
	 */
	@Column(name = "room_code")
	@Comment("會議室代號(用於與環控系統溝通用的會議室ID)")
	private String roomCode;

	/**
	 * 會議室名稱
	 */
	@Column(name = "room_name", nullable = false)
	@Comment("會議室名稱")
	private String roomName;
	
	/**
	 * 港別id(即港別編碼)
	 */
	@Column(name = "port_id", nullable = false)
	@Comment("港別id(即港別編碼)")
	private String portId;
	
	/**
	 * 辦公室別(即辦公室別編碼)
	 */
	@Column(name = "office_id", nullable = false)
	@Comment("辦公室別id(即辦公室別編碼)")
	private String officeId;

	/**
	 * 樓層
	 */
	@Column(name = "floor", nullable = false)
	@Comment("樓層")
	private Integer floor;
	
	/**
	 * 是否有環控設備
	 */
	@Column(name = "env_device", nullable = false)
	@Comment("是否有環控設備")
	private boolean envDevice;

	/**
	 * 是否可以執行線上會議
	 */
	@Column(name = "online_meeting", nullable = false)
	@Comment("是否可以執行線上會議")
	private boolean onlineMeeting;
	
	/**
	 * 是否啟用
	 */
	@Column(name = "room_enable")
	@Comment("是否啟用")
	private boolean roomEnable;

	/**
	 * 是否啟用
	 */
	@Column(name = "is_del", nullable = false)
	@Comment("是否刪除")
	private Boolean isDel;
	
	/**
	 * 可容納人數
	 */
	@Column(name = "capacity")
	@Comment("可容納人數")
	private Long capacity;
	
	/**
	 * 建立時間
	 */
	@Column(name = "created_time")
	@Comment("建立時間")
	@CreatedDate
	private LocalDateTime createdTime;
	
	/**
	 * 修改時間
	 */
	@Column(name = "updated_time")
	@Comment("修改時間")
	@LastModifiedDate
	private LocalDateTime updatedTime;
}
