package com.twport.meeting_backend.entity;

import java.time.LocalDateTime;

import jakarta.persistence.*;

import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年7月22日 
 * @class AppUser
 * @description 使用者資料表
 */

@Getter
@Setter
@ToString
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "APP_USER")
@Comment("使用者資訊紀錄表")
public class AppUser {
	
	@Id
	@GeneratedValue(strategy = GenerationType.UUID)
	@Column(name = "id")
	@Comment("使用者id")
	private String id;
	
	/**
	 * 帳號
	 */
	@Column(name = "account", nullable = false)
	@Comment("帳號")
	private String account;
	
	/**
	 * 密碼(使用者 : 密碼，管理者 : sso token)
	 */
	@Column(name = "password")
	@Comment("密碼(使用者 : 密碼，管理者 : sso token)")
	private String password;
	
	@Column(name = "role")
	@Comment("使用者權限")
	private String role;
	
	/**
	 * 所屬單位id
	 */
	@Column(name = "department_id", nullable = false)
	@Comment("所屬單位id")
	private String departmentId;
	
	/**
	 * 所屬公司id
	 */
	@Column(name = "company_id", nullable = false)
	@Comment("所屬公司id")
	private String companyId;
	
	/**
	 * 姓氏
	 */
	@Column(name = "first_name")
	@Comment("姓氏")
	private String firstName;
	
	/**
	 * 名字
	 */
	@Column(name = "last_name")
	@Comment("名字")
	private String lastName;
	
	/**
	 * 名字
	 */
	@Column(name = "full_name")
	@Comment("全名")
	private String fullName;
	
	/**
	 * 工號
	 */
	@Column(name = "job_num")
	@Comment("工號")
	private String jobNum;
	
	@Column(name = "job_title")
	@Comment("職稱")
	private String jonTitle;

	
	/**
	 * 識別證卡號
	 */
	@Column(name = "card_num")
	@Comment("識別證卡號")
	private String cardNum;
	
	/**
	 * 聯絡電話
	 */
	@Column(name = "phone")
	@Comment("聯絡電話")
	private String phone;
	
	/**
	 * 聯絡信箱
	 */
	@Column(name = "email")
	@Comment("聯絡信箱")
	private String email;

	/**
	 * 建立時間
	 */
	@Column(name = "created_time")
	@Comment("建立時間")
	@CreatedDate
	private LocalDateTime createdTime;
	
	/**
	 * 修改時間
	 */
	@Column(name = "updated_time")
	@Comment("修改時間")
	@LastModifiedDate
	private LocalDateTime updatedTime;
}
