package com.twport.meeting_backend.entity;

import java.time.LocalDateTime;

import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "COMPANY_LIST")
@Comment("港務公司表")
public class CompanyList {
	/**
	 * 單位id
	 */
	@Id
//	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "company_list_seq")
	@SequenceGenerator(name = "company_list_seq", sequenceName = "company_list_seq", allocationSize = 1)
	@Column(name = "id")
	@Comment("公司id")
	private String id;
	
	/*
	 * 單位名稱
	 */
	@Column(name = "name")
	@Comment("公司名稱")
	private String name;
	
	/**
	 * 單位代號
	 */
	@Column(name = "code")
	@Comment("公司代號")
	private String code;
	
	/**
	 * 創建時間
	 */
	@Column(name = "created_time")
	@Comment("創建時間")
	@CreatedDate
	private LocalDateTime createdTime;

	/**
	 * 最後更新時間
	 */
	@Column(name = "updated_time")
	@Comment("最後更新時間")
	@LastModifiedDate
	private LocalDateTime updatedTime;
}
