package com.twport.meeting_backend.aspect;

import java.util.Arrays;
import java.util.Enumeration;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * Controller 監控 AOP
 * 記錄所有 Controller 的請求資訊
 *
 * <AUTHOR>
 * @datetime 2024年12月19日
 * @class ControllerLoggingAspect
 * @description 監控所有 Controller 並記錄執行的 URI、請求參數、執行時間等資訊
 */
@Aspect
@Component
@Slf4j
public class ControllerLoggingAspect {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 定義切點：攔截所有 Controller 包下的方法
     */
    @Pointcut("execution(* com.twport.meeting_backend.controller..*(..))")
    public void controllerMethods() {}

    /**
     * 環繞通知：記錄請求開始和結束，計算執行時間，記錄 Request body 和 Response body
     */
    @Around("controllerMethods()")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 獲取請求資訊
        HttpServletRequest request = getCurrentRequest();
        String requestInfo = getRequestInfo(request, joinPoint);

        // 記錄 Request Body
        String requestBody = getRequestBody(joinPoint);

        log.info("=== 請求開始 === {}", requestInfo);
        if (requestBody != null && !requestBody.isEmpty()) {
            log.info("Request Body: {}", requestBody);
        }

        Object result = null;
        try {
            // 執行原方法
            result = joinPoint.proceed();

            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;

            // 記錄 Response Body
            String responseBody = getResponseBody(result);

            log.info("=== 請求完成 === {} | 執行時間: {}ms", requestInfo, executionTime);
            if (responseBody != null && !responseBody.isEmpty()) {
                log.info("Response Body: {}", responseBody);
            }

            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;

            log.error("=== 請求異常 === {} | 執行時間: {}ms | 異常: {}",
                     requestInfo, executionTime, e.getMessage());
            throw e;
        }
    }

    /**
     * 前置通知：記錄請求參數
     */
    @Before("controllerMethods()")
    public void logBefore(JoinPoint joinPoint) {
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            logRequestDetails(request, joinPoint);
        }
    }

    /**
     * 後置返回通知：記錄返回結果
     */
    @AfterReturning(pointcut = "controllerMethods()", returning = "result")
    public void logAfterReturning(JoinPoint joinPoint, Object result) {
        HttpServletRequest request = getCurrentRequest();
        String uri = request != null ? request.getRequestURI() : "Unknown";
        String method = request != null ? request.getMethod() : "Unknown";

        if (result != null) {
            log.info("返回結果 [{}] {} | 結果類型: {}", method, uri, result.getClass().getSimpleName());
        } else {
            log.info("返回結果 [{}] {} | 結果: null", method, uri);
        }
    }

    /**
     * 異常通知：記錄異常資訊
     */
    @AfterThrowing(pointcut = "controllerMethods()", throwing = "exception")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable exception) {
        HttpServletRequest request = getCurrentRequest();
        String uri = request != null ? request.getRequestURI() : "Unknown";
        String method = request != null ? request.getMethod() : "Unknown";

        log.error("異常發生 [{}] {} | 異常類型: {} | 異常訊息: {}",
                 method, uri, exception.getClass().getSimpleName(), exception.getMessage());
    }

    /**
     * 獲取當前請求
     */
    private HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            return attributes.getRequest();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 獲取請求基本資訊
     */
    private String getRequestInfo(HttpServletRequest request, JoinPoint joinPoint) {
        if (request == null) {
            return String.format("方法: %s.%s",
                               joinPoint.getSignature().getDeclaringTypeName(),
                               joinPoint.getSignature().getName());
        }

        return String.format("[%s] %s | 控制器: %s.%s | IP: %s",
                           request.getMethod(),
                           request.getRequestURI(),
                           joinPoint.getSignature().getDeclaringTypeName(),
                           joinPoint.getSignature().getName(),
                           getClientIpAddress(request));
    }

    /**
     * 記錄詳細的請求資訊
     */
    private void logRequestDetails(HttpServletRequest request, JoinPoint joinPoint) {
        // 記錄請求頭資訊
        StringBuilder headers = new StringBuilder();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            // 過濾敏感資訊
            if (headerName.toLowerCase().contains("authorization") ||
                headerName.toLowerCase().contains("cookie")) {
                headerValue = "***";
            }
            headers.append(headerName).append(": ").append(headerValue).append("; ");
        }

        // 記錄請求參數
        StringBuilder params = new StringBuilder();
        request.getParameterMap().forEach((key, values) -> {
            params.append(key).append(": ").append(Arrays.toString(values)).append("; ");
        });

        // 記錄方法參數
        Object[] args = joinPoint.getArgs();
        StringBuilder methodArgs = new StringBuilder();
        if (args != null && args.length > 0) {
            for (int i = 0; i < args.length; i++) {
                if (args[i] != null) {
                    methodArgs.append("參數").append(i + 1).append(": ")
                             .append(args[i].getClass().getSimpleName()).append("; ");
                }
            }
        }

        log.debug("請求詳情 [{}] {} | User-Agent: {} | 請求參數: {} | 方法參數: {}",
                 request.getMethod(),
                 request.getRequestURI(),
                 request.getHeader("User-Agent"),
                 params.length() > 0 ? params.toString() : "無",
                 methodArgs.length() > 0 ? methodArgs.toString() : "無");
    }

    /**
     * 獲取客戶端真實 IP 地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 獲取請求體內容
     */
    private String getRequestBody(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return null;
        }

        // 查找被 @RequestBody 註解的參數
        for (Object arg : args) {
            if (arg != null && isRequestBodyParameter(arg)) {
                try {
                    // 如果是字串，直接返回
                    if (arg instanceof String) {
                        return (String) arg;
                    }
                    // 如果是其他物件，轉換為 JSON
                    return objectMapper.writeValueAsString(arg);
                } catch (Exception e) {
                    log.warn("無法序列化請求體: {}", e.getMessage());
                    return arg.toString();
                }
            }
        }
        return null;
    }

    /**
     * 判斷是否為請求體參數
     */
    private boolean isRequestBodyParameter(Object arg) {
        // 排除 HttpServletRequest, HttpServletResponse 等 Servlet 相關物件
        if (arg instanceof HttpServletRequest ||
            arg instanceof jakarta.servlet.http.HttpServletResponse ||
            arg instanceof org.springframework.ui.Model ||
            arg instanceof org.springframework.web.servlet.ModelAndView) {
            return false;
        }

        // 如果是基本類型的包裝類或字串，且不是 URL 參數，可能是請求體
        Class<?> clazz = arg.getClass();
        if (clazz.isPrimitive() ||
            clazz.equals(String.class) ||
            clazz.equals(Integer.class) ||
            clazz.equals(Long.class) ||
            clazz.equals(Boolean.class) ||
            clazz.equals(Double.class) ||
            clazz.equals(Float.class)) {
            return true;
        }

        // 如果是自定義物件（通常是 VO 類別），很可能是請求體
        String packageName = clazz.getPackage() != null ? clazz.getPackage().getName() : "";
        return packageName.startsWith("com.twport.meeting_backend.vo");
    }

    /**
     * 獲取回應體內容
     */
    private String getResponseBody(Object result) {
        if (result == null) {
            return "null";
        }

        try {
            // 如果是字串，直接返回
            if (result instanceof String) {
                return (String) result;
            }

            // 如果是 ModelAndView，只記錄視圖名稱
            if (result instanceof org.springframework.web.servlet.ModelAndView) {
                org.springframework.web.servlet.ModelAndView mv = (org.springframework.web.servlet.ModelAndView) result;
                return "ModelAndView(view: " + mv.getViewName() + ")";
            }

            // 其他物件轉換為 JSON
            String json = objectMapper.writeValueAsString(result);

            // 如果 JSON 太長，截斷顯示
            if (json.length() > 1000) {
                return json.substring(0, 1000) + "... (截斷，總長度: " + json.length() + " 字元)";
            }

            return json;
        } catch (Exception e) {
            log.warn("無法序列化回應體: {}", e.getMessage());
            return result.getClass().getSimpleName() + "@" + Integer.toHexString(result.hashCode());
        }
    }
}
