package com.twport.meeting_backend.service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.entity.DepartmentList;
import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.entity.MeetingSignIn;
import com.twport.meeting_backend.repository.AppUserRepository;
import com.twport.meeting_backend.repository.DepartmentListRepository;
import com.twport.meeting_backend.repository.MeetingReserveRepository;
import com.twport.meeting_backend.repository.MeetingRoomRepository;
import com.twport.meeting_backend.repository.MeetingSignRepository;
import com.twport.meeting_backend.utils.UserHolder;
import com.twport.meeting_backend.vo.dto.SelectOption;
import com.twport.meeting_backend.vo.dto.StatisticsQueryDTO;
import com.twport.meeting_backend.vo.request.StatisticsQueryRequest;
import com.twport.meeting_backend.vo.response.BarChartData;
import com.twport.meeting_backend.vo.response.LineChartData;
import com.twport.meeting_backend.vo.response.StatisticsChartResponse;
import com.twport.meeting_backend.vo.response.StatisticsInitResponse;
import com.twport.meeting_backend.vo.response.StatisticsQueryPageModel;
import com.twport.meeting_backend.vo.response.base.PageResponse;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class StatisticsService {

	private final MeetingReserveRepository meetingReserveRepository;
	private final DepartmentListRepository departmentListRepository;
	private final MeetingRoomRepository meetingRoomRepository;
	private final AppUserRepository appUserRepository;
	private final MeetingSignRepository meetingSignRepository;

	public StatisticsService(MeetingReserveRepository meetingReserveRepository,
			DepartmentListRepository departmentListRepository, MeetingRoomRepository meetingRoomRepository,
			AppUserRepository appUserRepository, MeetingSignRepository meetingSignRepository) {
		this.meetingReserveRepository = meetingReserveRepository;
		this.departmentListRepository = departmentListRepository;
		this.meetingRoomRepository = meetingRoomRepository;
		this.appUserRepository = appUserRepository;
		this.meetingSignRepository = meetingSignRepository;
	}

	/**
	 * 取得Table列表
	 */
	/**
	 * 
	 * <AUTHOR> Zhang
	 * @datetime 2024年9月30日
	 * @method
	 * @param request
	 * @return
	 * @description 取得會議室使用紀錄(分頁查詢)
	 */
	public PageResponse<StatisticsQueryPageModel> getRecordTableData(StatisticsQueryRequest request) {
		// 初始化
		Pageable page = PageRequest.of(request.getCurrentPage(), request.getPageSize());
		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		// 資料查詢
		Page<StatisticsQueryDTO> recordPage = meetingReserveRepository.reservePaginationQuery(page,
				request.getRoom(), request.getStartDate(), request.getEndDate(), request.getKeyword(), request.getSort());

		List<StatisticsQueryPageModel> pageModels = new ArrayList<>();

		for (StatisticsQueryDTO result : recordPage.getContent()) {
			Boolean personal =  !StringUtils.equals(UserHolder.getCurrentUser().getId(), result.getReserveId()) && result.isPersonal();
			StatisticsQueryPageModel pageModel = new StatisticsQueryPageModel();

			pageModel.setTheme(personal ? "不公開" : result.getTheme());
			pageModel.setReserveDate(dateFormatter.format(result.getReserveDateStart()));

			Duration duration = Duration.between(result.getReserveDateStart(), result.getReserveDateEnd());
			Long hour = duration.toHours();
			Long minute = duration.toMinutes() % 60;
			if (hour != 0 && minute != 0) {
				pageModel.setReserveTime(hour + "小時" + minute + "分");
			} else if (hour == 0) {
				pageModel.setReserveTime(minute + "分");
			} else {
				pageModel.setReserveTime(hour + "小時");
			}
			pageModel.setRoomNameDisplay(result.getRoomName() + " " + "(" + result.getFloor() + "F)");
			pageModel.setDepartment(personal ? "不公開" : result.getDepartment());
			pageModel.setContactUser(result.getReserveFullName());
			pageModel.setHostUser(personal ? "不公開" : result.getHostFullName());
			pageModel.setPersonal(personal ? "不公開" : "公開");
			pageModel.setStatus(result.getStatus().getMessage());

			pageModel.setMembers(result.getMembers());
			List<MeetingSignIn> signInMember = meetingSignRepository.findByReserveId(result.getId());
			pageModel.setSignMembers(signInMember.size());

			pageModels.add(pageModel);
		}
		PageResponse<StatisticsQueryPageModel> pageResponse = new PageResponse<>();
		pageResponse.setTotalSize(Long.valueOf(recordPage.getTotalElements()).intValue());
		pageResponse.setPageSize(recordPage.getSize());
		pageResponse.setCurrentPage(recordPage.getNumber());
		pageResponse.setTotalPage(recordPage.getTotalPages());
		pageResponse.setContents(pageModels);

		return pageResponse;
	}

	/**
	 * 取得樓層資訊
	 */
	public StatisticsInitResponse getAllDefaultSelect() {
		List<MeetingRoom> meetingRooms = meetingRoomRepository.findAllByOrderByFloorAscRoomNameAsc();
		List<SelectOption> allRoomSelects = new ArrayList<>();
		List<SelectOption> floorSelOpts = new ArrayList<>();

		for (MeetingRoom meetingRoom : meetingRooms) {
			if (meetingRoom.getPortId().equals("KHH") && meetingRoom.getOfficeId().equals("KH01")) {

				Integer floor = meetingRoom.getFloor();
				String floorStr = floor.toString();
				String floorDisplay = floorStr + "F";

				String roomStr = meetingRoom.getId();
				String roomDisplay = meetingRoom.getRoomName();

				SelectOption floorOption = null;
				for (SelectOption option : floorSelOpts) {
					if (option.getValue().equals(floorStr)) {
						floorOption = option;
						break;
					}
				}

				if (floorOption == null) {
					floorOption = new SelectOption(floorStr, floorDisplay);
					floorSelOpts.add(floorOption);
				}
				floorOption.getSubOptions().add(new SelectOption(roomStr, roomDisplay));
				allRoomSelects.add(new SelectOption(roomStr, roomDisplay));
			}
		}

		StatisticsInitResponse response = new StatisticsInitResponse();
		response.setFloorSelects(floorSelOpts);
		response.setMeetingRoomSelects(allRoomSelects);

		log.info("options : {}", response);

		return response;
	}

	/**
	 * 匯出xlsx
	 */
	public Workbook exportXlsx(HttpServletResponse response) {
		Workbook wb = new XSSFWorkbook();
		String loginId = UserHolder.getCurrentUser().getId();
		try {
			response.setContentType("application/vnd.ms-excel");

			Sheet sheet = wb.createSheet("範例資料");
			int[] columnWidth = { 16 * 256, 15 * 256, 15 * 256, 15 * 256, 15 * 256, 15 * 256, 15 * 256, 15 * 256,
					15 * 256, 15 * 256, 15 * 256 };
			for (int i = 0; i < columnWidth.length; i++) {
				sheet.setColumnWidth(i, columnWidth[i]);
			}

			Row row = sheet.createRow(0);
			String[] headers = { "會議主題", "會議日期", "持續時間", "會議室", "預約單位", "聯絡人", "主持人", "是否公開", "狀態", "預約人數", "簽到人數" };
			for (int i = 0; i < headers.length; i++) {
				row.createCell(i).setCellValue(headers[i]);
			}

			int rowIndex = 1;
			List<MeetingReserve> reserveLists = meetingReserveRepository.findAll();
			Iterable<StatisticsQueryPageModel> models = new ArrayList<>();
			DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
			for (MeetingReserve reserveList : reserveLists) {
				// 使否不公開
				Boolean personal = !StringUtils.equals(loginId, reserveList.getReserveId()) && reserveList.isPersonal();
				StatisticsQueryPageModel model = new StatisticsQueryPageModel();
				model.setTheme(personal ? "不公開" : reserveList.getTheme());
				model.setReserveDate(dtf.format(reserveList.getReserveDateStart()));
				Duration duration = Duration.between(reserveList.getReserveDateStart(),
						reserveList.getReserveDateEnd());
				Long hour = duration.toHours();
				Long minute = duration.toMinutes() % 60;
				if (hour != 0 && minute != 0) {
					model.setReserveTime(hour + "小時" + minute + "分");
				} else if (hour == 0) {
					model.setReserveTime(minute + "分");
				} else {
					model.setReserveTime(hour + "小時");
				}
				Optional<MeetingRoom> roomName = meetingRoomRepository.findById(reserveList.getRoomId());
				if (roomName.isPresent()) {
					model.setRoomNameDisplay(roomName.get().getRoomName());
				} else {
					model.setRoomNameDisplay("");
				}

				if (personal) {
					model.setDepartment("不公開");
				} else if (reserveList.getDepartmentId() == null) {
					model.setDepartment(reserveList.getDepartmentName());
				} else {
					Optional<DepartmentList> departmentName = departmentListRepository
							.findById(reserveList.getDepartmentId());
					if (departmentName.isPresent()) {
						model.setDepartment(departmentName.get().getName());
					} else {
						model.setDepartment(reserveList.getDepartmentName());
					}
				}

				if (reserveList.getContactId() == null) {
					model.setContactUser(reserveList.getContactName());
				} else {
					Optional<AppUser> contactName = appUserRepository.findById(reserveList.getContactId());
					if (contactName.isPresent()) {
						model.setContactUser(contactName.get().getFullName());
					} else {
						model.setContactUser(reserveList.getContactName());
					}
				}

				if (personal) {
					model.setHostUser("不公開");
				} else if (reserveList.getHostId() == null) {
					model.setHostUser(reserveList.getHostName());
				} else {
					Optional<AppUser> hostName = appUserRepository.findById(reserveList.getHostId());
					if (hostName.isPresent()) {
						model.setHostUser(hostName.get().getFullName());
					} else {
						model.setHostUser(reserveList.getHostName());
					}
				}

				model.setPersonal(personal ? "不公開" : "公開");
				if (reserveList.getStatus().toString().equals("RESERVE")) {
					model.setStatus("已預約");
				}
				if (reserveList.getStatus().toString().equals("CANCEL")) {
					model.setStatus("已取消");
				}
				if (reserveList.getStatus().toString().equals("END")) {
					model.setStatus("已結束");
				}
				if (reserveList.getStatus().toString().equals("CONDUCT")) {
					model.setStatus("進行中");
				}
				model.setMembers(reserveList.getMembers());
				List<MeetingSignIn> signInMember = meetingSignRepository.findByReserveId(reserveList.getId());
				model.setSignMembers(signInMember.size());

				((List<StatisticsQueryPageModel>) models).add(model);
			}
			for (StatisticsQueryPageModel data : models) {
				row = sheet.createRow(rowIndex);
				row.createCell(0).setCellValue(data.getTheme());
				row.createCell(1).setCellValue(data.getReserveDate());
				row.createCell(2).setCellValue(data.getReserveTime());
				row.createCell(3).setCellValue(data.getRoomNameDisplay());
				row.createCell(4).setCellValue(data.getDepartment());
				row.createCell(5).setCellValue(data.getContactUser());
				row.createCell(6).setCellValue(data.getHostUser());
				row.createCell(7).setCellValue(data.getPersonal());
				row.createCell(8).setCellValue(data.getStatus());
				row.createCell(9).setCellValue(data.getMembers());
				row.createCell(10).setCellValue(data.getSignMembers());
				rowIndex++;
			}
			wb.write(response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			try {
				wb.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return wb;
	}

	/**
	 * 
	 * <AUTHOR>
	 * @datetime 2024年9月30日
	 * @method
	 * @return
	 * @description 統計圖表數據初始化
	 */
	public StatisticsChartResponse getReserveData() {
		List<MeetingRoom> allRoomId = meetingRoomRepository.findAll();

		// pieChart
		int reserveCount = 0;
		int cancelCount = 0;

		int mondayCount = 0;
		int tuesdayCount = 0;
		int wednesdayCount = 0;
		int thursdayCount = 0;
		int fridayCount = 0;
		String week = null;

		StatisticsChartResponse chartModels = new StatisticsChartResponse();
		// 現在時間
		LocalDate getMonth = LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), 1);
		int maxDayOfMonth = getMonth.lengthOfMonth();
		int nowMonth = LocalDate.now().getMonthValue();
		System.out.println("nowMonth  ：" + nowMonth);
		LocalDateTime startTime = LocalDateTime.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), 1, 00, 00, 00,
				00);
		LocalDateTime endTime = LocalDateTime.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), maxDayOfMonth,
				23, 59, 59, 99);

		// barChart
		// 日期區間每間會議室筆數
		List<BarChartData> barChartDataList = new ArrayList<BarChartData>();
		for (int j = 0; j < allRoomId.size(); j++) {
			if (allRoomId.get(j).getPortId().equals("KHH") && allRoomId.get(j).getOfficeId().equals("KH01")) {
				Long roomIdCount = meetingReserveRepository.countByReserveDateStartBetweenAndRoomId(startTime, endTime,
						allRoomId.get(j).getId());
				BarChartData barChartData = new BarChartData();
				barChartData.setRoomName(allRoomId.get(j).getRoomName());
				barChartData.setRoomNameCount(roomIdCount);
				barChartDataList.add(barChartData);
			}
		}
		chartModels.setBarChartDatas(barChartDataList);

		// 月份區間資料
		List<MeetingReserve> monthDatas = meetingReserveRepository.findByReserveDateStartBetween(startTime, endTime);

		for (int i = 0; i < monthDatas.size(); i++) {
			LocalDateTime localDate = monthDatas.get(i).getCreatedTime();
			// 把LocalDateTime轉Date
			ZoneId zoneId = ZoneId.systemDefault();
			ZonedDateTime zdt = localDate.atZone(zoneId);
			Date date = Date.from(zdt.toInstant());
			String reserve = monthDatas.get(i).getStatus().toString();
			String cancel = monthDatas.get(i).getStatus().toString();

			// 拿星期
			SimpleDateFormat sdf = new SimpleDateFormat("EEEE");
			week = sdf.format(date);

			// pieChartReserve,pieChartCancel
			if ("RESERVE".equals(reserve)) {

				reserveCount = reserveCount + 1;
			}
			if ("CANCEL".equals(cancel)) {
				cancelCount = cancelCount + 1;
			}

			// lineChartWeek
			switch (week) {
				case "星期一":
					mondayCount = mondayCount + 1;
					break;
				case "星期二":
					tuesdayCount = tuesdayCount + 1;
					break;
				case "星期三":
					wednesdayCount = wednesdayCount + 1;
					break;
				case "星期四":
					thursdayCount = thursdayCount + 1;
					break;
				case "星期五":
					fridayCount = fridayCount + 1;
					break;
			}

		}
		// 當前月份
		chartModels.setMonth(nowMonth);

		// pieChartReserve,pieChartCancelResult
		chartModels.setPieChartReserve(reserveCount);
		chartModels.setPieChartCancel(cancelCount);

		// lineChartWeekResult
		LineChartData lineChartData = new LineChartData();
		lineChartData.setMondayCount(mondayCount);
		lineChartData.setTuesdayCount(tuesdayCount);
		lineChartData.setWednesdayCount(wednesdayCount);
		lineChartData.setThursdayCount(thursdayCount);
		lineChartData.setFridayCount(fridayCount);
		chartModels.setLineChartDatas(lineChartData);

		return chartModels;
	}
}
