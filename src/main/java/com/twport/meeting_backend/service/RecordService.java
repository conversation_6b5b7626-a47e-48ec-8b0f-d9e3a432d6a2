package com.twport.meeting_backend.service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import com.lowagie.text.pdf.BaseFont;
import com.twport.meeting_backend.aop.RestApiException;
import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.entity.CompanyList;
import com.twport.meeting_backend.entity.DepartmentList;
import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.entity.MeetingSignIn;
import com.twport.meeting_backend.entity.OfficeList;
import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.enums.SignInType;
import com.twport.meeting_backend.repository.AppUserRepository;
import com.twport.meeting_backend.repository.CompanyListRepository;
import com.twport.meeting_backend.repository.DepartmentListRepository;
import com.twport.meeting_backend.repository.MeetingReserveDepartmentRepository;
import com.twport.meeting_backend.repository.MeetingReserveRepository;
import com.twport.meeting_backend.repository.MeetingRoomRepository;
import com.twport.meeting_backend.repository.MeetingSignRepository;
import com.twport.meeting_backend.repository.OfficeListRepository;
import com.twport.meeting_backend.utils.DateUtil;
import com.twport.meeting_backend.utils.UserHolder;
import com.twport.meeting_backend.vo.dto.DepartmentListOptionDto;
import com.twport.meeting_backend.vo.dto.PdfSignedRecord;
import com.twport.meeting_backend.vo.dto.PdfSignedRecordConfig;
import com.twport.meeting_backend.vo.dto.RecordQueryDTO;
import com.twport.meeting_backend.vo.dto.RecordQueryPageModel;
import com.twport.meeting_backend.vo.dto.SelectOption;
import com.twport.meeting_backend.vo.request.RecordQueryRequest;
import com.twport.meeting_backend.vo.request.RecordSignFileRequest;
import com.twport.meeting_backend.vo.request.RecordSignedDetailRequest;
import com.twport.meeting_backend.vo.request.RecordSignedEditInitRequest;
import com.twport.meeting_backend.vo.request.RecordSignedEditRequest;
import com.twport.meeting_backend.vo.response.RecordInitResponse;
import com.twport.meeting_backend.vo.response.RecordSignedDetailResponse;
import com.twport.meeting_backend.vo.response.RecordSignedEditInitResponse;
import com.twport.meeting_backend.vo.response.RecordSignedPdfResponse;
import com.twport.meeting_backend.vo.response.base.PageResponse;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @datetime 2024年7月26日
 * @class RecordService
 * @description 會議室查詢Service
 */
@Service
@Slf4j
public class RecordService {

	private final MeetingRoomRepository meetingRoomRepository;

	private final AppUserRepository userRepository;

	private final DepartmentListRepository departmentListRepository;

	private final CompanyListRepository companyListRepository;

	private final MeetingReserveRepository meetingReserveRepository;

	private final MeetingSignRepository meetingSignRepository;

	private final OfficeListRepository officeListRepository;

	private final DateUtil dateUtils;

	private final TemplateEngine templateEngine;

	public RecordService(
			MeetingRoomRepository meetingRoomRepository, AppUserRepository userRepository,
			DepartmentListRepository departmentListRepository, MeetingReserveRepository meetingReserveRepository,
			MeetingReserveDepartmentRepository meetingReserveDepartmentRepository,
			MeetingSignRepository meetingSignRepository, OfficeListRepository officeListRepository,
			CompanyListRepository companyListRepository, DateUtil dateUtils, TemplateEngine templateEngine) {
		this.meetingRoomRepository = meetingRoomRepository;
		this.userRepository = userRepository;
		this.departmentListRepository = departmentListRepository;
		this.meetingReserveRepository = meetingReserveRepository;
		this.meetingSignRepository = meetingSignRepository;
		this.officeListRepository = officeListRepository;
		this.companyListRepository = companyListRepository;
		this.dateUtils = dateUtils;
		this.templateEngine = templateEngine;
	}

	/**
	 * 取得樓層資訊
	 */
	public RecordInitResponse getAllDefaultSelect() {
		// 只獲取啟用中且未刪除的會議室
		List<MeetingRoom> meetingRooms = meetingRoomRepository.findByRoomEnable(true);
		// 按樓層和會議室名稱排序
		meetingRooms.sort(Comparator.comparing(MeetingRoom::getFloor).thenComparing(MeetingRoom::getRoomName));

		List<SelectOption> floorSelOpts = new ArrayList<>();
		List<SelectOption> allRoomSelects = new ArrayList<>();

		for (MeetingRoom meetingRoom : meetingRooms) {
			if (meetingRoom.getPortId().equals("KHH") && meetingRoom.getOfficeId().equals("KH01")) {

				Integer floor = meetingRoom.getFloor();
				String floorStr = floor.toString();
				String floorDisplay = floorStr + "F";

				String roomStr = meetingRoom.getId();
				String roomDisplay = meetingRoom.getRoomName();

				SelectOption floorOption = null;
				for (SelectOption option : floorSelOpts) {
					if (option.getValue().equals(floorStr)) {
						floorOption = option;
						break;
					}
				}

				if (floorOption == null) {
					floorOption = new SelectOption(floorStr, floorDisplay);
					floorSelOpts.add(floorOption);
				}
				floorOption.getSubOptions().add(new SelectOption(roomStr, roomDisplay));
				allRoomSelects.add(new SelectOption(roomStr, roomDisplay));
			}
		}

		List<SelectOption> departOption = departmentListRepository.findAllDepartmentLists("1").stream()
				.filter(depart -> depart.getCompanyId().toString().equals("1"))
				.map(department -> {
					return new SelectOption(String.valueOf(department.getId()),
							department.getDepartmentName() + "(" + department.getCompanyName() + ")");
				}).collect(Collectors.toList());

		RecordInitResponse response = new RecordInitResponse();
		response.setFloorSelects(floorSelOpts);
		response.setMeetingRoomSelects(allRoomSelects);
		response.setDepartmentSelects(departOption);

		log.info("options : {}", response);

		return response;
	}

	/**
	 * 取得Table列表
	 */
	public PageResponse<RecordQueryPageModel> getRecordTableData(RecordQueryRequest request) {
		// 初始化
		Pageable page = PageRequest.of(request.getCurrentPage(), request.getPageSize());
		// 資料查詢
		Page<RecordQueryDTO> recordPage = meetingReserveRepository.reservePaginationQuery(page,
				request.getDepartmentId(), request.getFloor(), request.getRoom(), request.getStartDate(),
				request.getEndDate(), request.getKeyword(), request.getSort());

		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
		String loginId = UserHolder.getCurrentUser().getId();

		List<RecordQueryPageModel> pageModels = new ArrayList<>();
		for (RecordQueryDTO result : recordPage.getContent()) {
			Boolean personal = !StringUtils.equals(loginId, result.getReserveId()) && result.isPersonal();
			RecordQueryPageModel pageModel = new RecordQueryPageModel();
			pageModel.setTheme(personal ? "不公開" : result.getTheme());
			pageModel.setRoomNameDisplay(result.getRoomName() + " " + "(" + result.getFloor() + "F)");
			pageModel.setHostUser(personal ? "不公開" : result.getHostFullName());
			pageModel.setDepartment(personal ? "不公開" : result.getDepartment());
			pageModel.setReserveUser(result.getReserveFullName());
			pageModel.setReserveUserPhone(result.getReservePhoneNumber());
			pageModel.setReserveDate(dateFormatter.format(result.getReserveDateStart()));
			pageModel.setReserveTime(timeFormatter.format(result.getReserveDateStart()) + " ~ "
					+ timeFormatter.format(result.getReserveDateEnd()));
			pageModel.setStatusDisplay(result.getStatus().getMessage());
			pageModel.setMeetingId(result.getMeetingId());

			pageModels.add(pageModel);
		}

		PageResponse<RecordQueryPageModel> pageResponse = new PageResponse<>();
		pageResponse.setTotalSize(Long.valueOf(recordPage.getTotalElements()).intValue());
		pageResponse.setPageSize(recordPage.getSize());
		pageResponse.setCurrentPage(recordPage.getNumber());
		pageResponse.setTotalPage(recordPage.getTotalPages());
		pageResponse.setContents(pageModels);

		return pageResponse;
	}

	public List<RecordSignedDetailResponse> getSignedDetails(RecordSignedDetailRequest request) {
		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
		List<RecordSignedDetailResponse> response = meetingSignRepository.findByReserveId(request.getReserveId())
				.stream().map(sign -> {
					RecordSignedDetailResponse signRecord = new RecordSignedDetailResponse();
					signRecord.setSignInId(sign.getId());
					signRecord.setDepartmentName(sign.getDepartmentName());
					signRecord.setJobTitle(sign.getJobTitle());
					signRecord.setType(sign.getSignInType().getMessage());
					signRecord.setImageUrl(sign.getSignInImageUrl());
					signRecord.setSignTime(dateFormatter.format(sign.getCreatedTime()));
					return signRecord;
				}).collect(Collectors.toList());

		return response;
	}

	/**
	 *
	 * <AUTHOR> Lin
	 * @datetime 2024年9月18日
	 * @method
	 * @param request
	 * @return
	 * @description 顯示PDF檔案排版(TODO 上線後需刪除)
	 */
	public RecordSignedPdfResponse signRecordFile(RecordSignFileRequest request) {
		MeetingReserve reserveInfo = Optional.ofNullable(meetingReserveRepository.findById(request.getReserveId()))
				.orElseThrow(NullPointerException::new).get();

		MeetingRoom roomInfo = Optional.ofNullable(meetingRoomRepository.findById(reserveInfo.getRoomId()))
				.orElseThrow(NullPointerException::new).get();

		OfficeList officeInfo = Optional.ofNullable(officeListRepository.findById(roomInfo.getOfficeId()))
				.orElseThrow(NullPointerException::new).get();

		// AppUser appUserInfo =
		// Optional.ofNullable(userRepository.findById(reserveInfo.getHostId()))
		// .orElseThrow(NullPointerException::new).get();

		List<PdfSignedRecord> recordList = meetingSignRepository.findByReserveId(request.getReserveId()).stream()
				.map(sign -> {
					PdfSignedRecord signRecord = new PdfSignedRecord();
					if (sign.getDepartmentId() != null) {
						DepartmentList department = Optional
								.ofNullable(departmentListRepository.findById(sign.getDepartmentId()))
								.orElseThrow(NullPointerException::new).get();
						CompanyList company = Optional
								.ofNullable(companyListRepository.findById(department.getCompanyId()))
								.orElseThrow(NullPointerException::new).get();
						signRecord.setDepartment(department.getName());
						signRecord.setCompany(company.getName());
					} else {
						signRecord.setDepartment(sign.getDepartmentName());
					}

					signRecord.setJobTitle(sign.getJobTitle());
					signRecord.setSignType(sign.getSignInType().toString());

					if (sign.getSignInType() == SignInType.SIGNIN) {
						signRecord.setSignName(sign.getSignInImageUrl());
					} else {
						AppUser signUser = Optional.ofNullable(userRepository.findById(sign.getUserId()))
								.orElseThrow(NullPointerException::new).get();
						signRecord.setSignName(signUser.getFullName());
					}
					return signRecord;
				}).collect(Collectors.toList());

		Map<PdfSignedRecordConfig, List<PdfSignedRecord>> recordMap = new HashMap<>();
		recordList.stream().collect(Collectors.groupingBy(PdfSignedRecord::getDepartment))
				.entrySet().stream().forEach(item -> {
					PdfSignedRecordConfig config = new PdfSignedRecordConfig();
					config.setDepartmentName(item.getKey());
					config.setCompanyName(item.getValue().get(0).getCompany());
					config.setRowspanSize((item.getValue().size() % 2) == 0 ? (item.getValue().size() / 2)
							: (item.getValue().size() / 2) != 0 ? (item.getValue().size() / 2) + 1 : 1);
					recordMap.put(config, item.getValue());
				});

		recordMap.forEach((k, v) -> {
			log.info("record key : {}", k);
			log.info("record value : {}", v);
		});

		RecordSignedPdfResponse response = new RecordSignedPdfResponse();
		response.setTheme(reserveInfo.getTheme());
		response.setReserveTime(dateUtils.convertToChineseFormat(reserveInfo.getReserveDateStart()));
		response.setReserveLocate(officeInfo.getName() + " " + roomInfo.getRoomName());
		response.setHostName(reserveInfo.getHostName());
		response.setSignRecord(recordMap);

		return response;
	}

	public void createSignInRecordFile(RecordSignFileRequest request, HttpServletResponse response) {
		MeetingReserve reserveInfo = Optional.ofNullable(meetingReserveRepository.findById(request.getReserveId()))
				.orElseThrow(NullPointerException::new).get();

		MeetingRoom roomInfo = Optional.ofNullable(meetingRoomRepository.findById(reserveInfo.getRoomId()))
				.orElseThrow(NullPointerException::new).get();

		OfficeList officeInfo = Optional.ofNullable(officeListRepository.findById(roomInfo.getOfficeId()))
				.orElseThrow(NullPointerException::new).get();

		List<PdfSignedRecord> recordList = meetingSignRepository.findByReserveId(request.getReserveId()).stream()
				.map(sign -> {
					PdfSignedRecord signRecord = new PdfSignedRecord();
					if (sign.getDepartmentId() != null) {
						DepartmentList department = Optional
								.ofNullable(departmentListRepository.findById(sign.getDepartmentId()))
								.orElseThrow(NullPointerException::new).get();
						CompanyList company = Optional
								.ofNullable(companyListRepository.findById(department.getCompanyId()))
								.orElseThrow(NullPointerException::new).get();
						signRecord.setDepartment(department.getName());
						signRecord.setCompany(company.getName());
					} else {
						signRecord.setDepartment(sign.getDepartmentName());
					}

					signRecord.setJobTitle(sign.getJobTitle());
					signRecord.setSignType(sign.getSignInType().toString());

					if (sign.getSignInType() == SignInType.SIGNIN) {
						try {
							File signIng = new File(sign.getSignInImageUrl());
							byte[] content = FileUtils.readFileToByteArray(signIng);
							signRecord.setSignName(Base64.getEncoder().encodeToString(content));
						} catch (IOException e) {
							throw new RestApiException(ResponseStatusCode.SEARCH_FAILED);
						};
					} else {
						AppUser signUser = Optional.ofNullable(userRepository.findById(sign.getUserId()))
								.orElseThrow(NullPointerException::new).get();
						signRecord.setSignName(signUser.getFullName());
					}
					return signRecord;
				}).collect(Collectors.toList());

		Map<PdfSignedRecordConfig, List<PdfSignedRecord>> recordMap = new HashMap<>();
		recordList.stream().collect(Collectors.groupingBy(PdfSignedRecord::getDepartment))
				.entrySet().stream().forEach(item -> {
					PdfSignedRecordConfig config = new PdfSignedRecordConfig();
					config.setDepartmentName(item.getKey());
					config.setCompanyName(item.getValue().get(0).getCompany());
					config.setRowspanSize((item.getValue().size() % 2) == 0 ? (item.getValue().size() / 2)
							: (item.getValue().size() / 2) != 0 ? (item.getValue().size() / 2) + 1 : 1);
					recordMap.put(config, item.getValue());
				});

		Context context = new Context();
		context.setVariable("theme", reserveInfo.getTheme());
		context.setVariable("reserveTime", dateUtils.convertToChineseFormat(reserveInfo.getReserveDateStart()));
		context.setVariable("reserveLocate", officeInfo.getName() + " " + roomInfo.getRoomName());
		context.setVariable("hostName", reserveInfo.getHostName());
		context.setVariable("signRecord", recordMap);

		String pdfContent = templateEngine.process("signRecord.html", context);

		String outputFolder = "thymeleaf.pdf";

		try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
			ITextRenderer renderer = new ITextRenderer();
			ITextFontResolver resolver = renderer.getFontResolver();
			resolver.addFont("static/fonts/NotoSansTC.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
			resolver.addFont("static/fonts/NotoSerifTC.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
			renderer.setDocumentFromString(pdfContent);
			renderer.layout();
			renderer.createPDF(outputStream);

			outputStream.close();
			response.setContentType("application/pdf");
			response.setHeader("Content-Disposition",
					"attachment; filename*=\"" + URLEncoder.encode(outputFolder, "UTF-8") + "\"");
			ByteArrayInputStream bit = new ByteArrayInputStream(outputStream.toByteArray());
			IOUtils.copy(bit, response.getOutputStream());
			response.flushBuffer();
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public RecordSignedEditInitResponse signRecordEditInit(RecordSignedEditInitRequest request) {
		MeetingSignIn signRecord = Optional.ofNullable(meetingSignRepository.findById(request.getSignInId()))
				.orElseThrow(NullPointerException::new).get();
		List<DepartmentListOptionDto> departments = departmentListRepository.findAllDepartmentLists("1");

		List<SelectOption> companyOptions = departments.stream()
				.collect(Collectors.groupingBy(DepartmentListOptionDto::getCompanyId))
				.entrySet().stream().map(item -> {
					SelectOption option = new SelectOption(
							String.valueOf(item.getKey()),
							item.getValue().get(0).getCompanyName());

					List<SelectOption> subOptions = item.getValue()
							.stream().map(department -> {
								return new SelectOption(String.valueOf(department.getId()),
										department.getDepartmentName());
							}).collect(Collectors.toList());

					option.setSubOptions(subOptions);
					return option;
				}).collect(Collectors.toList());

		String selectCompanyId = departments.stream().filter(depart -> depart.getId() == signRecord.getDepartmentId())
				.map(depart -> {
					if (depart == null) {
						return null;
					} else {
						return String.valueOf(depart.getCompanyId());
					}
				}).collect(Collectors.joining());

		RecordSignedEditInitResponse response = new RecordSignedEditInitResponse();
		response.setCompanyOptions(companyOptions);
		response.setSignInId(signRecord.getId());
		response.setCompanyId(selectCompanyId);
		response.setDepartmentId(signRecord.getDepartmentId());
		response.setDepartmentName(signRecord.getDepartmentName());
		response.setJobTitle(signRecord.getJobTitle());
		response.setType(signRecord.getSignInType());

		try {
			if (signRecord.getSignInType() == SignInType.SIGNIN) {
				File signIng = new File(signRecord.getSignInImageUrl());
				byte[] content;
				content = FileUtils.readFileToByteArray(signIng);
				response.setSignInImageUrl(Base64.getEncoder().encodeToString(content));
			} else {
				AppUser signUser = Optional.ofNullable(userRepository.findById(signRecord.getUserId()))
						.orElseThrow(NullPointerException::new).get();
				response.setSignInUserName(signUser.getFullName());
			}
		} catch (IOException e) {
			throw new RestApiException(ResponseStatusCode.SEARCH_FAILED);
		}

		log.info("init response : {}", response);

		return response;
	}

	@Transactional
	public void signInEdit(RecordSignedEditRequest request) {
		MeetingSignIn signRecord = Optional.ofNullable(meetingSignRepository.findById(request.getSignInId()))
				.orElseThrow(NullPointerException::new).get();

		if (request.getDepartmentId().equals("other")) {
			signRecord.setDepartmentId(null);
			signRecord.setDepartmentName(request.getDepartmentName());
		} else {
			signRecord.setDepartmentId(request.getDepartmentId());
			DepartmentList depart = Optional.ofNullable(departmentListRepository.findById(signRecord.getDepartmentId()))
					.orElseThrow(NullPointerException::new).get();
			signRecord.setDepartmentName(depart.getName());
		}

		signRecord.setJobTitle(request.getJobTitle());

		meetingSignRepository.save(signRecord);
	}
}
