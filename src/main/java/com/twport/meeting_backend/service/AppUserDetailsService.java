package com.twport.meeting_backend.service;

import java.util.Optional;

import org.springframework.context.annotation.Profile;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.repository.AppUserRepository;
import com.twport.meeting_backend.security.AppUserDetails;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年7月22日 
 * @class AppUserDetailsService
 * @description 使用者登入
 */

@Service
@Profile("dev")
public class AppUserDetailsService implements UserDetailsService {

	private final AppUserRepository appUserRepository;
	
	public AppUserDetailsService(AppUserRepository appUserRepository) {
		this.appUserRepository = appUserRepository;
	}

	@Override
	public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
		Optional<AppUser> opt = appUserRepository.findByAccount(username);
		
		if(opt.isPresent()) {
			System.out.println(opt.get().getAccount());
			System.out.println(opt.get().getPassword());
			return new AppUserDetails(opt.get());
		}
		
		throw new UsernameNotFoundException("查無使用者");
	}

}
