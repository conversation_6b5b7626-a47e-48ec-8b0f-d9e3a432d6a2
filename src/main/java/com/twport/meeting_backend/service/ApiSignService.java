package com.twport.meeting_backend.service;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.twport.meeting_backend.aop.RestApiException;
import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.entity.MeetingReserveDepartment;
import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.entity.MeetingSignIn;
import com.twport.meeting_backend.enums.RecordStatus;
import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.enums.SignInType;
import com.twport.meeting_backend.repository.AppUserRepository;
import com.twport.meeting_backend.repository.MeetingReserveDepartmentRepository;
import com.twport.meeting_backend.repository.MeetingReserveRepository;
import com.twport.meeting_backend.repository.MeetingRoomRepository;
import com.twport.meeting_backend.repository.MeetingSignRepository;
import com.twport.meeting_backend.utils.DateUtil;
import com.twport.meeting_backend.vo.dto.MeetingReserveNowQueryDto;
import com.twport.meeting_backend.vo.dto.SelectOption;
import com.twport.meeting_backend.vo.request.ApiSignDataRequest;
import com.twport.meeting_backend.vo.request.ApiSignMeetingQueryRequest;
import com.twport.meeting_backend.vo.request.ApiSignUploadSignDataRequest;
import com.twport.meeting_backend.vo.request.MeetingEndAndExtendRequest;
import com.twport.meeting_backend.vo.response.ApiSignDataResponse;
import com.twport.meeting_backend.vo.response.ApiSignMeetingQueryResponse;
import com.twport.meeting_backend.vo.response.ApiSignRoomResponse;
import com.twport.meeting_backend.vo.response.base.ErrorMessage;

import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ApiSignService {

	@Value("${uploadfile.location}")
	private String uploadFileLocation;

	private final MeetingReserveRepository reserveRepository;

	private final MeetingReserveDepartmentRepository reserveDepartRepository;

	private final MeetingRoomRepository roomRepository;

	private final MeetingSignRepository signInRepository;

	private final AppUserRepository appUserRepository;

	private final DateUtil dateUtil;

	public ApiSignService(MeetingReserveRepository reserveRepository,
			MeetingReserveDepartmentRepository reserveDepartRepository, MeetingRoomRepository roomRepository,
			MeetingSignRepository signInRepository, AppUserRepository appUserRepository, DateUtil dateUtil) {
		this.reserveRepository = reserveRepository;
		this.reserveDepartRepository = reserveDepartRepository;
		this.roomRepository = roomRepository;
		this.signInRepository = signInRepository;
		this.appUserRepository = appUserRepository;
		this.dateUtil = dateUtil;
	}

	public ApiSignMeetingQueryResponse nowReserveQuery(ApiSignMeetingQueryRequest request) {
		// 初始化
		ApiSignMeetingQueryResponse response = new ApiSignMeetingQueryResponse();
		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
		// 查詢會議室
		MeetingRoom room = roomRepository.findByRoomCode(request.getMeetingRoomCode());
		response.setRoomName(room.getRoomName());
		// 查詢會議
		List<MeetingReserveNowQueryDto> results = reserveRepository.findNowMeetingReserve(room.getId());
		if (results != null && !CollectionUtils.isEmpty(results)) {
			MeetingReserveNowQueryDto nowResult = results.get(0);
			Boolean personal = nowResult.getPersonal();
			List<MeetingReserveDepartment> departsList = reserveDepartRepository
					.findByReserveId(nowResult.getReserveId());
			Long personSignedCounts = signInRepository.countByReserveId(nowResult.getReserveId());
			response.setReserveId(nowResult.getReserveId());
			response.setMeeting(true);
			response.setHostName(personal ? "不公開" : nowResult.getHostName());
			response.setTheme(personal ? "不公開" : nowResult.getTheme());
			response.setMembers(nowResult.getMembers());
			response.setSignedCounts(personSignedCounts);
			response.setReserveDate(dateFormatter.format(nowResult.getReserveDateStart()));
			response.setReserveStartTime(timeFormatter.format(nowResult.getReserveDateStart()));
			response.setReserveEndTime(timeFormatter.format(nowResult.getReserveDateEnd()));
			response.setDepartmentsList(departsList.stream().map(depart -> {
				return new SelectOption(String.valueOf(depart.getDepartmentId()), depart.getDepartmentName());
			}).collect(Collectors.toList()));
		} else {
			response.setMeeting(false);
		}
		return response;
	}

	/**
	 * 上傳簽名檔
	 * 
	 * @param request 簽名檔
	 * @throws IOException
	 */
	@Transactional
	public void signDataUpload(ApiSignUploadSignDataRequest request) throws IOException {
		// 查詢 會議
		Optional<MeetingReserve> reserveOpt = reserveRepository.findById(request.getReserveId());
		if (!reserveOpt.isPresent()) {
			throw new RestApiException(ResponseStatusCode.INSERT_FAILED);
		}

		// 新增 簽名檔
		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
		MeetingSignIn signData = new MeetingSignIn();
		signData.setReserveId(request.getReserveId());
		if (request.getDepartmentId() != null) {
			signData.setDepartmentId(request.getDepartmentId());
		}
		signData.setDepartmentName(request.getDepartmentName());
		signData.setJobTitle(request.getJobTitle());
		signData.setSignInType(SignInType.SIGNIN);
		MeetingSignIn createSign = signInRepository.save(signData);

		// 資料初始化
		MeetingReserve reserve = reserveOpt.get();
		// 圖檔
		byte[] decodedBytes = Base64.getDecoder().decode(request.getImageData());
		// 會議室 ID
		String roomId = reserve.getRoomId();
		// 會議 ID
		String reserveId = String.valueOf(request.getReserveId());
		// 簽名檔 ID
		String signId = String.valueOf(createSign.getId());
		// 檔名
		String fileName = dateFormatter.format(LocalDateTime.now()) + roomId + reserveId + signId;
		// 檔案建立
		File file = new File(uploadFileLocation + fileName + ".png");
		try (FileOutputStream fo = new FileOutputStream(file)) {
			fo.write(decodedBytes);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		}
		// 檔案位置
		createSign.setSignInImageUrl(file.getAbsolutePath());
		signInRepository.save(createSign);
	}

	/*
	 * 查看簽到名單
	 */
	public List<ApiSignDataResponse> signDataQuery(ApiSignDataRequest request) {
		List<MeetingSignIn> nowResult = signInRepository.findByReserveId(request.getReserveId());
		DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
		int index = 1;
		if (nowResult != null) {
			List<ApiSignDataResponse> responseList = new ArrayList<>();
			for (MeetingSignIn item : nowResult) {
				ApiSignDataResponse response = new ApiSignDataResponse();
				response.setIndex(index);
				response.setDepartmentName(item.getDepartmentName());
				response.setJobTitle(item.getJobTitle());
				response.setSignInType(item.getSignInType().toString());
				try {
					switch (item.getSignInType().toString()) {
						case "SIGNIN":
							File signIng = new File(item.getSignInImageUrl());
							byte[] content = FileUtils.readFileToByteArray(signIng);
							response.setSignInName(Base64.getEncoder().encodeToString(content));
							break;
						case "IDCARD":
							Optional<AppUser> username = appUserRepository.findById(item.getUserId());
							response.setSignInName(username.get().getFullName());
							break;
					}
					response.setUpdatedTime(timeFormatter.format(item.getUpdatedTime()));
					index++;
					responseList.add(response);
				} catch (IOException e) {
					log.error("error: ", e);
				}
			}

			return responseList;
		}
		return null;
	}

	// 會議結束
	public void StatusUpdate(MeetingEndAndExtendRequest request) {
		if (request.getReserveId() != null) {
			Optional<MeetingReserve> reserveDataOpt = reserveRepository.findById(request.getReserveId());
			MeetingReserve reserveData = null;
			if (reserveDataOpt.isPresent()) {
				reserveData = reserveDataOpt.get();
			} else {
				throw new RestApiException(ResponseStatusCode.UPDATE_FAILED);
			}
			if (reserveData.getStatus().isConduct()) {
				reserveData.setStatus(RecordStatus.END);
				reserveData.setReserveDateEnd(dateUtil.getMeetingMinute(LocalDateTime.now()));
				reserveRepository.save(reserveData);
			} else {
				throw new RestApiException(ResponseStatusCode.UPDATE_FAILED);
			}
		} else {
			throw new RestApiException(ResponseStatusCode.UPDATE_FAILED);
		}
	}

	// 會議延長
	public void extended(MeetingEndAndExtendRequest request) {
		// 初始化
		List<ErrorMessage> errors = new ArrayList<>();
		Optional<MeetingReserve> meetingReserveOpt = reserveRepository.findById(request.getReserveId());
		MeetingReserve meetingReserve = meetingReserveOpt.get();
		LocalDateTime bTime = meetingReserve.getReserveDateEnd();
		LocalDateTime starTime = meetingReserve.getReserveDateStart();
		LocalDateTime endTime = meetingReserve.getReserveDateEnd();
		String roomId = meetingReserve.getRoomId();
		LocalTime newTime = request.getExtendTime();
		LocalDate today = LocalDate.of(bTime.getYear(), bTime.getMonth(), bTime.getDayOfMonth());
		LocalDateTime newEndTime = LocalDateTime.of(today, newTime);
		if (newEndTime.isAfter(starTime) && newEndTime.isAfter(endTime)) {
			List<MeetingReserve> resultList = reserveRepository.findIfMeetingExist(bTime, newEndTime, roomId, null);
			// 會議延長(更新)
			if (!CollectionUtils.isEmpty(resultList)) {
				throw new RestApiException(ResponseStatusCode.UPDATE_FAILED);
			} else {
				meetingReserve.setReserveDateEnd(newEndTime);
				reserveRepository.save(meetingReserve);
			}
		} else {
			errors.add(new ErrorMessage("signError", "延長會議時間不可早於會議結束時間"));
			throw new RestApiException(ResponseStatusCode.UPDATE_FAILED, errors);
		}
	}

	// 會議室清單
	public List<ApiSignRoomResponse> meetingRoomQuery() {
		// 初始化
		List<ApiSignRoomResponse> roomList = new ArrayList<ApiSignRoomResponse>();
		// 查詢會議室
		List<MeetingRoom> allRoomId = roomRepository.findByRoomEnable(true);
		// 嵌入資料
		allRoomId.forEach(room -> {
			if (room.getPortId().equals("KHH") && room.getOfficeId().equals("KH01")) {
				ApiSignRoomResponse roomName = new ApiSignRoomResponse();
				roomName.setRoomId(room.getId());
				roomName.setRoomCode(room.getRoomCode());
				roomName.setRoomName(room.getRoomName());
				roomList.add(roomName);
			}
		});
		// 排序
		roomList.sort(Comparator.comparing(ApiSignRoomResponse::getRoomName));
		return roomList;
	}
}
