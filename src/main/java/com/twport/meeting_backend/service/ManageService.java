package com.twport.meeting_backend.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.entity.OfficeList;
import com.twport.meeting_backend.entity.PortList;
import com.twport.meeting_backend.repository.MeetingRoomRepository;
import com.twport.meeting_backend.repository.OfficeListRepository;
import com.twport.meeting_backend.repository.PortListRepository;
import com.twport.meeting_backend.vo.dto.SelectOption;
import com.twport.meeting_backend.vo.request.ManageAddRequest;
import com.twport.meeting_backend.vo.request.ManageDeleteRequest;
import com.twport.meeting_backend.vo.request.ManageQueryRequest;
import com.twport.meeting_backend.vo.request.ManageUpdateRequest;
import com.twport.meeting_backend.vo.response.ManageAddResponse;
import com.twport.meeting_backend.vo.response.ManageQueryResponse;
import com.twport.meeting_backend.vo.response.base.PageResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @datetime 2024年7月22日
 * @class MeetingRoomsManageService
 * @description 會議室管理相關功能
 */
@Service
@Slf4j
public class ManageService {

	private final PortListRepository portListRepository;

	private final OfficeListRepository officeListRepository;

	private final MeetingRoomRepository meetingRoomRepository;

	public ManageService(MeetingRoomRepository meetingRoomRepository,
			PortListRepository portListRepository,
			OfficeListRepository officeListRepository) {
		this.meetingRoomRepository = meetingRoomRepository;
		this.portListRepository = portListRepository;
		this.officeListRepository = officeListRepository;
	}

	public List<SelectOption> getSelectOption() {
		// 查詢全部港別
		List<PortList> portList = portListRepository.findAll();

		// 全部條件的下拉選單
		List<SelectOption> alloOptionList = portList.stream().map(port -> {
			// 港別選單
			SelectOption portOption = new SelectOption(port.getId(), port.getName());

			// 查詢港別下的全部辦公室別
			List<OfficeList> officeList = officeListRepository.findByPortId(port.getId());

			// 將辦公室別選單設為港別的聯動子選單
			portOption.setSubOptions(officeList.stream().map(office -> {
				SelectOption officeOption = new SelectOption(office.getId(), office.getName());

				// 查詢辦公室別下的全部會議室（排除已刪除的）
				List<MeetingRoom> roomList = meetingRoomRepository.findByOfficeId(office.getId());;
				if (!roomList.isEmpty()) {
					// 將相同樓層的會議室分類出來
					Map<Integer, List<MeetingRoom>> groupMap = roomList.stream()
							.collect(Collectors.groupingBy(MeetingRoom::getFloor));

					// 將樓層選單設為辦公室別選單的聯動子選單
					officeOption.setSubOptions(groupMap.entrySet().stream().map(e -> {
						SelectOption floorOption = new SelectOption(String.valueOf(e.getKey()),
								String.valueOf(e.getKey()) + "樓");

						// 將會議室選單設為樓層選單的連動子選單
						floorOption.setSubOptions(e.getValue().stream().map(room -> {
							return new SelectOption(String.valueOf(room.getId()), room.getRoomName());
						}).collect(Collectors.toList()));

						return floorOption;
					}).collect(Collectors.toList()));
				}

				return officeOption;
			}).collect(Collectors.toList()));

			return portOption;
		}).collect(Collectors.toList());

		return alloOptionList;
	}

	public PageResponse<ManageQueryResponse> query(ManageQueryRequest request) {
		// 初始化
		Pageable page = PageRequest.of(request.getCurrentPage(), request.getPageSize());
		// 資料查詢
		Page<ManageQueryResponse> roomPage = meetingRoomRepository.paginationQuery(page, request.getPortId(),
				request.getOfficeId(),
				request.getFloor(), request.getRoomId(), request.getKeyword(), request.getSort());
		// 回傳資料
		PageResponse<ManageQueryResponse> pageResponse = new PageResponse<>();
		pageResponse.setContents(roomPage.getContent());
		pageResponse.setCurrentPage(roomPage.getNumber());
		pageResponse.setPageSize(roomPage.getSize());
		pageResponse.setTotalPage(roomPage.getTotalPages());
		pageResponse.setTotalSize(Long.valueOf(roomPage.getTotalElements()).intValue());

		log.debug("page info : {}", pageResponse);

		return pageResponse;
	}

	/**
	 * 會議室新增
	 *
	 * @param request
	 * @return
	 */
	@Transactional
	public ManageAddResponse add(ManageAddRequest request) {
		MeetingRoom room = new MeetingRoom();
		room.setPortId(request.getPortId());
		room.setOfficeId(request.getOfficeId());
		room.setRoomName(request.getRoomName());
		room.setFloor(request.getFloor());
		room.setCapacity(request.getCapacity());
		room.setOnlineMeeting(request.getOnlineMeeting());
		room.setEnvDevice(request.getEnvDevice());
		room.setRoomEnable(request.getRoomEnable());

		MeetingRoom createRoom = meetingRoomRepository.save(room);
		createRoom.setRoomCode(createRoom.getPortId() +
				createRoom.getOfficeId() +
				createRoom.getId());
		meetingRoomRepository.save(createRoom);

		PortList portList = Optional.ofNullable(portListRepository.findById(createRoom.getPortId()))
				.orElseThrow(NullPointerException::new).get();

		OfficeList officeList = Optional.ofNullable(officeListRepository.findById(createRoom.getOfficeId()))
				.orElseThrow(NullPointerException::new).get();

		ManageAddResponse response = new ManageAddResponse();
		response.setPortName(portList.getName());
		response.setOfficeName(officeList.getName());
		response.setRoomName(createRoom.getRoomName());
		response.setFloor(createRoom.getFloor());
		response.setCapacity(createRoom.getCapacity());
		response.setRoomCode(createRoom.getRoomCode());
		response.setRoomEnable(createRoom.isRoomEnable());
		response.setOnlineMeeting(createRoom.isOnlineMeeting());
		response.setEnvDevice(createRoom.isEnvDevice());

		return response;
	}

	@Transactional
	public void delete(ManageDeleteRequest request) {
		MeetingRoom room = Optional.ofNullable(meetingRoomRepository.findById(request.getRoomId()))
				.orElseThrow(NullPointerException::new).get();
		room.setRoomEnable(false);
		room.setIsDel(true);
		meetingRoomRepository.save(room);
	}

	@Transactional
	public void update(ManageUpdateRequest request) {
		MeetingRoom room = Optional.ofNullable(meetingRoomRepository.findById(request.getRoomId()))
				.orElseThrow(NullPointerException::new).get();

		room.setRoomName(request.getRoomName());
		room.setRoomEnable(request.getRoomEnable());
		room.setCapacity(request.getCapacity());
		room.setEnvDevice(request.getEnvDeviceEnable());
		room.setOnlineMeeting(request.getOnlineMeetingEnable());
		meetingRoomRepository.save(room);
	}
}
