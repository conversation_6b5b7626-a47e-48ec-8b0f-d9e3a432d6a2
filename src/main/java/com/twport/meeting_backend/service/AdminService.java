package com.twport.meeting_backend.service;

import java.text.MessageFormat;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.twport.meeting_backend.vo.response.SsoTokenResponse;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AdminService {
	
	@Value("${admin.sso.url}")
	private String ssoUrl;
	
	@Value("${admin.backup.sso.url}")
	private String backupSsoUrl;
	
	@Value("${admin.sso.redirect.url}")
	private String redirectUrl;
	
	private String urlFormat = "{0}?redirect_url={1}";
	
	@Value("${admin.token.validate.url}")
	private String tokenValidateUrl;
	
	@Value("${admin.access.account}")
	private String adminAccessAccount;
	
	private final RestTemplate restTemplate;
		
	public AdminService(RestTemplate restTemplate) {
		this.restTemplate = restTemplate;
	}
	
	public String getSsoUrl() {
		String url = MessageFormat.format(urlFormat, ssoUrl, redirectUrl);
		try {
			ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<String>(null, new HttpHeaders()), String.class);
			HttpStatusCode statusCode = responseEntity.getStatusCode();
			if (!statusCode.is4xxClientError() || !statusCode.is5xxServerError()) {
				return url;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		// 改連線為備用SSO登入
		url = MessageFormat.format(urlFormat, backupSsoUrl, redirectUrl);
		try {
			ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<String>(null, new HttpHeaders()), String.class);
			HttpStatusCode statusCode = responseEntity.getStatusCode();
			if (!statusCode.is4xxClientError() || !statusCode.is5xxServerError()) {
				return url;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return "";
	}
	
	public String validateAuth(String token) {
		String account = "";
		SsoTokenResponse ssoTokenResponse = validateToken(token);
		if (ssoTokenResponse != null) {
			for (String acc : adminAccessAccount.split(",")) {
				if (acc.equals(ssoTokenResponse.getAccountInfo().getAccount())) {
					account = acc;
				}
			}
		}
		
		return account;
	}
	
	private SsoTokenResponse validateToken(String token) {
		log.debug("sso token : {}", token);
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setBearerAuth(token);
		HttpEntity<Void> httpEntity = new HttpEntity<>(headers);
		
		ResponseEntity<SsoTokenResponse> responseEntity = restTemplate.exchange(tokenValidateUrl, HttpMethod.GET, httpEntity, SsoTokenResponse.class);
		
		log.debug("token validate response : {}", responseEntity.getBody());
				
		SsoTokenResponse ssoTokenResponse = responseEntity.getBody();
		
		return ssoTokenResponse;
	} 
	
}
