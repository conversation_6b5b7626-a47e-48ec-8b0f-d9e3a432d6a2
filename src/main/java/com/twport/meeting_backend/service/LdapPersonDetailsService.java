package com.twport.meeting_backend.service;

import java.util.Optional;

import org.springframework.context.annotation.Profile;
import org.springframework.ldap.query.LdapQuery;
import org.springframework.ldap.query.LdapQueryBuilder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.entity.ldap.Person;
import com.twport.meeting_backend.repository.AppUserRepository;
import com.twport.meeting_backend.repository.ldap.LdapPersonRepository;
import com.twport.meeting_backend.security.LdapUserDetails;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Profile({"frontend"})
public class LdapPersonDetailsService implements UserDetailsService {
	
	private final LdapPersonRepository personRepository;
	private final AppUserRepository appUserRepository;

	public LdapPersonDetailsService(
			LdapPersonRepository personRepository,
			AppUserRepository appUserRepository) {
		this.personRepository = personRepository;
		this.appUserRepository = appUserRepository;
	}

	@Override
	public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
		log.info("ldap user name : {}", username);
		
		LdapQuery query = LdapQueryBuilder.query().where("sAMAccountName").is(username);
		Optional<Person> personOpt = personRepository.findOne(query);
		log.info("ldap personOpt : {}", personOpt);
		log.info("ldap personOpt is present : {}", personOpt.isPresent());
		
		if(personOpt.isPresent()) {
			log.info("single person : {}", personOpt.get());
			Optional<AppUser> appUserOpt = appUserRepository.findByAccount(personOpt.get().getSAMAccountName());
			
			if(appUserOpt.isPresent()) {
				LdapUserDetails details = new LdapUserDetails(personOpt.get(), appUserOpt.get());
				log.info("ldap details : {}", details);
				return details;
			}
			
			log.info("not find person");
			throw new UsernameNotFoundException("查無使用者");
		}else {
			log.info("not find person");
			throw new UsernameNotFoundException("查無使用者");
		}
	}
}
