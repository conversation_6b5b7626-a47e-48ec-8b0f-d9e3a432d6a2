package com.twport.meeting_backend.service;

import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.twport.meeting_backend.aop.RestApiException;
import com.twport.meeting_backend.entity.AppUser;
import com.twport.meeting_backend.entity.CompanyList;
import com.twport.meeting_backend.entity.DepartmentList;
import com.twport.meeting_backend.entity.MeetingReserve;
import com.twport.meeting_backend.entity.MeetingReserveDepartment;
import com.twport.meeting_backend.entity.MeetingRoom;
import com.twport.meeting_backend.enums.MeetingMode;
import com.twport.meeting_backend.enums.RecordStatus;
import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.enums.SituationMode;
import com.twport.meeting_backend.repository.AppUserRepository;
import com.twport.meeting_backend.repository.CompanyListRepository;
import com.twport.meeting_backend.repository.DepartmentListRepository;
import com.twport.meeting_backend.repository.MeetingReserveDepartmentRepository;
import com.twport.meeting_backend.repository.MeetingReserveRepository;
import com.twport.meeting_backend.repository.MeetingRoomRepository;
import com.twport.meeting_backend.utils.DateUtil;
import com.twport.meeting_backend.utils.RestApiUtils;
import com.twport.meeting_backend.utils.UserHolder;
import com.twport.meeting_backend.vo.dto.DepartmentListOptionDto;
import com.twport.meeting_backend.vo.dto.ReserveQueryDTO;
import com.twport.meeting_backend.vo.dto.ReserveQueryPageModel;
import com.twport.meeting_backend.vo.dto.ReserveTimeDetails;
import com.twport.meeting_backend.vo.dto.ReserveTimeQueryDTO;
import com.twport.meeting_backend.vo.dto.SelectOption;
import com.twport.meeting_backend.vo.request.CusuApiRequest;
import com.twport.meeting_backend.vo.request.ReserveAddRequest;
import com.twport.meeting_backend.vo.request.ReserveCompanyChangeRequest;
import com.twport.meeting_backend.vo.request.ReserveDeleteRequest;
import com.twport.meeting_backend.vo.request.ReserveDepartmentChangeRequest;
import com.twport.meeting_backend.vo.request.ReserveDetailRequest;
import com.twport.meeting_backend.vo.request.ReserveQueryDayReserveRequest;
import com.twport.meeting_backend.vo.request.ReserveQueryRequest;
import com.twport.meeting_backend.vo.request.ReserveTimeQueryRequest;
import com.twport.meeting_backend.vo.request.ReserveUpdateRequest;
import com.twport.meeting_backend.vo.response.CusuApiResponse;
import com.twport.meeting_backend.vo.response.ReserveCompanyChangeResponse;
import com.twport.meeting_backend.vo.response.ReserveDepartmentChangeResponse;
import com.twport.meeting_backend.vo.response.ReserveDetailResponse;
import com.twport.meeting_backend.vo.response.ReserveInitResponse;
import com.twport.meeting_backend.vo.response.ReserveQueryDayReserveResponse;
import com.twport.meeting_backend.vo.response.ReserveQueryResponse;
import com.twport.meeting_backend.vo.response.ReserveTimeQueryResponse;
import com.twport.meeting_backend.vo.response.base.ErrorMessage;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @datetime 2024年7月26日
 * @class Service
 * @description 會議室預約系統 Service
 */
@Service
@Slf4j
public class ReserveService {

	private final DateUtil dateUtil;

	private final AppUserRepository userRepository;

	private final CompanyListRepository companyListRepository;

	private final DepartmentListRepository departmentListRepository;

	private final MeetingReserveRepository meetingReserveRepository;

	private final MeetingReserveDepartmentRepository meetingReserveDepartmentRepository;

	private final MeetingRoomRepository meetingRoomRepository;

	private final RestApiUtils restApiUtils;

	DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
	DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");

	public ReserveService(AppUserRepository userRepository, CompanyListRepository companyListRepository,
			DepartmentListRepository departmentListRepository, MeetingReserveRepository meetingReserveRepository,
			MeetingReserveDepartmentRepository meetingReserveDepartmentRepository,
			MeetingRoomRepository meetingRoomRepository, DateUtil dateUtil, RestApiUtils restApiUtils) {
		this.userRepository = userRepository;
		this.companyListRepository = companyListRepository;
		this.departmentListRepository = departmentListRepository;
		this.meetingReserveRepository = meetingReserveRepository;
		this.meetingReserveDepartmentRepository = meetingReserveDepartmentRepository;
		this.meetingRoomRepository = meetingRoomRepository;
		this.dateUtil = dateUtil;
		this.restApiUtils = restApiUtils;
	}

	/**
	 * 取得選單資訊
	 */
	public ReserveInitResponse getAllDefaultSelect() {
		// 資料初始化
		List<SelectOption> meetingRoomSelects = new ArrayList<>();
		// 查詢會議室
		List<MeetingRoom> meetingRooms = meetingRoomRepository.findAllByOrderByFloorAscRoomNameAsc();
		// 資料邏輯
		meetingRooms.stream()
				.filter(room -> StringUtils.equals("KHH", room.getPortId()) && // 港別 = 高雄港
						StringUtils.equals("KH01", room.getOfficeId()) && // 辦公室別 = 高雄港旅運中心
						room.isRoomEnable()) // 使否啟用
				.forEach(room -> {
					// ID
					String id = room.getId();
					// 會議室
					String name = room.getRoomName();
					// 樓層
					Integer floor = room.getFloor();
					// 容納人數
					Long capacity = room.getCapacity();
					// 樓層篩選條件
					SelectOption floorOption = meetingRoomSelects.stream()
							.filter(data -> StringUtils.equals(data.getValue(), String.valueOf(floor))).findFirst()
							.orElse(null);
					if (floorOption == null) {
						floorOption = new SelectOption(floor.toString(), String.format("%d樓", floor));
						meetingRoomSelects.add(floorOption);
					}
					// 會議室篩選條件
					SelectOption roomOption = new SelectOption(id, name);
					roomOption.setTooltipTitle(String.format("%d人", capacity));
					floorOption.getSubOptions().add(roomOption);
				});
		// 公司下拉選單
		List<CompanyList> companyList = companyListRepository.findAll();
		List<SelectOption> companySelects = companyList.stream()
				.map(company -> {
					return new SelectOption(company.getId().toString(), company.getName());
				})
				.toList();
		// 與會單位下拉選單
		List<DepartmentListOptionDto> departmentListOptionDtos = departmentListRepository.findAllDepartmentLists("1");
		List<SelectOption> departmentSelects = departmentListOptionDtos.stream()
				.map(department -> {
					return new SelectOption(department.getId().toString(),
							String.format("%s(%s)", department.getDepartmentName(), department.getCompanyName()));
				}).toList();
		// 會議模式列表
		List<SelectOption> meetingModeSelects = Arrays.asList(MeetingMode.values()).stream()
				.map(mode -> {
					return new SelectOption(mode.name(), mode.getMessage());
				}).toList();
		// 會議模式列表
		List<SelectOption> situationModeSelects = Arrays.asList(SituationMode.values()).stream()
				.map(mode -> new SelectOption(mode.name(), mode.getMessage())).toList();
		// 建立回傳資料
		ReserveInitResponse response = new ReserveInitResponse();
		response.setMeetingRoomSelects(meetingRoomSelects);
		response.setCompanySelects(companySelects);
		response.setDepartmentSelects(departmentSelects);
		response.setMeetingModeSelects(meetingModeSelects);
		response.setSituationModeSelects(situationModeSelects);
		return response;
	}

	public ReserveCompanyChangeResponse companySelectChange(ReserveCompanyChangeRequest request) {
		List<SelectOption> departmentOption = departmentListRepository.findByCompanyId(request.getCompanyId()).stream()
				.map(depart -> {
					return new SelectOption(String.valueOf(depart.getId()), depart.getName());
				}).collect(Collectors.toList());

		ReserveCompanyChangeResponse response = new ReserveCompanyChangeResponse();
		response.setType(request.getType());
		response.setDepartmentOptions(departmentOption);

		return response;
	}

	public ReserveDepartmentChangeResponse departmentSelectChange(ReserveDepartmentChangeRequest request) {
		// List<SelectOption> userOption =
		// userRepository.findByDepartmentId(request.getDepartmentId())
		// 根據DepartmentId照筆劃排序中文名字
		List<SelectOption> userOption = userRepository.findByDepartmentIdOrderByFullNameAsc(request.getDepartmentId())
				.stream().map(user -> {
					// 名字內是否存在英文，有的話只取中文名字
					String fullName = user.getFullName();
					if (fullName.matches(".*[a-zA-Z]+.*")) {
						String chineseFullName = fullName.replaceAll("[^\\u4e00-\\u9fa5]", "");
						SelectOption option = new SelectOption(user.getId(),
								chineseFullName + "(" + user.getJobNum() + ")");
						option.setSecValue(user.getPhone());
						option.setThrValue(user.getFullName());
						return option;
					} else {
						SelectOption option = new SelectOption(user.getId(),
								user.getFullName() + "(" + user.getJobNum() + ")");
						option.setSecValue(user.getPhone());
						option.setThrValue(user.getFullName());
						return option;
					}

				}).collect(Collectors.toList());

		ReserveDepartmentChangeResponse response = new ReserveDepartmentChangeResponse();
		response.setType(request.getType());
		response.setUserOptions(userOption);

		return response;
	}

	/**
	 * ajax 查詢所有會議室預約資訊 type = 查詢種類 月/周/日
	 */
	public ReserveQueryResponse query(ReserveQueryRequest request) {
		LocalDate parseDate = request.getDate();
		LocalDateTime[] dates = null;
		if ("daily".equals(request.getType())) {
			dates = dateUtil.getStartEndByDate(parseDate);
		} else if ("weekly".equals(request.getType())) {
			dates = dateUtil.getWeekFirstLastByDate(parseDate);
		} else if ("monthly".equals(request.getType())) {
			dates = dateUtil.getMonthStartEndByToday(parseDate);
		}
		@SuppressWarnings("null")
		LocalDateTime firstDay = dates[0];
		LocalDateTime lastDay = dates[1];

		Map<String, List<ReserveTimeDetails>> reserveMap = new LinkedHashMap<>();

		// 周曆和月曆才需要統計每個時間段的會議室預約狀況
		if ("weekly".equals(request.getType()) || "monthly".equals(request.getType())) {
			for (int i = 0; i <= Duration.between(firstDay, lastDay).toDays(); i++) {
				String date = dateFormatter.format(firstDay.plusDays(i));
				List<ReserveTimeDetails> reserveDetailList = new ArrayList<>();
				for (int j = 0; j < 24; j++) {
					ReserveTimeDetails detail = new ReserveTimeDetails();
					detail.setReserveDate(date);
					detail.setHour(String.format("%02d", j));
					detail.setRoomCount(0L);
					reserveDetailList.add(detail);
				}
				reserveMap.put(date, reserveDetailList);
			}
		}

		ReserveQueryResponse response = new ReserveQueryResponse();
		response.setQueryType(request.getType());

		if (!request.getFloor().isEmpty() || !request.getRoom().isEmpty()) {
			List<ReserveQueryDTO> resultList = meetingReserveRepository.findAllReserveLists(request.getFloor(),
					request.getRoom(), firstDay, lastDay);
			String loginId = UserHolder.getCurrentUser().getId();
			List<ReserveQueryPageModel> pageModels = resultList.stream().map(result -> {
				ReserveQueryPageModel pageModel = new ReserveQueryPageModel();

				String contactPhone = null;
				String contactName = null;

				if (StringUtils.isNoneBlank(result.getContactId())) {
					AppUser contactUser = Optional.ofNullable(userRepository.findById(result.getContactId()))
							.orElseThrow(NullPointerException::new).get();
					contactPhone = contactUser.getPhone();
					contactName = contactUser.getFullName();
				} else {
					contactPhone = result.getContactPhone();
					contactName = result.getContactName();
				}

				pageModel.setTheme(result.getReserveId().equals(loginId) ? result.getTheme()
						: result.isPersonal() ? "不公開" : result.getTheme());
				pageModel.setOwn(result.getReserveId().equals(loginId) ? "ownMeeting" : "otherMeeting");
				pageModel.setContactName(contactName);
				pageModel.setContactPhone(contactPhone);
				pageModel.setReserveId(result.getReserveId());
				pageModel.setMeetingId(result.getMeetingId());
				pageModel.setPersonal(result.isPersonal());
				pageModel.setRoomId(result.getRoomId());
				pageModel.setRoomName(result.getRoomName());
				pageModel.setReserveDate(dateFormatter.format(result.getReserveDateStart()));
				pageModel.setReserveStartTime(timeFormatter.format(result.getReserveDateStart()));
				pageModel.setReserveEndTime(timeFormatter.format(result.getReserveDateEnd()));

				reserveMap.forEach((k, v) -> {
					if (pageModel.getReserveDate().equals(k)) {
						String startH = pageModel.getReserveStartTime().split(":")[0];
						String endH = pageModel.getReserveEndTime().split(":")[0];

						for (int i = Integer.parseInt(startH); i < Integer.parseInt(endH); i++) {
							v.get(i).setRoomCount(Long.sum(v.get(i).getRoomCount(), 1));
						}
						reserveMap.put(k, v);
					}
				});

				return pageModel;
			}).collect(Collectors.toList());

			response.setPageContent(pageModels);
		}

		response.setReserveMap(reserveMap);

		return response;
	}

	/**
	 * 
	 * <AUTHOR> Lin
	 * @datetime 2024年9月1日
	 * @method
	 * @param request
	 * @return
	 * @description 查看更多會議功能 - 查詢指定時間段的所有會議資訊
	 */
	public ReserveQueryDayReserveResponse queryOneDayofReserveDetails(ReserveQueryDayReserveRequest request) {
		LocalDate parseDate = request.getDate();
		LocalDateTime[] dates = new LocalDateTime[2];

		// 如果沒有開始時間及結束時間，即代表從月曆點擊【查看更多會議】
		if (request.getStartTime() == null && request.getEndTime() == null) {
			dates = dateUtil.getStartEndByDate(parseDate);
		} else {
			LocalDateTime startDateTime = LocalDateTime.of(parseDate, request.getStartTime());
			LocalDateTime endDateTime = LocalDateTime.of(parseDate, request.getEndTime());
			dates[0] = startDateTime;
			dates[1] = endDateTime;
		}

		LocalDateTime firstDay = dates[0];
		LocalDateTime lastDay = dates[1];

		List<ReserveQueryDTO> resultList = meetingReserveRepository.findAllReserveLists(request.getFloor(),
				request.getRoom(), firstDay, lastDay);
		log.info("query result : {}", resultList);

		String loginId = UserHolder.getCurrentUser().getId();

		List<ReserveQueryPageModel> pageModels = resultList.stream().map(result -> {
			ReserveQueryPageModel pageModel = new ReserveQueryPageModel();

			String contactPhone = null;
			String contactName = null;

			if (StringUtils.isNoneBlank(result.getContactId())) {
				AppUser contactUser = Optional.ofNullable(userRepository.findById(result.getContactId()))
						.orElseThrow(NullPointerException::new).get();
				contactPhone = contactUser.getPhone();
				contactName = contactUser.getFullName();
			} else {
				contactPhone = result.getContactPhone();
				contactName = result.getContactName();
			}

			pageModel.setTheme(result.getReserveId().equals(loginId) ? result.getTheme()
					: result.isPersonal() ? "不公開" : result.getTheme());
			pageModel.setOwn(result.getReserveId().equals(loginId) ? "ownMeeting" : "otherMeeting");
			pageModel.setContactName(contactName);
			pageModel.setContactPhone(contactPhone);
			pageModel.setReserveId(result.getReserveId());
			pageModel.setMeetingId(result.getMeetingId());
			pageModel.setPersonal(result.isPersonal());
			pageModel.setRoomId(result.getRoomId());
			pageModel.setRoomName(result.getRoomName());
			pageModel.setReserveDate(dateFormatter.format(result.getReserveDateStart()));
			pageModel.setReserveStartTime(timeFormatter.format(result.getReserveDateStart()));
			pageModel.setReserveEndTime(timeFormatter.format(result.getReserveDateEnd()));

			return pageModel;
		}).collect(Collectors.toList());

		ReserveQueryDayReserveResponse response = new ReserveQueryDayReserveResponse();
		response.setPageContent(pageModels);

		return response;
	}

	/**
	 * 
	 * <AUTHOR> Lin
	 * @datetime 2024年9月1日
	 * @method
	 * @param request
	 * @return
	 * @description 會議預約Modal初始化
	 */
	public ReserveTimeQueryResponse reserveTimeQuery(ReserveTimeQueryRequest request) {
		LocalDate parseDate = request.getDate();

		LocalDateTime firstDay = null;
		LocalDateTime lastDay = null;

		if (request.getQueryType().equals("monthly")) {
			LocalDateTime[] dates = dateUtil.getStartEndByDate(parseDate);
			firstDay = dates[0];
			lastDay = dates[1];
		} else {
			firstDay = LocalDateTime.of(parseDate, request.getStartTime());
			lastDay = LocalDateTime.of(parseDate, request.getEndTime());
		}

		List<SelectOption> floorOptions = getFilterFloorOption(firstDay, lastDay, null);
		log.info("filter floor options : {}", floorOptions);

		DepartmentList department = Optional
				.ofNullable(departmentListRepository.findById(UserHolder.getCurrentUser().getDepartmentId()))
				.orElseThrow(NullPointerException::new).get();

		ReserveTimeQueryResponse response = new ReserveTimeQueryResponse();
		response.setQueryType(request.getQueryType());
		response.setContactId(UserHolder.getCurrentUser().getId());
		response.setContactName(UserHolder.getCurrentUser().getName());
		response.setCompanyId(UserHolder.getCurrentUser().getCompanyId());
		response.setDepartmentId(UserHolder.getCurrentUser().getDepartmentId());
		response.setDepartmentName(department.getName());
		response.setFloorOptions(floorOptions);

		return response;
	}

	/**
	 * 新增預約會議 【條件】 登入人員須有權限 會議室要為啟用狀態 會議室預約時間不得衝突
	 */
	@Transactional
	public void saveReserve(ReserveAddRequest request) {
		List<ErrorMessage> errors = new ArrayList<>();
		// 會議室是否存在
		Optional<MeetingRoom> meetingRoomOpt = meetingRoomRepository.findById(request.getRoomId());
		if (!meetingRoomOpt.isPresent()) {
			log.error("例外：會議室不存在");
			throw new RestApiException(ResponseStatusCode.INSERT_FAILED,  Arrays.asList(new ErrorMessage("roomId", "會議室不存在")));
		}

		// 會議室狀態是否啟用
		MeetingRoom roomEntity = meetingRoomOpt.get();
		if (!roomEntity.isRoomEnable()) {
			log.error("例外：會議室未啟用");
			throw new RestApiException(ResponseStatusCode.INSERT_FAILED,  Arrays.asList(new ErrorMessage("roomId", "會議室尚未啟用，無法進行預約")));
		}

		// 會議室預約時間不得衝突
		LocalDateTime bTime = request.getBeginTime();
		LocalDateTime eTime = request.getEndTime();
		String roomId = request.getRoomId();

		// 預約型態
		String type = request.getType();
		// 週期性預約結束時間(取結束日期"yyyy-MM-dd")
		LocalDateTime cycleBTime = request.getCycleBeginTime();
		long week = 0;
		// 計算預約區間相差天數
		if (cycleBTime != null && type != null) {
			Duration durDays = Duration.between(bTime, cycleBTime);
			long totalDays = durDays.toDays();

			// 周預約
			if (type.equals("week")) {
				week = totalDays / 7;
				// 區間固定星期的日期
				for (long i = 0; i <= week; i++) {
					LocalDateTime newBTime = bTime.plusDays(i * 7);
					LocalDateTime newETime = eTime.plusDays(i * 7);

					List<MeetingReserve> resultList = meetingReserveRepository.findIfMeetingExist(newBTime, newETime,
							roomId, null);
					log.info("newBTime and newETime room query result : {}", resultList);
					// 判斷是否有重疊到會議
					if (!CollectionUtils.isEmpty(resultList)) {
						errors.add(new ErrorMessage("timeSet", "預約時間已有其他會議，請重新設定"));
						throw new RestApiException(ResponseStatusCode.INSERT_FAILED, errors);
					}
					String qrCode = setCusuApi(request.getMembers(), request.getFloor());
					saveMeetingReserveEntity(request, newBTime, newETime, qrCode);
				}
			}
			// 日預約
			if (type.equals("daily")) {
				for (long i = 0; i <= totalDays; i++) {
					LocalDateTime newBeginTime = bTime.plusDays(i);
					LocalDateTime newEndTime = eTime.plusDays(i);

					List<MeetingReserve> resultList = meetingReserveRepository.findIfMeetingExist(newBeginTime,
							newEndTime, roomId, null);
					log.info("newBTime and newETime room query result : {}", resultList);
					// 判斷是否有重疊到會議
					if (!CollectionUtils.isEmpty(resultList)) {
						errors.add(new ErrorMessage("timeSet", "預約時間已有其他會議，請重新設定"));
						throw new RestApiException(ResponseStatusCode.INSERT_FAILED, errors);
					}
					String qrCode = setCusuApi(request.getMembers(), request.getFloor());
					saveMeetingReserveEntity(request, newBeginTime, newEndTime, qrCode);
				}
			}
			// 月預約
		} else if ("month".equals(type)) {
			// 月預約的當天yyyy-MM-dd
			LocalDate today = bTime.toLocalDate();
			LocalDate reservationDate = bTime.toLocalDate();
			// 月預約 第幾周、星期幾
			int dayOfWeek = Integer.parseInt(request.getDayOfWeek());
			int weekOfMonth = Integer.parseInt(request.getWeekOfMonth());

			for (int i = 0; i <= 3; i++) {

				// 目前月份的第一天
				LocalDate firstDayOfMonth = today.withDayOfMonth(1);

				// 目前月份的第一個目標日(當月或下一次的day of week)
				LocalDate firstTargetDay = firstDayOfMonth.with(TemporalAdjusters.nextOrSame(DayOfWeek.of(dayOfWeek)));

				// 第幾周星期幾 (第一周 = 0)
				LocalDate targetDate = firstTargetDay.plusWeeks(weekOfMonth - 1);

				// 如果當月沒有第五周，就往第四周抓(代表第四周是最後一周)
				if (targetDate.getMonthValue() != today.getMonthValue()) {
					targetDate = firstTargetDay.plusWeeks((weekOfMonth - 1) - 1);

				}

				if (targetDate.isBefore(reservationDate)) {
					today = today.plusMonths(1);
					continue;
				}

				if (targetDate.getMonthValue() == today.getMonthValue()) {
					LocalDateTime reserveDayOfMonthStart = targetDate.atTime(bTime.toLocalTime());
					LocalDateTime reserveDayOfMonthEnd = targetDate.atTime(eTime.toLocalTime());
					List<MeetingReserve> resultList = meetingReserveRepository
							.findIfMeetingExist(reserveDayOfMonthStart, reserveDayOfMonthEnd, roomId, null);
					log.info("newBTime and newETime room query result : {}", resultList);
					// 判斷是否有重疊到會議
					if (!CollectionUtils.isEmpty(resultList)) {
						errors.add(new ErrorMessage("timeSet", "預約時間已有其他會議，請重新設定"));
						throw new RestApiException(ResponseStatusCode.INSERT_FAILED, errors);
					}
					String qrCode = setCusuApi(request.getMembers(), request.getFloor());
					saveMeetingReserveEntity(request, reserveDayOfMonthStart, reserveDayOfMonthEnd, qrCode);
					today = today.plusMonths(1);
				}
			}
		} else {
			List<MeetingReserve> resultList = meetingReserveRepository.findIfMeetingExist(bTime, eTime, roomId, null);
			log.info("room query result : {}", resultList);

			if (!CollectionUtils.isEmpty(resultList)) {
				errors.add(new ErrorMessage("timeSet", "預約時間已有其他會議，請重新設定"));
				throw new RestApiException(ResponseStatusCode.INSERT_FAILED, errors);
			}
			String qrCode = setCusuApi(request.getMembers(), request.getFloor());
			saveMeetingReserveEntity(request, bTime, eTime, qrCode);
		}
	}

	/**
	 * 新增預約會議 新增進資料庫
	 */
	public void saveMeetingReserveEntity(ReserveAddRequest request, LocalDateTime bTime, LocalDateTime eTime, String qrCode) {
		// 新增預約
		MeetingReserve reserveEntity = new MeetingReserve();
		reserveEntity.setRoomId(request.getRoomId());
		reserveEntity.setReserveId(UserHolder.getCurrentUser().getId());

		if (StringUtils.isNotBlank(request.getHostId())) {
			reserveEntity.setHostId(request.getHostId());
		}
		reserveEntity.setHostName(request.getHostName());

		if (StringUtils.isNoneBlank(request.getContactId())) {
			reserveEntity.setContactId(request.getContactId());
		}
		reserveEntity.setContactName(request.getContactName());
		reserveEntity.setContactPhone(request.getContactPhone());

		if (StringUtils.isNoneBlank(request.getReserveDeptId())) {
			reserveEntity.setDepartmentId(request.getReserveDeptId());
		}
		reserveEntity.setDepartmentName(request.getReserveDeptName());

		reserveEntity.setMeetingMode(request.getMeetingMode());
		reserveEntity.setSituationMode(request.getSituationMode());
		reserveEntity.setStatus(RecordStatus.RESERVE);
		reserveEntity.setMembers(request.getMembers());
		reserveEntity.setTheme(request.getTheme());
		reserveEntity.setPersonal(request.getPersonal());

		reserveEntity.setReserveDateStart(bTime);
		reserveEntity.setReserveDateEnd(eTime);
		reserveEntity.setOnlineMeetingUrl(request.getUrl());
		reserveEntity.setQrCode(qrCode);

		MeetingReserve createReserve = meetingReserveRepository.save(reserveEntity);

		int size = request.getAttendDept().size();
		List<String> attendDept = request.getAttendDept();
		List<String> nameValue = request.getNameValue();
		IntStream.range(0, size).forEach(i -> {
			MeetingReserveDepartment dep = new MeetingReserveDepartment();
			dep.setReserveId(createReserve.getId());
			dep.setDepartmentId(Long.valueOf(attendDept.get(i)));
			dep.setDepartmentName(nameValue.get(i));
			meetingReserveDepartmentRepository.save(dep);
		});
	}

	/**
	 * 接收QRCode
	 */
	// public String getQRCode(QRcodeRequest request) {
	// request.setQrCode(
	// "iVBORw0KGgoAAAANSUhEUgAAAPEAAADxAQAAAAAysGykAAAFH0lEQVR4Xu2ZTY6rSgyFjRjUjGwAqbbBjC0lG0hgA7AlZrUNpNpAmNUA4feZDv3uj3Slp6rR1YtarTQHyZR9fHxMi/7xM8ivV37+/I//euXnz9+AD1IfvVTBz2ltemm6VmRtZJUTy8dnjdtSv12tS+TeLejQ7y+Nmsrgk8TJ6dT7Me1Ppwdgqt99HLpyuPCN88kjcdB6sluK4i6+3f7QepC96fTd1VNXDCd/U7e+tJWunpMebqdK9/7H/Gbh1IRov/38yI/fwP+CG01dnBedXHujRJ1uur7SN39z8Qn+OgK29578QTTS6Yd+lb4MPid/dGopDFGXlf54aJzT3nzOl4sTf17aiuawtCmNMgZ5ab39e74sfFQPocZA/ryqtci4rE/x04dfufggcQ5yRza6tVpqTas4Wnx9hFK4Hh0l2h9Jt0VHficYBynK4G8ndzk7Q+hC3YLcgjwCHVMKhwJSLTSin7/+DMbf4+vZsvE51IPVRB663pLXAJGpGLUqg2vaK6UstQa/LfvN8re/SOSnPrk4bGpc+wp+6ETEk84qxC1wpQw+h7P+Ege3P/v22flN29fit0sfM/ExWf9BgVGJH2c1cr17HqoMfvSWwpsJuao1+vpYbBY1n/7LxbfENPDUZxKIbMedHFpFx5TBp460MXZqG/uhRTmwA+Pip74MrkmejhRyF1MCl0H+4K/cP/HzcVKIuagtYDCVenGLfOtHLk6pK6i02HAgf++e+Yx4QOEyOObokLYKtEX7IH7PIwgUuPQ7F9/OnA19ex7rHHFuv4tc/ZeLj7rfrSHwdORPB4ojNq6v+ZyL03Db0r5S+6ALcQFCr5yM/sTPxQfcRIf4oa9onvF3VOhGxxTCOxM8PKlwo0ilNpy3ReRTn1wc5zXgWVLbdMwHtbIvYl8+/M3FJ1NWKiONoEm0HeOCikWTqxK44hwpdWorhDbR2XFMwvz8nk+ZOJPzZpOzxp+afgd5Ggu4WAa3PWDZpdtvAebyJW5aj8mWniL4JFYW8y8OZL279tmjhZCiFO4n9APOBs8Rsb2b+dN4+ZdcnP6zFncMhDj0PIJtTraiffKXi4+LBXyiqcESicSqzR8zTWXwczIwkCn+tnhlUevXJ71yxc/EUdYGzXYR887MZ0uubBHxJ1YAH02Z8BTc6DnZHKCYWcihL4NTfILzu7FT0oU2Im6LLYJlcIdhZ7PkFhtrh1u/4lO3IvhbAP0crDluEK03x3pKVBkct/XAXy97tdBzyAZ0aCHvt//KxA8LyFPUBwHTiso+kUBWwE99cnENBJeXOVPGGr1iU+6ldHwZ/LBVdRc703rvmRIIYa3qL/3Ixb/eGQwoq+OL5RKjasPzEz8XP/C/gqcDh78oN87CXlRYOsvgXCNbeCJ7Z/B2nBULs1/+KBs3T0ryPPtTY/sZKyBasl79l4tb2CVuaWXmj4rKCom0XfDidyaO/lW24rTYutMZRWOxi1f+cnHiM4JsSuMvtL27OAa8av3tbzLxWXeYe2rSfu6v9iAvhRSF8MVWDXutKLC4JpHU/959+5dcXG1E2Hx4m34j5zBibeyNWhncWoFqINtMiW4/lYNCxcvf5eKzslbaiCN5t9QarUxu7UVUEfz0vzaQIcKABVB7VzSZqS+HS2StvCXGTts4f75FaK/9uABuzrfH5a3GKeKbuVsv/c7F7f8Pic6OtjYF5jOZg1xmJIvg1J+Bw37z7Olp2k6ejnFng6gI/qfP//ivV37+/PX4P3A/b2C+sMCPAAAAAElFTkSuQmCC");
	// String qrCode = request.getQrCode();
	// return qrCode;
	// }

	/**
	 * cusu API
	 */
	public String setCusuApi(Integer members, String floor) {
		CusuApiRequest request = new CusuApiRequest();
		String extensionNumber = "";
		// 觸發時間
		LocalDateTime now = LocalDateTime.now();
		// 格式化
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		String nowFormatter = now.format(dateTimeFormatter);
		// 分割樓層
		String[] floorList = floor.split("(?<=\\d)(?=\\D)");
		// 取樓層數字
		if (floorList[0].length() == 1) {
			extensionNumber = "000" + floorList[0]; // 如果樓層是1~9前面補0
		} else {
			extensionNumber = "00" + floorList[0];
		}
		// 每5人一張卡片
		Integer card = members / 5;

		request.setCardCount(card);
		request.setExtensionNumber(extensionNumber);
		request.setTriggeredTime(nowFormatter);
		request.setDeviceID("Device_none");
		request.setToken("NSST");

		// 使用RestApiUtils
		CusuApiResponse response = restApiUtils.getCusu(request, CusuApiResponse.class);

		if (response != null) {
			log.info("API 成功: {}", response);
			return response.getCards().get(0);
		} else {
			log.error("API 失敗");
			return "";
		}
	}

	/**
	 * ajax 刪除預約會議 【條件】 登入人員須有權限 必須是該創建會議人員 該會議狀態必須為「預約中」
	 */
	@Transactional
	public void delete(ReserveDeleteRequest request) {
		// 初始化
		List<ErrorMessage> errors = new ArrayList<>();
		// 會議查詢
		Optional<MeetingReserve> reserveEntity = meetingReserveRepository.findById(request.getMeetingId());
		if (reserveEntity.isPresent()) {
			MeetingReserve reserve = reserveEntity.get();
			// 必須是該創建會議人員
			String userAccountId = reserve.getReserveId();
			RecordStatus status = reserve.getStatus();
			String loginAccountId = UserHolder.getCurrentUser().getId();
			if (!userAccountId.equals(loginAccountId)) {
				errors.add(new ErrorMessage("alert", "尚未有權限刪除"));
				throw new RestApiException(ResponseStatusCode.DELETE_FAILED, errors);
			}
			if (status.isConduct() || status.isEnd()) {
				errors.add(new ErrorMessage("alert", "「會議時間早於現在時間」不可異動/刪除"));
				throw new RestApiException(ResponseStatusCode.DELETE_FAILED, errors);
			}
			MeetingReserve deleteEntity = reserveEntity.get();
			deleteEntity.setStatus(RecordStatus.CANCEL);
			meetingReserveRepository.save(deleteEntity);
		}
	}

	/**
	 * ajax 更新預約會議 【條件】 登入人員需要權限 必須是該創建會議人員 該會議狀態必須為「預約中」
	 */
	@Transactional
	public void update(ReserveUpdateRequest request) {
		List<ErrorMessage> errors = new ArrayList<>();
		List<MeetingReserve> resultList = meetingReserveRepository.findIfMeetingExist(request.getBeginTime(),
				request.getEndTime(), request.getRoomId(), request.getMeetingId());
		// 會議室預約時間不得衝突
		if (!CollectionUtils.isEmpty(resultList)) {
			errors.add(new ErrorMessage("timeSet", "預約時間已有其他會議，請重新設定"));
			throw new RestApiException(ResponseStatusCode.UPDATE_FAILED, errors);
		}
		Optional<MeetingReserve> reserveOpt = meetingReserveRepository.findById(request.getMeetingId());
		if (reserveOpt.isPresent()) {
			MeetingReserve reserveEntity = reserveOpt.get();
			// 必須是該創建會議人員
			// 會議狀態必須為「預約中」
			String userAccountId = reserveEntity.getReserveId();
			String loginAccountId = UserHolder.getCurrentUser().getId();

			if(reserveEntity.getStatus().isConduct()){
				throw new RestApiException(ResponseStatusCode.UPDATE_FAILED, Arrays.asList(new ErrorMessage("alert", "會議進行中，不可異動/刪除")));
			}

			if (!userAccountId.equals(loginAccountId) || !reserveEntity.getStatus().isReserve()) {
				errors.add(new ErrorMessage("alert", "「會議時間早於現在時間」不可異動/刪除"));
				throw new RestApiException(ResponseStatusCode.UPDATE_FAILED, errors);
			}

			reserveEntity.setRoomId(request.getRoomId());
			reserveEntity.setHostId(request.getHostId());
			reserveEntity.setContactId(request.getContactId());
			reserveEntity.setContactName(request.getContactName());
			reserveEntity.setContactPhone(request.getContactPhone());
			reserveEntity.setMeetingMode(request.getMeetingMode());
			reserveEntity.setSituationMode(request.getSituationMode());
			reserveEntity.setMembers(request.getMembers());
			reserveEntity.setTheme(request.getTheme());
			reserveEntity.setDepartmentId(request.getReserveDeptId());
			reserveEntity.setDepartmentName(request.getReserveDeptName());
			reserveEntity.setPersonal(request.getPersonal());
			reserveEntity.setReserveDateStart(request.getBeginTime());
			reserveEntity.setReserveDateEnd(request.getEndTime());

			reserveEntity.setOnlineMeetingUrl(request.getUrl());
			meetingReserveRepository.save(reserveEntity);

			meetingReserveDepartmentRepository.deleteAllByReserveId(request.getMeetingId());
			int size = request.getAttendDept().size();
			List<String> attendDept = request.getAttendDept();
			List<String> nameValue = request.getNameValue();
			IntStream.range(0, size).forEach(i -> {
				MeetingReserveDepartment dep = new MeetingReserveDepartment();
				dep.setReserveId(request.getMeetingId());
				dep.setDepartmentId(Long.valueOf(attendDept.get(i)));
				dep.setDepartmentName(nameValue.get(i));
				meetingReserveDepartmentRepository.save(dep);
			});
		}
	}

	/**
	 * 
	 * <AUTHOR> Lin
	 * @datetime 2024年9月1日
	 * @method
	 * @param request
	 * @return
	 * @description 編輯/查看會議細節
	 */
	public ReserveDetailResponse details(ReserveDetailRequest request) {
		MeetingReserve reserveInfo = Optional.ofNullable(meetingReserveRepository.findById(request.getMeetingId()))
				.orElseThrow(NullPointerException::new).get();

		MeetingRoom roomInfo = Optional.ofNullable(meetingRoomRepository.findById(reserveInfo.getRoomId()))
				.orElseThrow(NullPointerException::new).get();

		log.info("query result : {}", reserveInfo);

		List<SelectOption> floorOptions = null;
		if (request.getReserveType().equals("ownMeeting")) {
			floorOptions = getFilterFloorOption(reserveInfo.getReserveDateStart(), reserveInfo.getReserveDateEnd(),
					reserveInfo.getId());
		}

		ReserveDetailResponse response = new ReserveDetailResponse();
		response.setMeetingId(reserveInfo.getId());
		response.setReserveType(request.getReserveType());
		response.setFloorOptions(floorOptions);
		response.setRoomId(reserveInfo.getRoomId());
		response.setRoomName(roomInfo.getRoomName());
		response.setFloor(String.valueOf(roomInfo.getFloor()));
		response.setUrl(reserveInfo.getOnlineMeetingUrl());
		response.setQrCode(reserveInfo.getQrCode());
		response.setReserveDate(dateFormatter.format(reserveInfo.getReserveDateStart()));
		response.setReserveStartTime(timeFormatter.format(reserveInfo.getReserveDateStart()));
		response.setReserveEndTime(timeFormatter.format(reserveInfo.getReserveDateEnd()));

		if (StringUtils.isNotBlank(reserveInfo.getContactId())) {
			response.setContactId(reserveInfo.getContactId());
		} else {
			response.setContactId("");
		}
		response.setContactName(reserveInfo.getContactName());
		response.setContactPhone(reserveInfo.getContactPhone());

		if (request.getReserveType().equals("otherMeeting") && reserveInfo.isPersonal()) {
			response.setTheme("不公開");
			response.setDepartmentName("不公開");
			response.setHostName("不公開");
			response.setMeetingModeName("不公開");
			response.setSituationModeName("不公開");
			response.setMembers("不公開");
		} else {
			response.setTheme(reserveInfo.getTheme());
			response.setMeetingMode(reserveInfo.getMeetingMode());
			response.setSituationMode(reserveInfo.getSituationMode());
			response.setMeetingModeName(reserveInfo.getMeetingMode().getMessage());
			response.setSituationModeName(reserveInfo.getSituationMode().getMessage());
			response.setUrl(reserveInfo.getOnlineMeetingUrl());
			response.setMembers(String.valueOf(reserveInfo.getMembers()));
			response.setReserveDepartments(reserveInfo.getAttendDepartment());
			response.setReserveDepartmentsName(reserveInfo.getAttendDepartmentNames());
			response.setPersonal(reserveInfo.isPersonal());

			if (reserveInfo.getDepartmentId() != null) {
				response.setDepartmentId(String.valueOf(reserveInfo.getDepartmentId()));
				DepartmentList depart = Optional
						.ofNullable(departmentListRepository.findById(reserveInfo.getDepartmentId()))
						.orElseThrow(NullPointerException::new).get();
				response.setDepartmentName(depart.getName());
				response.setCompanyId(String.valueOf(depart.getCompanyId()));
			} else {
				response.setDepartmentId("");
				response.setDepartmentName(reserveInfo.getDepartmentName());
				response.setCompanyId("");
			}

			if (StringUtils.isNotBlank(reserveInfo.getHostId())) {
				response.setHostId(reserveInfo.getHostId());
				AppUser hostUser = Optional.ofNullable(userRepository.findById(reserveInfo.getHostId()))
						.orElseThrow(NullPointerException::new).get();
				response.setHostName(hostUser.getFullName());
			} else {
				response.setHostId("");
				response.setHostName(reserveInfo.getHostName());
			}
		}

		return response;
	}

	private List<SelectOption> getFilterFloorOption(LocalDateTime firstDay, LocalDateTime lastDay, String meetingId) {
		// 會議室查詢
		List<MeetingRoom> rooms = meetingRoomRepository.findByOfficeIdAndRoomEnable("KH01", true);
		// 會議室預約查詢
		List<ReserveTimeQueryDTO> allReserveRecords = meetingReserveRepository.findAllReserveRecords(firstDay, lastDay);
		// 資料建立
		List<SelectOption> floorOptions = rooms.stream()
				.collect(Collectors.groupingBy(MeetingRoom::getFloor))
				.entrySet()
				.stream()
				.map(meetingRoom -> {
					SelectOption floorOpt = new SelectOption(String.valueOf(meetingRoom.getKey()),
							String.valueOf(meetingRoom.getKey()) + "樓");

					List<String> roomList = meetingRoom.getValue().stream().map(MeetingRoom::getId)
							.toList();

					List<ReserveTimeQueryDTO> filterRecordList = allReserveRecords.stream()
							.filter(reserveRecord -> roomList.contains(reserveRecord.getRoomId()))
							.toList();

					List<ReserveTimeQueryDTO> filterRecordIdList = filterRecordList.stream()
							.filter(reserveRecord -> meetingId == reserveRecord.getMeetingId())
							.toList();

					if (filterRecordIdList.size() > 0) {
						floorOpt.setDisabled(false);
					} else if (filterRecordList.size() == roomList.size()) {
						floorOpt.setDisabled(true);
					} else {
						floorOpt.setDisabled(false);
					}

					List<String> recordRoomIdList = allReserveRecords.stream().map(reserveRecord -> {
						return reserveRecord.getRoomId();
					}).toList();

					List<SelectOption> roomOptions = meetingRoom.getValue()
							.stream()
							.map(room -> {
								SelectOption roomOpt = new SelectOption(room.getId(), room.getRoomName());
								roomOpt.setSecValue(
										String.valueOf(room.getCapacity() == null ? 0 : room.getCapacity()));
								if (!filterRecordIdList.isEmpty()) {
									if (filterRecordIdList.get(0).getRoomId().equals(room.getId())) {
										roomOpt.setDisabled(false);
									}
								} else if (recordRoomIdList.contains(room.getId())) {
									roomOpt.setDisabled(true);
								} else {
									roomOpt.setDisabled(false);
								}
								return roomOpt;
							}).toList();

					floorOpt.setSubOptions(roomOptions);
					return floorOpt;
				}).toList();

		return floorOptions;
	}
}
