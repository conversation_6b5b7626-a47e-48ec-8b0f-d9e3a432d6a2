package com.twport.meeting_backend.config;

import java.io.Serializable;
import java.util.List;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年8月2日
 * @class MeetingRoomIdGeneratorConfig
 * @description 自訂產生會議室ID規則
 */
@Slf4j
public class MeetingRoomIdGeneratorConfig implements IdentifierGenerator {

	private static final long serialVersionUID = 1L;

	@Override
	public Serializable generate(SharedSessionContractImplementor session, Object object) {

		String id = session.getEntityPersister(object.getClass().getName(), object).getIdentifierPropertyName();
		String table = object.getClass().getSimpleName();
		String floor = String.valueOf(
				session.getEntityPersister(object.getClass().getName(), object).getPropertyValue(object, "floor"));

		String query = String.format("select %s from %s where floor=%s",
				id, table, floor);

		List<String> ids = session.createSelectionQuery(query, String.class).getResultList();

		Long maxId = ids.stream().map(data -> data.substring(2)).mapToLong(Long::parseLong).max().orElse(0L);

		return String.format("%02d%02d", Integer.valueOf(floor), maxId + 1);
	}

}
