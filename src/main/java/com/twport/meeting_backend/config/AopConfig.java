package com.twport.meeting_backend.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * AOP 配置類
 * 
 * <AUTHOR>
 * @datetime 2024年12月19日
 * @class AopConfig
 * @description 啟用 AspectJ 自動代理，確保 AOP 功能正常運作
 */
@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class AopConfig {
    // 此配置類用於啟用 AOP 功能
    // proxyTargetClass = true 表示使用 CGLIB 代理而不是 JDK 動態代理
}
