package com.twport.meeting_backend.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;

@Configuration
@Profile("frontend")
public class LdapTemplateConfig {
	@Value("${spring.ldap.urls}")
	private String ldapUrl;
	
	@Value("${spring.ldap.username}")
	private String userName;
	
	@Value("${spring.ldap.password}")
	private String password;
	
	@Value("${spring.ldap.base}")
	private String base;
	
	@Bean
	public LdapContextSource ldapContextSource() {
		LdapContextSource source = new LdapContextSource();
		source.setUrl(ldapUrl);
		source.setUserDn(userName);
		source.setPassword(password);
		source.setBase(base);
		return source;
	}
	
	@Bean
	public LdapTemplate ldapTemplate() {
		return new LdapTemplate(ldapContextSource());
	}
}
