package com.twport.meeting_backend.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
import org.springframework.security.web.csrf.CsrfTokenRequestAttributeHandler;

import com.twport.meeting_backend.security.SecurityAuthSuccessHandler;
import com.twport.meeting_backend.service.AppUserDetailsService;

@Configuration
@EnableWebSecurity
@Profile("dev")
public class DevWebSecurityConfig {
	
	private final SecurityAuthSuccessHandler successHandler;
	
	public final AppUserDetailsService appUserService;
	
	public DevWebSecurityConfig(SecurityAuthSuccessHandler successHandler, 
			AppUserDetailsService appUserService) {
		this.successHandler = successHandler;
		this.appUserService = appUserService;
	}

	@Bean
	public AuthenticationProvider daoAuthenticationProvider() {
	      DaoAuthenticationProvider provider = new DaoAuthenticationProvider();
	      // 添加加解密物件
	      provider.setPasswordEncoder(passwordEncoder());
	      provider.setUserDetailsService(this.appUserService);
	      return provider;
	}

	// 認證管理器
	@Bean
	public AuthenticationManager authenticationManager(
	  AuthenticationConfiguration configuration) throws Exception {
	  return new ProviderManager(daoAuthenticationProvider());
	}
	
	@Bean
	public PasswordEncoder passwordEncoder() {
		return new BCryptPasswordEncoder();
	}
	
	@Bean
	public SecurityFilterChain securityChain(HttpSecurity http) throws Exception {
		http
			.csrf((csrf) -> csrf
				.csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse())
				.csrfTokenRequestHandler(new CsrfTokenRequestAttributeHandler())
				.ignoringRequestMatchers("/api/**")
				.ignoringRequestMatchers("/infoApi/**")
				.ignoringRequestMatchers("/signApi/**")
				.ignoringRequestMatchers("/admin/**")
			)
			.authorizeHttpRequests(
					requests -> requests
										.requestMatchers("/api/**").permitAll()
										.requestMatchers("/infoApi/**").permitAll()
										.requestMatchers("/signApi/**").permitAll()
										.requestMatchers("/sample/**").permitAll()
										.requestMatchers("/admin/**").permitAll()
										.requestMatchers("/user/login").permitAll()
										.requestMatchers("/user/logout").permitAll()
										.requestMatchers("/assets/**").permitAll()
										.requestMatchers("/uploads/**").permitAll()
										.anyRequest()
										.authenticated()
					)
			.formLogin(form -> form
					.loginPage("/user/login")
					.successHandler(authenticationSuccessHandler())
					)
			.logout(logout -> logout
					.logoutSuccessUrl("/user/login?logout")
					.invalidateHttpSession(true)
					)
			.cors(cors -> cors.disable());
		
		return http.build();
	}
	
	@Bean
    public AuthenticationSuccessHandler authenticationSuccessHandler() {
        return successHandler;
    }
}
