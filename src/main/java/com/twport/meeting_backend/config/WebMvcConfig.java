package com.twport.meeting_backend.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

@Configuration
public class WebMvcConfig extends WebMvcConfigurationSupport {

	@Value("${uploadfile.location}")
	private String uploadFileLocaltion;
	
	@Override
	protected void addResourceHandlers(ResourceHandlerRegistry registry) {		
		registry.addResourceHandler("/uploads/**")
		.addResourceLocations("file:" + uploadFileLocaltion);
		registry.addResourceHandler("/**")
		.addResourceLocations("classpath:/static/");
	}
}
