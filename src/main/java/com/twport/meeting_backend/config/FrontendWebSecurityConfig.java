package com.twport.meeting_backend.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
import org.springframework.security.web.csrf.CsrfTokenRequestAttributeHandler;

import com.twport.meeting_backend.security.LdapCustomAuthenticationProvider;
import com.twport.meeting_backend.security.SecurityAuthSuccessHandler;
import com.twport.meeting_backend.service.LdapPersonDetailsService;

@Configuration
@EnableWebSecurity
@Profile({"frontend"})
public class FrontendWebSecurityConfig {
	
	private final LdapCustomAuthenticationProvider ldapProvider;
	
	private final LdapPersonDetailsService userDetailsService;
	
	private final SecurityAuthSuccessHandler successHandler;

	public FrontendWebSecurityConfig(
			LdapCustomAuthenticationProvider ldapProvider, 
			LdapPersonDetailsService userDetailsService,
			SecurityAuthSuccessHandler successHandler) {
		this.ldapProvider = ldapProvider;
		this.userDetailsService = userDetailsService;
		this.successHandler = successHandler;
	}

	@Bean
	public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {		
		http
		.csrf((csrf) -> csrf
				.csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse())
				.csrfTokenRequestHandler(new CsrfTokenRequestAttributeHandler())
				.ignoringRequestMatchers("/api/**")
				.ignoringRequestMatchers("/infoApi/**")
				.ignoringRequestMatchers("/signApi/**")
				.ignoringRequestMatchers("/admin/**")
		)
		.authorizeHttpRequests(
				requests -> requests
									.requestMatchers("/api/**").permitAll()
									.requestMatchers("/infoApi/**").permitAll()
									.requestMatchers("/signApi/**").permitAll()
									.requestMatchers("/sample/**").permitAll()
									.requestMatchers("/admin/**").permitAll()
									.requestMatchers("/user/login").permitAll()
									.requestMatchers("/user/logout").permitAll()
									.requestMatchers("/assets/**").permitAll()
									.requestMatchers("/uploads/**").permitAll()
									.anyRequest()
									.authenticated()
				)
		.formLogin(form -> form
				.loginPage("/user/login")
				.successHandler(authenticationSuccessHandler())
				)
		.logout(logout -> logout
				.logoutSuccessUrl("/user/login?logout")
				.invalidateHttpSession(true)
				)
		.cors(cors -> cors.disable());
		
		http.authenticationProvider(ldapProvider);
		http.userDetailsService(userDetailsService);
		return http.build();
	}
	
	@Bean
	public PasswordEncoder passwordEncoder() {
		return new BCryptPasswordEncoder();
	}
	
	@Bean
    public AuthenticationSuccessHandler authenticationSuccessHandler() {
        return successHandler;
    }
}
