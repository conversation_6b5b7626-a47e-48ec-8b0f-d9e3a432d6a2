package com.twport.meeting_backend.aop;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.vo.response.base.AjaxRestResponse;
import com.twport.meeting_backend.vo.response.base.ErrorMessage;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @datetime 2024年7月26日 
 * @class RestApiExceptionHandler
 * @description API接口的錯誤訊息回傳處理
 */
@RestControllerAdvice
@Slf4j
public class RestApiExceptionHandler {
	
	/**
	 * <AUTHOR> Lin
	 * @datetime 2024年7月26日
	 * @method 
	 * @param e
	 * @return
	 * @description 程式發生預期外的錯誤
	 */
	@SuppressWarnings("rawtypes")
	@ExceptionHandler(Exception.class)
	public ResponseEntity<AjaxRestResponse> globalException(Exception e) {
		log.error(e.getMessage(), e);
		HttpStatus httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
		return new ResponseEntity<>(getBaseResponse(ResponseStatusCode.ERROR.getCode(), ResponseStatusCode.ERROR.getMessage()), httpStatus);
	}
	
	/**
	 * 
	 * <AUTHOR> Lin
	 * @datetime 2024年10月7日
	 * @method 
	 * @param e
	 * @param response
	 * @throws IOException
	 * @description 404錯誤
	 */
	@ExceptionHandler(NoResourceFoundException.class)
	public void noResourceFoundException(NoResourceFoundException e, HttpServletResponse response) throws IOException {
		log.error(e.getMessage(), e);		
		response.sendError(HttpStatus.NOT_FOUND.value());
	}
	
	/**
	 * 
	 * <AUTHOR> Lin
	 * @datetime 2024年7月26日
	 * @method 
	 * @param e
	 * @return
	 * @description 專案自訂錯誤
	 */
	@SuppressWarnings("rawtypes")
	@ExceptionHandler(RestApiException.class)
	public ResponseEntity<AjaxRestResponse> restApiException(RestApiException e) {
		log.error(e.getMessage(), e);
		HttpStatus httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
		return new ResponseEntity<>(getBaseResponse(e.getCode(), e.getErrorResult()), httpStatus);
	}
	
	/**
	 * 
	 * <AUTHOR> Lin
	 * @datetime 2024年7月26日
	 * @method 
	 * @param e
	 * @return
	 * @description 檢核錯誤
	 */
	@SuppressWarnings("rawtypes")
	@ExceptionHandler(BindException.class)
	public ResponseEntity<AjaxRestResponse> onBindException(BindException e) {
		log.error(e.getMessage(), e);
		HttpStatus httpStatus = HttpStatus.BAD_REQUEST;
		BindingResult bindingResult = e.getBindingResult();
		
		List<FieldError> fieldErrors = bindingResult.getFieldErrors();
		List<ErrorMessage> errorList = fieldErrors.stream().map(field -> {
			log.warn("bind error field : {}", field.getField());
			log.warn("bind error field message : {}", field.getDefaultMessage());
			return new ErrorMessage(field.getField(), field.getDefaultMessage());
		}).collect(Collectors.toList());
		
		return new ResponseEntity<>(getBaseResponse(ResponseStatusCode.CHECK_FAILED, errorList), httpStatus);
	}
	
	@SuppressWarnings("rawtypes")
	private AjaxRestResponse getBaseResponse(String statusCode, String statusMessage) {
		AjaxRestResponse baseResponse = new AjaxRestResponse(statusCode, statusMessage);
		return baseResponse;
	}
	
	@SuppressWarnings("rawtypes")
	private AjaxRestResponse getBaseResponse(ResponseStatusCode statusCode, List<ErrorMessage> messages) {
		return AjaxRestResponse.rest(messages, statusCode);
	}
	
	@SuppressWarnings("rawtypes")
	private AjaxRestResponse getBaseResponse(String statusCode, List<ErrorMessage> messages) {
		return AjaxRestResponse.rest(messages, statusCode);
	}
}
