package com.twport.meeting_backend.aop;


import java.util.List;

import com.twport.meeting_backend.enums.ResponseStatusCode;
import com.twport.meeting_backend.vo.response.base.ErrorMessage;

import lombok.Getter;

@Getter
public class RestApiException extends RuntimeException {
	
	private static final long serialVersionUID = 1L;

	private final String code;

	private final String msg;
	
	private List<ErrorMessage> errorResult;
	
	public RestApiException(ResponseStatusCode statusCode) {
		super(statusCode.getMessage());
		this.code = statusCode.getCode();
		this.msg = statusCode.getMessage();
	}
	
	public RestApiException(ResponseStatusCode statusCode, List<ErrorMessage> errorResult) {
		super(statusCode.getMessage());
		this.code = statusCode.getCode();
		this.msg = statusCode.getMessage();
		this.errorResult = errorResult;
	}
}
