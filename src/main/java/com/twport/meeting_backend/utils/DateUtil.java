package com.twport.meeting_backend.utils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;

import org.springframework.stereotype.Component;

@Component
public class DateUtil {
	
	/**
	 * 取得當天起時
	 */
	public LocalDateTime getDateStartTime(LocalDate date) {
		return LocalDateTime.of(date, LocalTime.MIDNIGHT);
	}
	
	/**
	 * 取得當天迄時
	 */
	public LocalDateTime getDateEndTime(LocalDate date) {
		return LocalDateTime.of(date, LocalTime.MAX);
	} 
	
	/**
	 * 取得當週第一天
	 */
	public LocalDateTime getWeekFirstDay(LocalDate date) {
		return LocalDateTime.of(date.with(DayOfWeek.MONDAY), LocalTime.MIDNIGHT);
	} 
	
	/**
	 * 取得當週最後一天
	 */
	public LocalDateTime getWeekLastDay(LocalDate date) {
		return LocalDateTime.of(date.with(DayOfWeek.SUNDAY), LocalTime.MAX);
	}
	
	/**
	 * 取得當月第一天
	 */
	public LocalDateTime getMonthFirstDay(LocalDate date) {
		return LocalDateTime.of(date.with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIDNIGHT);
	}
	
	/**
	 * 取得當月最後一天
	 */
	public LocalDateTime getMonthLastDay(LocalDate date) {
		return LocalDateTime.of(date.with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
	}
	
	/**
	 * 取得當天起迄時段
	 */
	public LocalDateTime[] getStartEndByDate(LocalDate date) {
		LocalDateTime[] dates = new LocalDateTime[2];
		dates[0] = getDateStartTime(date);
		dates[1] = getDateEndTime(date);
		return dates;
	}
	
	/**
	 * 取得當週起迄時段
	 */
	public LocalDateTime[] getWeekFirstLastByDate(LocalDate date) {
		LocalDateTime[] dates = new LocalDateTime[2];
		dates[0] = getWeekFirstDay(date);
		dates[1] = getWeekLastDay(date);
		return dates;
	}
	
	/**
	 * 取得當月起迄時段
	 */
	public LocalDateTime[] getMonthStartEndByToday(LocalDate date) {
		LocalDateTime[] dates = new LocalDateTime[2];
		dates[0] = getMonthFirstDay(date);
		dates[1] = getMonthLastDay(date);
		return dates;
	}

	/**
	 * 取得 00 15 30 45 分鐘
	 * 
	 * @param time 當前時間
	 */
	public LocalDateTime getMeetingMinute(LocalDateTime time) {
		int min = time.getMinute();
		int hour = time.getHour();
		if (min < 15 && min > 0) {
			min = 15;
		} else if (min > 15 && min < 30) {
			min = 30;
		} else if (min > 30 && min < 45) {
			min = 45;
		} else if (min > 45) {
			min = 0;
			hour += 1;
		}
		return LocalDateTime.of(time.getYear(), time.getMonth().getValue(), time.getDayOfMonth(), hour, min, 0);
	}

	
	public String convertToChineseFormat(LocalDateTime localDateTime) {
		Integer year = localDateTime.getYear();
		Integer month = localDateTime.getMonth().getValue();
		Integer day = localDateTime.getDayOfMonth();
		Integer date = localDateTime.getDayOfWeek().getValue();
		Integer hour = localDateTime.getHour();
		
		String timeZone = "";
		if(hour / 12 == 0) {
			timeZone = "上午";
		}else {
			timeZone = "下午";
		}
		
		return (year - 1911) + "年" + month + "月" + day + "日" + "(" + convertChineseWeekString(date) + ")" + timeZone + (hour % 12) + "時";
	}
	
	private String convertChineseWeekString(Integer week) {
		switch(week) {
			case 1:
				return "星期一";
			case 2:
				return "星期二";
			case 3:
				return "星期三";
			case 4:
				return "星期四";
			case 5:
				return "星期五";
			case 6:
				return "星期六";
			case 7:
				return "星期日";
			default:
				return "";
		}
	}
}
