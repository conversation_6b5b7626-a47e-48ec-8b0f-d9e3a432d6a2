package com.twport.meeting_backend.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ClientUtils {
	
	public HttpServletRequest getCurrentRequest() {
        try {
        	ServletRequestAttributes servletAttributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
            if (servletAttributes != null) {
                return servletAttributes.getRequest();
            }
        }
        catch (IllegalStateException ex){
            log.warn("No thread-bound request found");
        }
        return null;
    }
    
    public String getClientIP(HttpServletRequest request) {
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotEmpty(ipAddress) && StringUtils.contains(ipAddress, ","))
        {
            String[] proxyIps = ipAddress.split(",");
            ipAddress = proxyIps[0];
        }
        if (StringUtils.isEmpty(ipAddress) || StringUtils.equalsIgnoreCase(ipAddress, "unknown"))
        {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isEmpty(ipAddress) || StringUtils.equalsIgnoreCase(ipAddress, "unknown"))
        {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isEmpty(ipAddress) || StringUtils.equalsIgnoreCase(ipAddress, "unknown"))
        {
            ipAddress = request.getRemoteAddr();
        }
        return ipAddress;
    }
}
