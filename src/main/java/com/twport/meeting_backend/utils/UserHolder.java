package com.twport.meeting_backend.utils;

import java.util.Objects;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import com.twport.meeting_backend.security.AppUserDetails;
import com.twport.meeting_backend.security.LdapUserDetails;
import com.twport.meeting_backend.vo.dto.UserProfile;

public class UserHolder {
	
	private UserHolder() {

	}

	public static UserProfile getCurrentUser() {
		
		UserDetails userDetails = getUserDetail();
		
		if (Objects.isNull(userDetails)) {
			return null;
		}
		
		if(userDetails instanceof AppUserDetails) {
			AppUserDetails userDetail = (AppUserDetails)userDetails;
			return userDetail.getCurrentUser();
		}else {
			LdapUserDetails userDetail = (LdapUserDetails)userDetails;
			return userDetail.getCurrentUser();	
		}
	}

	private static UserDetails getUserDetail() {
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		if (authentication == null || !authentication.isAuthenticated()) {
			return null;
		}
		return (UserDetails) authentication.getPrincipal();
	}
}
