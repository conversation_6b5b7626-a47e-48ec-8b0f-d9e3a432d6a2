package com.twport.meeting_backend.utils;

import java.text.MessageFormat;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @datetime 2024年11月21日
 * @class Utils
 * @description API 工具
 */
@Slf4j
@Component
public class RestApiUtils {

    private static final String LINE = "--------------------------------";

    private final ObjectMapper mapper;

    private final RestTemplate rest;

    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    private String urlFormat = "http://{0}{1}";

    public RestApiUtils(HttpServletRequest httpRequest, ObjectMapper mapper, RestTemplate rest) {
        this.mapper = mapper;
        this.rest = rest;
    }

    /**
     * 呼叫智慧接待 API 取得 QR_CODE
     * 
     * @param <T>
     * @param request
     * @param responseClass
     * @return
     */
    public <T> T getCusu(Object request, Class<T> responseClass) {
        // 檢查當前環境
        boolean isDev = "dev".equals(activeProfile);
        
        // 在開發環境中返回模擬數據
        if (isDev) {
            log.info("開發環境: 略過智慧接待 API 呼叫");
            return null;
        }

        // 正式環境呼叫 API
        String ip = "***********:8080";
        String restUrl = MessageFormat.format(urlFormat, ip, "/cusu/api/datavalue/ResponseTelephoneJSON");
        T response = null;
        try {
            // 【LOG】記錄
            log.info(LINE);
            log.info("智慧接待 API ");
            log.info(LINE);
            log.info("URL: {}", restUrl);
            log.info("REQUEST: {}", mapper.writeValueAsString(request));
            ResponseEntity<T> responseEntity = rest.postForEntity(restUrl, getHttpEntity(request), responseClass);
            response = responseEntity.getBody();
            log.info("RESPONSE: {}", mapper.writeValueAsString(response));
            log.info(LINE);
            return response;
        } catch (HttpClientErrorException | JsonProcessingException e) {
            log.error("【錯誤訊息】{}", e);
            return null;
        }
    }

    /**
     * 添加 Content-Length 設定
     */
    private HttpEntity<Object> getHttpEntity(Object request) throws JsonProcessingException {
        HttpHeaders header = new HttpHeaders();
        header.add("Accept", MediaType.APPLICATION_JSON_VALUE);
        header.setContentType(MediaType.APPLICATION_JSON);
        header.setContentLength(mapper.writeValueAsString(request).getBytes().length);
        return new HttpEntity<>(request, header);
    }

}
