table tbody {
	color: rgba(255, 255, 255);
    display: block;
    height: 500px;
    overflow-y: scroll;
}

table thead,
tbody tr {
	color: rgba(255, 255, 255);
    display: table;
    width: 100%;
    table-layout: fixed;
}

table thead {
	color: rgba(255, 255, 255);
    width: calc(100% - 15px);
}

table tbody tr td{
    overflow: visible;
}

/* width */
table tbody::-webkit-scrollbar {
    width: 10px;
   
}

/* Track */
table tbody::-webkit-scrollbar-track {
    background: #2F313C;
}

/* Handle */
table tbody::-webkit-scrollbar-thumb {
    background: rgba(99, 99, 99, 0.5);
    border-radius: 18px;
}

/* Handle on hover */
table tbody::-webkit-scrollbar-thumb:hover {
    background: rgba(99, 99, 99, 1);
}

/* CSS for highlighting table rows on hover */
table tbody tr td:hover[name=noMeeting] {
    background-color: rgba(99, 99, 99, 0.60);
    cursor: pointer;
}

/* ownMeeting */
table tbody tr td div[name=ownMeeting] {
	color: rgba(38, 38, 38, 1);
	background-color: rgba(255, 215, 173, 1);
}

/* ownMeeting hover */
table tbody tr td div:hover[name=ownMeeting] {
	color: rgba(38, 38, 38, 1);
    background-color: rgba(244, 164, 96, 1);
    cursor: pointer;
}

/* ownMeeting */
input[name=ownMeeting] {
	color: rgba(38, 38, 38, 1);
	background-color: rgba(255, 215, 173, 1);
}

/* ownMeeting hover */
input:hover[name=ownMeeting] {
	color: rgba(38, 38, 38, 1);
    background-color: rgba(244, 164, 96, 1);
    cursor: pointer;
}

/* otherMeeting */
input[name=otherMeeting] {
	color: rgba(38, 38, 38, 1);
	background-color:  rgba(169, 169, 169, 1);
}

/* otherMeeting hover */
input:hover[name=otherMeeting] {
	color: rgba(38, 38, 38, 1);
    background-color:  rgba(143, 143, 143, 1);
    cursor: pointer;
}

/* otherMeeting */
table tbody tr td div[name=otherMeeting] {
	color: rgba(38, 38, 38, 1);
    background-color: rgba(169, 169, 169, 1);
}

/* otherMeeting hover */
table tbody tr td div:hover[name=otherMeeting] {
	color: rgba(38, 38, 38, 1);
    background-color: rgba(143, 143, 143, 1);
    cursor: pointer;
}

#reduceDate{
    width: 20px;
    height: 30px;
    background-color: transparent;
    margin-top: 10px;
}
#addDate{
    width: 20px;
    height: 30px;
    background-color: transparent;
    margin-top: 10px;
}
#deleteBtn{
    width: 30px;
    height: 40px;
    background-color: transparent;
}
#checkPic{
    width: 30px;
    height: 40px;
    background-color: transparent;
}

.light-model {
	background-color: white;
	border-radius: 8px;

	::placeholder {
		color: white;
	}

	.content-search {
		background-color: rgba(218, 220, 224, 0.6); 
	
	}

	table tbody {
		color: rgba(73, 80, 87, 1);
	}

	table thead,
	tbody tr {
		color: rgba(73, 80, 87, 1);
	}

	table thead {
		color: rgba(73, 80, 87, 1);
	}
	
/* 	table tbody tr:hover { */
/* 		background-color: rgba(218, 220, 224, 0.6); */
/* 		border-radius: 8px; */
/* 	} */
	table tbody tr td:hover[name=noMeeting] {
	    background-color: rgba(99, 99, 99, 0.20);
	    cursor: pointer;
	}

}
.content-search {
	margin-left: 0px;
	padding: 10px;
	border-radius: 5px;
	background-color: rgba(255, 255, 255, 0.098);
}

.content-search input[type=checkbox] {
	box-shadow: none;
	border: none;
}

.content-search input[type=checkbox]:checked {
	background-color: gray;
}

.content-search input:focus {
	outline: none;
	border-color: aliceblue;
}

table #daily_table #firstChild {
	position: absolute;
}

label.required::after {
	content: '＊';
	color: #ff5400;
}
