.datatable {
	padding-top: 20px;
	margin-top: 20px;
	margin-bottom: 40px;
}

.light-model {
	background-color: white;
	border-radius: 8px;

	::placeholder {
		color: white;
	}

	.content-search {
		background-color: rgba(218, 220, 224, 0.6);

		input[type=text] {
			background-color: rgba(59, 80, 90, 1);
			background-image: url("../images/search-light.png");
		}
	}

	table thead tr th {
		color: rgba(73, 80, 87, 1);
	}

	table tbody tr td {
		color: rgba(73, 80, 87, 1);
	}

	table tbody tr:hover {
		background-color: rgba(218, 220, 224, 0.6);
		border-radius: 8px;
	}

}

/*.select-div {*/
/*	width: 12%;*/
/*	flex: 0 0 auto;*/
/*}*/

.select-div-long {
	width: 18%;
	flex: 0 0 auto;
}

.shadow-gray {
	border: solid 1px;
	border-color: rgba(198, 199, 248, 1);
	box-shadow: 0px 0px 0px 3.2px rgba(198, 199, 248, 0.25);
}

.content-search {
	margin-top: 5px;
	margin-left: 15px;
	margin-right: 15px;
	padding: 10px;
	border-radius: 5px;
	background-color: rgba(255, 255, 255, 0.098);
}

.content-search input[type=text] {
	border: 1px solid;
	border-color: rgba(255, 255, 255, 0.1);
	border-radius: 8px;
	background-color: rgba(28, 28, 28, 0.4);
	background-image: url("../images/search.png");
	background-position: 10px 11px;
	background-repeat: no-repeat;
	padding-left: 30px;
	padding-right: 4px;
	padding-top: 8px;
	padding-bottom: 8px;
	color: white;
}

.content-search input[type=checkbox] {
	box-shadow: none;
	border: none;
}

.content-search input[type=checkbox]:checked {
	background-color: gray;
}

.content-search input:focus {
	outline: none;
	border-color: aliceblue;
}

.datatable-table {
	margin-top: 20px;
	margin-left: 15px;
	margin-right: 15px;
}

table thead tr th {
	font-weight: 400;
	color: rgba(255, 255, 255, 0.4);
	padding-left: 8px;
	padding-right: 8px;
	padding-top: 12px;
	padding-bottom: 12px;
	flex: 0 0 auto;
	word-wrap:break-word;
	word-break:break-all;
}

table tbody tr td {
	font-weight: 400;
	color: rgba(255, 255, 255);
	padding-left: 8px;
	padding-right: 8px;
	padding-top: 12px;
	padding-bottom: 12px;
	flex: 0 0 auto;
	word-wrap:break-word;
	word-break:break-all;
}

table tbody tr:hover {
	background-color: rgba(255, 255, 255, 0.05);
	border-radius: 8px;
}

.datatable-h3 {
	color: rgba(122, 125, 130, 1);
	font-weight: 700;
	margin-top: 50px;
}

.page_btn {
	background-color: rgba(218, 220, 224, 0);
	border: none; 
	border-radius: 8px;
}

.page_btn:hover, .page_btn_active:hover {
	background-color: rgba(218, 220, 224, 0.4);
}

.page_btn_active {
	background-color: rgba(218, 220, 224, 0.6);
	border: none; 
	border-radius: 8px;
}