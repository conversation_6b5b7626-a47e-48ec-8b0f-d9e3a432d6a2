/*.search_Content {*/
/*    position: absolute;*/
/*    inset: 50px 50px 100px 300px;*/
/*    display: flex;*/
/*    flex-direction: column;*/
/*    align-items: flex-start;*/
/*    gap: 20px;*/
/*    !*padding: 100px 100px 50px 100px;*!*/
/*    box-sizing: border-box;*/
/*    !*width: 100%;*!*/
/*    max-width: initial; !* 最大宽度 *!*/
/*    background-color: #2c3e50; !* 背景颜色 測試用*!*/
/*}*/

.search_Title {
    font-family: Arial, Helvetica, serif;
    font-weight: 600;
    color: rgb(255, 255, 255);
    margin-bottom: 10px;
}

.search_description {
    font-family: Arial, Helvetica, serif;
    color: rgb(255, 255, 255);
}


.search_Bar {
    display: flex;
    gap: 10px;
    width: 100%;
    justify-content: space-between;
}

.search_Bar select{
    flex: 1;
    border-radius: 10px ;
    /*margin-right: 10px;*/
}
.search_Bar input{
    flex: 1;
    border-radius: 10px ;
    /*margin-right: 10px;*/
}


.search_Table {
    font-family: Arial, Helvetica, serif;
    font-weight: 600;
    color: rgb(255, 255, 255);
    /*margin-bottom: 10px;*/

    width: 100%;
    margin-top: 20px;
    background-color: #ecf0f1;
    padding: 10px;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.098);
}

searchbtn {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 210px;
    padding: 15px;
    box-sizing: border-box;
    border: none;
    background: white;
    cursor: pointer;
    border-radius: 10px;
}

/*.appointment {*/
/*    !*position:absolute;*!*/
/*    bottom: 20px;*/
/*    display: flex;*/
/*    align-items: center;*/
/*    gap: 10px;*/
/*    width: 210px;*/
/*    padding: 15px;*/
/*    box-sizing: border-box;*/
/*    border: none;*/
/*    background: linear-gradient(to right, #394359, #232731);*/
/*    cursor: pointer;*/
/*    border-radius: 10px;*/
/*}*/

/*.appointment_Img {*/
/*    width: 40px;*/
/*    height: 40px;*/
/*    background-size: cover;*/
/*    border-radius: 50%;*/
/*}*/

/*.appointment_Txt {*/
/*    font-family: Arial, Helvetica, serif;*/
/*    font-size: 17px;*/
/*    ont-weight: 600;*/
/*    color: rgb(255, 255, 255);*/
/*}*/
