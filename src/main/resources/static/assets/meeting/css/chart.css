.row1 {
   display: grid;
   grid-template-columns:40% 60%;
  
  
}
.row2  {
	display: grid;
	grid-template-columns: 100%;
	grid-template-rows: 400px;
	grid-auto-flow: row
}

#line {
	border-radius: 10px;
	background-color: rgb(211, 211, 211, 0.25);
}
#pie {
	border-radius: 10px;
	background-color: rgb(211, 211, 211, 0.25);
}

#bar {
	border-radius: 10px;
	background-color: rgb(211, 211, 211, 0.25);
	width: 100%;
}