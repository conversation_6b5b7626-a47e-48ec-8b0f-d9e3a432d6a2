/**
 * 共用參數
 */
const contentPath = "/meeting-reserve";

/**
 * 判斷是否是 null / undefinded / 空
 */
function isBlank(str) {
	if (str === null || str === undefined) {
		return true
	} else if (str.trim() === '') {
		return true
	} else {
		return false;
	}
}

/**
 * AJAX 包裝
 */
function ajaxSubmit(ajaxData, ...args) {
	const token = $("meta[name='_csrf']").attr("content");
	const header = $("meta[name='_csrf_header']").attr("content");
	const beforeSend = ajaxData.beforeSend
	const success = ajaxData.success
	const complete = ajaxData.complete
	const error = ajaxData.error
	const dataType = isBlank(ajaxData.dataType) ? "json" : ajaxData.dataType
	const contentType = isBlank(ajaxData.contentType) ? "application/json" : ajaxData.contentType
	const method = isBlank(ajaxData.method) ? "post" : ajaxData.method
	// dataType如果是binary，要加上xhrField這個參數
	const xhrField = dataType == 'binary' ? {responseType: 'blob'} : ''
	const url = contentPath + ajaxData.url
	const data = ajaxData.data;
	$.ajax({
		headers: {
			[header]: token
		},
		url: url,
		method: method,
		dataType: dataType,
		data: JSON.stringify(data),
		contentType: contentType,
		xhrFields: xhrField,
		beforeSend: function() {
			loadingModalShow();
			if (typeof (beforeSend) === "function") {
				beforeSend(...args)
			}
		},
		success: function(resp, status, xhr) {
			if (typeof (success) === "function") {
				success(resp, ...args)
			}
		},
		complete: function() {
			loadingModalHide();
			if (typeof (complete) === "function") {
				complete(...args)
			}
		},
		error: function(resp) {
			if (typeof (error) === "function") {
				error(resp.responseJSON, ...args)
			} else if (resp.responseJSON != undefined) {
				let errorMessage = "";
				for (const index in resp.responseJSON.errors) {
					const error = resp.responseJSON.errors[index]
					errorMessage += error.defaultMessage
					if (index != resp.responseJSON.errors.length) {
						errorMessage += " , "
					}
				}
			}
		}
	})
}