let viewMeetingImg = "../assets/meeting/images/reserve-info-white.png";
let viewSignImg = "../assets/meeting/images/sign-record-white.png";
let paginationBtnClass = "text-light";
let paginationleftBtn = "../assets/meeting/images/leftArrow.png";
let paginationRightBtn = "../assets/meeting/images/rightArrow.png";
let pageSizeClass = "text-light";

let companyOptions = [];
let companyId = "";
let departmentId = "";
let departmentName = "";

/**
 * 顏色設定初始化
 */
function colorChangeInit() {
  if ($("#colorChange").prop("checked")) {
    viewMeetingImg = "../assets/meeting/images/reserve-info-black.png";
    viewSignImg = "../assets/meeting/images/sign-record-black.png";
    paginationBtnClass = "text-dark";
    paginationleftBtn = "../assets/meeting/images/leftArrowLightMode.png";
    paginationRightBtn = "../assets/meeting/images/rightArrowLightMode.png";
    pageSizeClass = "text-dark";
    $("#colorChangeText").text("淺色版");
    $("#colorChangeText").removeClass("text-light");
    $("#colorChangeText").addClass("text-dark");
    $("#datatable").addClass("light-model");
    $("button[name=page]").removeClass("text-light");
    $("#pageSize").removeClass("text-light");
  } else {
    viewMeetingImg = "../assets/meeting/images/reserve-info-white.png";
    viewSignImg = "../assets/meeting/images/sign-record-white.png";
    paginationBtnClass = "text-light";
    paginationleftBtn = "../assets/meeting/images/leftArrow.png";
    paginationRightBtn = "../assets/meeting/images/rightArrow.png";
    pageSizeClass = "text-light";
    $("#colorChangeText").text("深色版");
    $("#colorChangeText").removeClass("text-dark");
    $("#colorChangeText").addClass("text-light");
    $("#datatable").removeClass("light-model");
    $("button[name=page]").removeClass("text-dark");
    $("#pageSize").removeClass("text-dark");
  }

  $(".datatable-table tbody img.view-meeting").attr("src", viewMeetingImg);
  $(".datatable-table tbody img.view-sign").attr("src", viewSignImg);
  $("button[name=page]").addClass(paginationBtnClass);
  $("#paging #left_arrow img").attr("src", paginationleftBtn);
  $("#paging #right_arrow img").attr("src", paginationRightBtn);
  $("#pageSize").addClass(pageSizeClass);
}

/**
 * 樓層下拉選單變動事件
 */
function floorSelectChangeEvent(value) {
  $("#room").empty().append(`<option value="" selected>會議室</option>`);
  initSelectOption.floorSelects.forEach((floorSelect) => {
    if (floorSelect.value == value) {
      floorSelect.subOptions.forEach((roomSelect) => {
        $("#room").append(
          `<option value="${roomSelect.value}">${roomSelect.label}</option>`
        );
      });
    }
  });
}

/**
 * 查詢事件
 */
function search() {
  const data = {
    sort: $("#sort").val(),
    departmentId: $("#department").val(),
    floor: $("#floor").val(),
    room: $("#room").val(),
    startDate: $("#startDate").val(),
    endDate: $("#endDate").val(),
    keyword: $("#resultSearch").val(),
  };

  const ajax = {
    url: "/meeting/record/query",
    data: data,
    success: searchSuccess,
  };

  query(ajax);
}

function searchSuccess(resp) {
  const pageContent = resp.result;
  // 顯示無資料或分頁器
  if (pageContent.totalSize == 0) {
    $("#noDataTxt").css("display", "block");
    $("#paging").addClass("d-none");
  } else {
    $("#noDataTxt").css("display", "none");
    $("#paging").removeClass("d-none");
  }
  // 組合資料版面
  let tbodyHtml = "";
  pageContent.contents.forEach((item) => {
    let color = "";
    switch (item.statusDisplay) {
      case "已預約":
        color = "#4AA785";
        break;
      case "進行中":
        color = "#8A8CD9";
        break;
      case "已結束":
        color = "#59A8D4";
        break;
      case "取消":
        color = "gray";
        break;
    }
    tbodyHtml += `
		<tr class='row' style="${
      item.theme === "不公開" ? "pointer-events: none;" : ""
    }">
			<td style="width: 16%;">${item.theme}</td>
			<td style="width: 14%;white-space: nowrap;">${item.roomNameDisplay}</td>
			<td style="width: 6%;">${item.hostUser}</td>
			<td style="width: 10%;">${item.department}</td>
			<td style="width: 14%; ">${item.reserveUser}/
			<br/>
			${item.reserveUserPhone}
			</td>
			<td style="width: 10%;text-align:center;">${item.reserveDate}</td>
			<td style="width: 11%;text-align:center;">${item.reserveTime}</td>
			<td style="width: 7%;text-align:center;color:${color}">${
      item.statusDisplay
    }</td>
			<td style="width: 12%;display: flex; justify-content: center; flex-wrap: nowrap; gap: 10px;">
				<img class="mx-2 view-meeting" data-meetingid='${
          item.meetingId
        }' style="width: 30px;height: 25px;" src="${viewMeetingImg}" title="查看"/>
				<img class="mx-2 view-sign" data-meetingid='${
          item.meetingId
        }' style="width: 25px;height: 30px;" src="${viewSignImg}" title="簽到紀錄"/>
			</td>
		</tr>
		`;
  });
  $("#datatable tbody").html(tbodyHtml);

  $("div [name=page]").remove();
  // 組合分頁器版面
  let pageHtml = "";
  // 總頁數
  const totalNum = pageContent.totalPage;
  // 當前頁數
  const currentNum = pageContent.currentPage;
  // 最小值
  let minNum = 0;
  if (currentNum > 2) minNum = currentNum - 2;

  for (let i = minNum; i < minNum + 5; i++) {
    if (i > totalNum - 1) {
      break;
    }
    pageHtml += getPageNum(currentNum == i ? "page_btn_active" : "", i);
  }
  if (totalNum - currentNum > 3) {
    pageHtml += 
	`<div name="page" class="d-flex align-items-center">
		...
	</div>`;
    pageHtml += getPageNum("", totalNum - 1);
  }

  $("#left_arrow").parent().after(pageHtml);

  // 是否顯示上一頁
  if (pageContent.isFirstPage) {
    $("#left_arrow").addClass("d-none");
  } else {
    $("#left_arrow").removeClass("d-none");
  }

  // 是否顯示下一頁
  if (pageContent.isLastPage) {
    $("#right_arrow").addClass("d-none");
  } else {
    $("#right_arrow").removeClass("d-none");
  }
}

// 取得分頁樣式
function getPageNum(active, num) {
  return `<div name="page" class="d-flex align-items-center">
		<button 
			class="fs-5 ${paginationBtnClass} ${active} px-3 mx-1 page_btn" 
			onclick="specifyPage(${num})">
			${num + 1}
		</button>
	</div>`;
}

function signedDetailEvent(meetingId) {
  const data = {
    reserveId: meetingId,
  };
  const ajax = {
    url: "/meeting/record/signIn",
    data: data,
    success: signedDetailSuccess,
  };
  ajaxSubmit(ajax);
}

function signedDetailSuccess(response) {
  if (response.result.length > 0) {
    let tbodyHtml = "";
    response.result.forEach((sign) => {
      let editBtnHTML = `<input type="button" 
								class="col-12 btn rounded-pill btn-purple edit-sign-record" 
								data-signinid='${sign.signInId}' 
								value="編輯"/>`;

      if (sign.type == "識別證") {
        editBtnHTML = `<input type="button" 
									class="col-12 btn rounded-pill btn-purple edit-sign-record" 
									data-signinid='${sign.signInId}' 
									value="編輯" disabled/>`;
      }

      tbodyHtml +=
        `<tr class="row">
								<td style="width: 20%;">${sign.departmentName}</td>
								<td style="width: 20%;">${sign.jobTitle}</td>
								<td style="width: 20%;">${sign.type}</td>
								<td style="width: 20%;">${sign.signTime}</td>
								<td style="width: 20%;" class="row">` +
        editBtnHTML +
        `</td></tr>`;
    });
    $("#signInModal tbody").empty().html(tbodyHtml);
    $("#signInRecord").css("display", "");
    $("#noSignInRecord").css("display", "none");
    $("#fileDownloadBtn").css("display", "");
  } else {
    $("#signInRecord").css("display", "none");
    $("#noSignInRecord").css("display", "");
    $("#fileDownloadBtn").css("display", "none");
  }
}

/**
 * 各會議細節
 */
function meetingDetailEvent(meetingId, detailType) {
  const data = {
    meetingId: meetingId,
    reserveType: detailType,
  };

  const ajax = {
    url: "/meeting/reserve/details",
    data: data,
    success: meetingDetailSuccess,
  };
  ajaxSubmit(ajax);
}

function meetingDetailSuccess(response) {
  console.log(response);
  writeModalForRead(response.result);
}

function signRecordFileEvent(meetingId) {
  const data = {
    reserveId: meetingId,
  };
  console.log("data", data);
  const ajax = {
    url: "/meeting/record/signFile",
    data: data,
    dataType: "binary",
    success: signRecordFileSuccess,
  };
  ajaxSubmit(ajax);
}

function signRecordFileSuccess(response) {
  console.log(response);

  const downloadUrl = (window.URL || window.webkitURL).createObjectURL(
    response
  );
  const download_link = document.createElement("a");
  download_link.href = downloadUrl;
  download_link.download = "簽到紀錄表.pdf";
  document.body.appendChild(download_link);
  download_link.click();

  setTimeout(function () {
    URL.revokeObjectURL(downloadUrl);
    download_link.remove();
  }, 1000);
}

function signEditInitEvent(signInId) {
  const data = {
    signInId: signInId,
  };
  console.log("data", data);
  const ajax = {
    url: "/meeting/record/signEditInit",
    data: data,
    success: signEditInitSuccess,
  };
  ajaxSubmit(ajax);
}

function signEditInitSuccess(response) {
  console.log(response);
  companyOptions = response.result.companyOptions;
  companyId = response.result.companyId;
  departmentId =
    response.result.departmentId != null
      ? response.result.departmentId
      : "other";

  departmentName =
    departmentId == "other" ? response.result.departmentName : "";

  companySelectChangeEventInSignEditModal();

  $("#modal-edit-jobTitle").val(response.result.jobTitle);
  $('input[id="signInType"]').each(function () {
    if ($(this).val() == response.result.type) {
      $(this).prop("checked", true);
    }
  });

  if (response.result.type == "SIGNIN") {
    $("#modal-edit-name")
      .empty()
      .append(`<img width="100%" src="data:image/jpeg;base64, ${response.result.signInImageUrl}">`);
  } else {
    $("#modal-edit-name")
      .empty()
      .append(`<h3>${response.result.signInUserName}</h3>`);
  }

  $("#confirmBtn").data("signinid", response.result.signInId);
}

function companySelectChangeEventInSignEditModal() {
  console.log(companyId);
  console.log(departmentId);
  if (companyId == null || companyId == 0 || companyId == "") {
    $("#modal-edit-company-select")
      .empty()
      .append(`<option value="" selected>請選擇</option>`);
  } else {
    $("#modal-edit-company-select")
      .empty()
      .append(`<option value="">請選擇</option>`);
  }

  if (departmentId == 0 || departmentId == "") {
    $("#modal-edit-department-select")
      .empty()
      .append(`<option value="" selected>請選擇</option>`);
  } else {
    $("#modal-edit-department-select")
      .empty()
      .append(`<option value="">請選擇</option>`);
  }

  companyOptions.forEach((company) => {
    if (company.value == companyId) {
      $("#modal-edit-company-select").append(
        `<option value="${company.value}" selected>${company.label}</option>`
      );

      company.subOptions.forEach((depart) => {
        if (depart.value == departmentId) {
          $("#modal-edit-department-select").append(
            `<option value="${depart.value}" selected>${depart.label}</option>`
          );
        } else {
          $("#modal-edit-department-select").append(
            `<option value="${depart.value}">${depart.label}</option>`
          );
        }
      });
    } else {
      $("#modal-edit-company-select").append(
        `<option value="${company.value}">${company.label}</option>`
      );
    }
  });

  if (departmentId == "other") {
    $("#modal-edit-department-select").append(
      `<option value="other" selected>其他單位</option>`
    );
  } else {
    $("#modal-edit-department-select").append(
      `<option value="other">其他單位</option>`
    );
  }

  departmentSelectChangeEventInSignEditModal();
}

function departmentSelectChangeEventInSignEditModal() {
  console.log(departmentId);
  $("#modal-edit-department-text").val(departmentName);
  if (departmentId != "other") {
    $("#modal-edit-company-select").attr("disabled", false);
    $("#modalEditDepartText").css("display", "none");
  } else {
    $("#modal-edit-company-select").attr("disabled", true);
    $("#modalEditDepartText").css("display", "");
  }
}

function signRecordEditConfirm(signInId) {
  const data = {
    signInId: signInId,
    departmentId: departmentId,
    departmentName: departmentName,
    jobTitle: $("#modal-edit-jobTitle").val(),
  };
  console.log("data", data);
  const ajax = {
    url: "/meeting/record/signInEdit",
    data: data,
    success: signEditConfirmSuccess,
  };
  ajaxSubmit(ajax);
}

function signEditConfirmSuccess(response) {
  console.log(response);
  $("#signInEditModal").modal("hide");
  $("#signInModal").modal("show");
}

/**
 * 繪製明細Modal
 */
function writeModalForRead(result) {
  console.log(result);
  $("#detail-theme").text(result.theme);
  $("#detail-time").text(
    result.reserveDate +
      " " +
      result.reserveStartTime +
      " ~ " +
      result.reserveEndTime
  );
  $("#detail-floor").text(result.floor);
  $("#detail-roomname").text(result.roomName);
  $("#detail-hostname").text(result.hostName);
  $("#detail-contactname").text(result.contactName);
  $("#detail-contactphone").text(result.contactPhone);
  $("#detail-deptart").text(result.departmentName);

  let attendDept = "";
  result.reserveDepartmentsName.forEach((value, index) => {
    attendDept += value;
    if (index != result.reserveDepartmentsName.length - 1) {
      attendDept += " , ";
    }
  });
  $("#detail-attenddeptart").text(attendDept);

  $("#detail-meetingmode").text(result.meetingModeName);
  $("#detail-situationmode").text(result.situationModeName);
  $("#detail-members").text(result.members);
}
