$(function() {
	let meetingId = 0;
	let signInId = 0;

	$("#floor").change(function() {
		floorSelectChangeEvent($(this).val());
	})

	// 查詢欄位清除
	$("#cleanBtn").click(function() {
		$("select[name=query-form]").val("");
		$("input[name=query-form]").val("");
		$("#room").empty().append(
			`<option value="" selected>會議室</option>`
		);

		initSelectOption.meetingRoomSelects.forEach(roomSelect => {
			$("#room").append(
				`<option value="${roomSelect.value}">${roomSelect.label}</option>`
			);
		});

		search();
	})

	// 下拉選單查詢
	$("select[name=query-form]").change(function() {
		search();
	})

	// 日期元件查詢
	$("input[name=query-form]").change(function() {
		search();
	})

	// 排序查詢
	$("#sort").change(() => search());

	$("#colorChange").change(function() {
		colorChangeInit();
	})

	$("#left_arrow").click(function() {
		prevPage();
	})

	$("#right_arrow").click(function() {
		nextPage();
	})

	$('table tbody').on('click', '.view-meeting', function() {
		meetingId = $(this).data('meetingid');
		$('#reserveDetailModal').modal('show');
	});

	$('table tbody').on('click', '.view-sign', function() {
		meetingId = $(this).data('meetingid');
		$('#signInModal').modal('show');
	});

	$('#reserveDetailModal').on('show.bs.modal', function() {
		meetingDetailEvent(meetingId, "ownMeeting");
	});

	$('#signInModal').on('show.bs.modal', function() {
		signedDetailEvent(meetingId);
	});

	$('#fileDownloadBtn').click(function() {
		signRecordFileEvent(meetingId);
	});

	$('table tbody').on('click', '.edit-sign-record', function() {
		signInId = $(this).data('signinid');
		$('#signInModal').modal('hide');
		$('#signInEditModal').modal('show');
	});

	$('#signInEditModal').on('show.bs.modal', function() {
		signEditInitEvent(signInId);
	});

	$("#signInEditModal").on('hide.bs.modal', function() {
		$('#signInModal').modal('show');
	});

	$("#modal-edit-company-select").change(function() {
		companyId = $(this).val();
		$("#modal-edit-department-select").val('');
		departmentId = $("#modal-edit-department-select").val();
		companySelectChangeEventInSignEditModal();
	});

	$("#modal-edit-department-select").change(function() {
		departmentId = $(this).val();
		departmentSelectChangeEventInSignEditModal();
	});

	$("#modalConfirmBtn").click(function() {
		signRecordEditConfirm(signInId);
	});

	$("#modalCleanBtn").click(function() {
		signEditInitEvent(signInId);
	});

	$('#resultSearch').keydown(function(e) {
		if (e.key == 'Enter') {
			search();
		}
	});

	$('#editPageSize').change(function() {
		changePageSize($('#editPageSize').val());
	});
	
	init();
});

// 畫面初始化
function init() {
	colorChangeInit();
	search();
}
