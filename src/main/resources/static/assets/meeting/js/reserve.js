// 日期初始化
let nowDateTime = new Date();
let nowY = nowDateTime.getFullYear();
let nowM = nowDateTime.getMonth() + 1;
nowM = nowM >= 10 ? nowM : "0" + nowM;
let nowD = nowDateTime.getDate();
nowD = nowD >= 10 ? nowD : "0" + nowD;
let nowDateTimeStr = nowY + "-" + nowM + "-" + nowD;
let reserveModalType = "addMeeting";
$(function () {
  let calendarType = "daily";

  $("#beginTime").change(function () {
    const b = $(this).val();
    const e = $("#endTime").val();
    const b_time = b.split(":");
    const e_time = e.split(":");
    const hour = Number(b_time[0]) + 1;
    const min = b_time[1];
    if (Number(b_time[0]) >= Number(e_time[0]))
      $("#endTime").val(`${hour.toString().padStart(2, "0")}:${min}`);
  });

  $("#date").val(nowDateTimeStr);
  // 各MODAL初始化
  $("#cancelModal").modal({ backdrop: "static", keyboard: false });

  let editAndAddReserveDate = "";
  let editAndAddStartTime = "";
  let editAndAddEndTime = "";

  let meetingId = "";
  let isDeleteEvent = false;
  let reserveConfimEvent = false;
  let cancelEvent = false;

  let dropDownSelectType = "";

  /** 篩選條件區塊的各元件觸發事件設定 START */
  // 全選樓層按鈕
  $("input[name=floorBtnAll]").click(function () {
    if ($(this).hasClass("active")) {
      $(this).removeClass("active");
      $("input[name=floorBtn]").removeClass("active");
      $("input[name=roomBtnAll]").removeClass("active");
      $("input[name=roomBtnAll]").attr("disabled", true);
    } else {
      $(this).addClass("active");
      $("input[name=floorBtn]").addClass("active");
      $("input[name=roomBtnAll]").addClass("active");
      $("input[name=roomBtnAll]").attr("disabled", false);
    }
    changeRoomItem();
    queryMeetingEvent(calendarType);
  });

  // 樓層按鈕
  $("input[name=floorBtn]").click(function () {
    if ($(this).hasClass("active")) {
      $(this).removeClass("active");
      $("input[name=floorBtnAll]").removeClass("active");

      let hasActive = false;
      $("input[name=floorBtn]").each(function () {
        if ($(this).hasClass("active")) {
          hasActive = true;
        }
      });

      if (!hasActive) {
        $("input[name=roomBtnAll]").removeClass("active");
        $("input[name=roomBtnAll]").attr("disabled", true);
      }
    } else {
      $(this).addClass("active");
      $("input[name=roomBtnAll]").addClass("active");
      $("input[name=roomBtnAll]").attr("disabled", false);

      let allActive = true;
      $("input[name=floorBtn]").each(function () {
        if (!$(this).hasClass("active")) {
          allActive = false;
        }
      });
      if (allActive) {
        $("input[name=floorBtnAll]").addClass("active");
      }
    }
    changeRoomItem();
    queryMeetingEvent(calendarType);
  });

  // 全選會議室按鈕
  $("input[name=roomBtnAll]").click(function () {
    if ($(this).hasClass("active")) {
      $(this).removeClass("active");
      $("input[name=roomBtn]").removeClass("active");
    } else {
      $(this).addClass("active");
      $("input[name=roomBtn]").addClass("active");
    }

    queryMeetingEvent(calendarType);
  });

  // 會議室按鈕
  $("input[name=roomBtn]").click(function () {
    if ($(this).hasClass("active")) {
      $(this).removeClass("active");
      $("input[name=roomBtnAll]").removeClass("active");
    } else {
      $(this).addClass("active");
      let allActive = true;
      $("input[name=roomBtn]").each(function () {
        if (!$(this).hasClass("active")) {
          allActive = false;
        }
      });
      if (allActive) {
        $("input[name=roomBtnAll]").addClass("active");
      }
    }

    queryMeetingEvent(calendarType);
  });

  $("#date").on("change", function () {
    queryMeetingEvent(calendarType);
  });

  $("#searchBtn").click(function () {
    queryMeetingEvent(calendarType);
  });

  /** 篩選條件區塊的各元件觸發事件設定 END */

  /** 行事曆區塊的各元件觸發事件設定 START */
  // 行事曆切換事件(月 / 週 / 日)
  setCalendarSelectChanged(function (value) {
    calendarType = value;
    queryMeetingEvent(calendarType);
    scrollToTarget(calendarType, `#scroll-container-${calendarType}`, 8);
  });

  //標頭 left 調整日期
  $("#reduceDate").click(function () {
    let nowDateTimeStr = previousCalendar($("#date").val());
    $("#date").val(nowDateTimeStr);
    queryMeetingEvent(calendarType);
  });

  //標頭 right 調整日期
  $("#addDate").click(function () {
    let nowDateTimeStr = nextCalendar($("#date").val());
    $("#date").val(nowDateTimeStr);
    queryMeetingEvent(calendarType);
  });

  //點擊「查看更多會議」
  $("table tbody").on("click", "div[id=moreMeeting]", function (event) {
    event.stopPropagation();
    let date = $(this).closest("td").attr("data-info");

    const dateArr = date.split("-");
    // 長度為4時，代表是從週曆點擊
    if (dateArr.length == 4) {
      let weekDay = dateArr[0] + "-" + dateArr[1] + "-" + dateArr[2];
      let startTime = dateArr[3] + ":" + "00:00";
      let endTime =
        String(parseInt(dateArr[3]) + 1).padStart(2, "0") + ":" + "00:00";
      queryDetailEvent(weekDay, startTime, endTime);
    } else {
      queryDetailEvent(date, null, null);
    }
  });

  // 修改會議 按鈕 (週月)
  $("table tbody").on("click", "input[name=ownMeeting]", function (event) {
    event.stopPropagation();
    reserveModalType = $(this).attr("name");
    meetingId = $(this).data("meetingid");
    $("#reserveDetailModal").modal("show");
  });

  // 瀏覽會議 按鈕 (週月)
  $("table tbody").on("click", "input[name=otherMeeting]", function (event) {
    event.stopPropagation();
    reserveModalType = $(this).attr("name");
    meetingId = $(this).data("meetingid");
    $("#reserveDetailModal").modal("show");
  });

  // 修改/瀏覽會議 div (日)
  $("table tbody").on("click", "td div[data-type=edit]", function () {
    reserveModalType = $(this).attr("name");
    meetingId = $(this).closest("td").data("meetingid");
    if (
      reserveModalType == "ownMeeting" ||
      reserveModalType == "otherMeeting"
    ) {
      $("#reserveDetailModal").modal("show");
    }
  });

  //純新增預約
  $("table tbody").on(
    "click",
    "td[data-type=dailyDay],td[data-type=weeklyDay], td[data-type=monthlyDay]",
    function () {
      reserveModalType = "addMeeting";
      let selectDateSplit = $(this).data("info").split("-");

      //日曆
      if (calendarType == "daily") {
        dailyAddRoomId = selectDateSplit[0];
        let hour = selectDateSplit[1];

        editAndAddReserveDate = $("#date").val();
        editAndAddStartTime = hour + ":00";
        editAndAddEndTime =
          (Number(hour) + 1).toString().padStart(2, "0") + ":00";
      } else if (calendarType == "weekly") {
        let year = selectDateSplit[0];
        let month = selectDateSplit[1];
        let day = selectDateSplit[2];
        let hour = selectDateSplit[3];

        editAndAddReserveDate = year + "-" + month + "-" + day;
        editAndAddStartTime = hour + ":00";
        editAndAddEndTime =
          (Number(hour) + 1).toString().padStart(2, "0") + ":00";
      } else if (calendarType == "monthly") {
        let year = selectDateSplit[0];
        let month = selectDateSplit[1];
        let day = selectDateSplit[2];

        editAndAddReserveDate = year + "-" + month + "-" + day;
        editAndAddStartTime = null;
        editAndAddEndTime = null;
      }

      $("#reserveModal").modal("show");
    }
  );

  /** 行事曆區塊的各元件觸發事件設定 END */

  /** 預約 / 編輯 會議MODAL的各元件觸發事件設定 START */

  $("#reserveModal").on("show.bs.modal", function () {
    if (!cancelEvent) {
      $("#theme_error").text("");
      $("#reserveDate_error").text("");
      $("#reserveDateEnd_error").text("");
      $("#beginTime_error").text("");
      $("#endTime_error").text("");
      $("#floor_error").text("");
      $("#roomId_error").text("");
      $("#hostName_error").text("");
      $("#contact_error").text("");
      $("#reserveDeptName_error").text("");
      $("#attendDept_error").text("");
      $("#members_error").text("");

      if (reserveModalType == "addMeeting") {
        dateTimePickerInit(
          editAndAddReserveDate,
          editAndAddStartTime,
          editAndAddEndTime
        );
        reserveTimeQueryEvent(
          calendarType,
          editAndAddReserveDate,
          editAndAddStartTime,
          editAndAddEndTime
        );
      } else {
        $("#deleteBtn").show();
        meetingDetailEvent(meetingId, reserveModalType);
      }
    }

    cancelEvent = false;
  });

  $("#reserveModal").on("hide.bs.modal", function () {
    $("#deleteBtn").hide();
    if (!isDeleteEvent && !reserveConfimEvent) {
      $("#cancelModal").modal("show");
    }
    isDeleteEvent = false;
    reserveConfimEvent = false;
  });

  $("#reserveDetailModal").on("show.bs.modal", function () {
    meetingDetailEvent(meetingId, reserveModalType);
    if (reserveModalType == "ownMeeting") {
      $("#editBtn").show();
      $("#QRCodeBtn").show();
    } else if (reserveModalType == "otherMeeting") {
      $("#editBtn").hide();
      $("#QRCodeBtn").hide();
    }
  });

  $("#host-select-btn").on("click", function () {
    dropDownSelectType = "host";
    $("#host-company").val("");
    $("#host-depart")
      .empty()
      .append(`<option value="" selected>請選擇</option>`);
    $("#host-user").empty().append(`<option value="" selected>請選擇</option>`);
  });

  $("#host-company").on("change", function () {
    companyChangeEvent($(this).find(":selected").val(), dropDownSelectType);
  });

  $("#host-depart").on("change", function () {
    departmentChangeEvent($(this).find(":selected").val(), dropDownSelectType);
  });

  $("#host-user").on("change", function () {
    if ($(this).find(":selected").val() !== "") {
      $("#host-user-confirm").removeClass("btn-secondary");
      $("#host-user-confirm").addClass("btn-primary");
      $("#host-user-confirm").attr("disabled", false);
    } else {
      $("#host-user-confirm").removeClass("btn-primary");
      $("#host-user-confirm").addClass("btn-secondary");
      $("#host-user-confirm").attr("disabled", true);
    }
  });

  $("#host-user-confirm").on("click", function () {
    const hostName = $("#host-user").find(":selected").data("name");
    $("#hostName").val(hostName);
  });

  $("#contact-select-btn").on("click", function () {
    dropDownSelectType = "contact";
    $("#contact-company").val("");
    $("#contact-depart")
      .empty()
      .append(`<option value="" selected>請選擇</option>`);
    $("#contact-user")
      .empty()
      .append(`<option value="" selected>請選擇</option>`);
  });

  $("#contact-company").on("change", function () {
    companyChangeEvent($(this).find(":selected").val(), dropDownSelectType);
  });

  $("#contact-depart").on("change", function () {
    departmentChangeEvent($(this).find(":selected").val(), dropDownSelectType);
  });

  $("#contact-user").on("change", function () {
    if ($(this).find(":selected").val() !== "") {
      $("#contact-user-confirm").removeClass("btn-secondary");
      $("#contact-user-confirm").addClass("btn-primary");
      $("#contact-user-confirm").attr("disabled", false);
    } else {
      $("#contact-user-confirm").removeClass("btn-primary");
      $("#contact-user-confirm").addClass("btn-secondary");
      $("#contact-user-confirm").attr("disabled", true);
    }
  });

  $("#contact-user-confirm").on("click", function () {
    const contactName = $("#contact-user").find(":selected").data("name");
    const contactPhone = $("#contact-user").find(":selected").data("phone");
    $("#contact-name").val(contactName);
    $("#contact-phone").val(contactPhone);

    //聯絡人與預約單位同步公司與單位
    const reserveDeptName = $("#contact-depart").find(":selected").text();
    $("#reserveDeptName").val(reserveDeptName);
    $("#reserveDeptId").val($("#contact-depart").find(":selected").val());
    $("#reserveCompanyId").val($("#contact-company").find(":selected").val());
    $("#reserveDept-company").val($("#reserveCompanyId").val());
  });

  $("#reserveDept-select-btn").on("click", function () {
    dropDownSelectType = "reserveDept";
    $("#reserveDept-company").val($("#reserveCompanyId").val());
    companyChangeEvent($("#reserveCompanyId").val(), dropDownSelectType);
  });

  $("#reserveDept-company").on("change", function () {
    companyChangeEvent($(this).find(":selected").val(), dropDownSelectType);
  });

  $("#reserveDept-depart").on("change", function () {
    if ($(this).find(":selected").val() !== "") {
      $("#reserveDept-depart-confirm").removeClass("btn-secondary");
      $("#reserveDept-depart-confirm").addClass("btn-primary");
      $("#reserveDept-depart-confirm").attr("disabled", false);
    } else {
      $("#reserveDept-depart-confirm").removeClass("btn-primary");
      $("#reserveDept-depart-confirm").addClass("btn-secondary");
      $("#reserveDept-depart-confirm").attr("disabled", true);
    }
  });

  $("#reserveDept-depart-confirm").on("click", function () {
    const reserveDeptName = $("#reserveDept-depart").find(":selected").text();
    $("#reserveDeptName").val(reserveDeptName);
    $("#reserveDeptId").val($("#reserveDept-depart").find(":selected").val());
  });

  // 新增/修改成功的提示視窗隱藏事件
  $("#editCallBack").on("hide.bs.modal", function () {
    queryMeetingEvent(calendarType);
    cleanModal();
  });

  $("#room").change(function () {
    $("#floor").val(
      findParentLabelBySubValue(editAndAddFloorOptions, $("#room").val())
    );
  });

  //預約按鈕
  $("#confirmBtn").click(function () {
    reserveConfimEvent = true;
    if (reserveModalType == "addMeeting") {
      insertMeetingEvent();
    } else {
      updateMeetingEvent(meetingId);
    }
  });

  //清除重選按鈕
  $("#cleanBtn").click(function () {
    cleanModal();
  });

  $("#deleteBtn").click(function () {
    isDeleteEvent = true;
    $("#reserveModal").modal("hide");
    meetingId = $(this).attr("data-meetingid");
    $("#deleteConfirmModal").modal("show");
  });

  $("#deleteConfirmBtn").click(function () {
    $("#deleteConfirmModal").modal("hide");
    deleteMeetingEvent(meetingId);
  });
  /** 預約 / 編輯 會議MODAL的各元件觸發事件設定 END */

  /** 取消新增 / 編輯 會議MODAL的各元件觸發事件設定 START  */
  $("#cancelBtn").click(function () {
    cancelEvent = true;
    $("#reserveModal").modal("show");
  });

  //確認取消按鈕
  $("#cancelConfirmBtn").click(function () {
    cleanModal();
  });
  /** 取消新增 / 編輯 會議MODAL的各元件觸發事件設定 END  */

  /** 會議詳情MODAL的各元件觸發事件設定 START */
  $("#detailModal").on("click", ".detailBtn", function () {
    reserveModalType = $(this).attr("name");
    meetingId = $(this).data("meetingid");
    if (
      reserveModalType == "ownMeeting" ||
      reserveModalType == "otherMeeting"
    ) {
      $("#reserveDetailModal").modal("show");
    }
    $("#detailModal").modal("hide");
  });
  /** 會議詳情MODAL的各元件觸發事件設定 END */

  /** 深淺版面切換 START */
  $("#colorChange").change(function () {
    colorChangeInit();
  });
  /** 深淺版面切換 END */

  changeRoomItem();
  queryMeetingEvent(calendarType);

  // 預約類型CheckBox
  $("input[name='reserveType']").change(function () {
    reserveChange();
  });

  // 會議 QR Code 下載
  $("#qrCodeDownloadBtn").click(function () {
    const linkSource = $("#qrCodeImage").attr("src");
    const downloadLink = document.createElement("a");
    downloadLink.href = linkSource;
    downloadLink.download = "QRCODE";
    downloadLink.click();
    downloadLink.remove();
  });
});

$(document).ready(function () {

  // 會議模式
  $("input[name=meetingMode]").change(function() {
    const val = $(this).val();
    let mode = ''
    if (val == 'MIX') mode = 'VIDEO'
    else mode = 'NORMAL'
    $(`input[name=situationMode][value=${mode}]`).prop("checked", true);
  });

  scrollToTarget("daily", "#scroll-container-daily", 8);
});

let dailyAddRoomId = 0;

let editAndAddFloorOptions = [];

let editMeetingInfo = {
  meetingId: "",
  reserveType: "",
  theme: "",
  reserveDate: "",
  reserveDateEnd: "",
  reserveStartTime: "",
  reserveEndTime: "",
  floor: "",
  roomId: "",
  roomName: "",
  hostId: "",
  hostName: "",
  contactId: "",
  contactName: "",
  contactPhone: "",
  personal: false,
  members: "",
  meetingMode: "",
  meetingModeName: "",
  situationMode: "",
  situationModeName: "",
  departmentId: "",
  departmentName: "",
  url: "",
  reserveDepartments: null,
  reserveDepartmentsName: null,
};

function dateTimePickerInit(reserveDate, beginTime, endTime, reserveDateEnd) {
  if (beginTime == null) {
    beginTime = "08:00";
  }

  if (endTime == null) {
    endTime = "09:00";
  }

  flatpickr("#beginTime", {
    enableTime: true,
    noCalendar: true,
    dateFormat: "H:i",
    inline: false,
    allowInput: true,
    minuteIncrement: 15,
    defaultHour: parseInt(beginTime.split(":")[0]),
    defaultMinute: parseInt(beginTime.split(":")[1]),
    time_24hr: true,
  });

  flatpickr("#endTime", {
    enableTime: true,
    noCalendar: true,
    dateFormat: "H:i",
    inline: false,
    allowInput: true,
    minuteIncrement: 15,
    defaultHour: parseInt(endTime.split(":")[0]),
    defaultMinute: parseInt(endTime.split(":")[1]),
    time_24hr: true,
  });

  flatpickr("#reserveDate", {
    inline: false,
    defaultDate: reserveDate,
  });

  flatpickr("#reserveDateEnd", {
    inline: false,
    defaultDate: reserveDateEnd,
  });

  $("#beginTime").val(beginTime);
  $("#endTime").val(endTime);
  $("#reserveDate").val(reserveDate);
  $("#reserveDateEnd").val(reserveDateEnd);
}

function companyChangeEvent(companyId, dropDownType) {
  const data = {
    companyId: companyId,
    type: dropDownType,
  };

  const ajax = {
    url: "/meeting/reserve/companyChange",
    data: data,
    success: companyChangeSuccess,
  };

  ajaxSubmit(ajax);
}

function companyChangeSuccess(response) {
  let changeElement = "#" + response.result.type + "-depart";

  if (response.result.type !== "reserveDept") {
    $(changeElement)
      .empty()
      .append(`<option value="" selected>請選擇</option>`);

    let userSelectElement = "#" + response.result.type + "-user";
    $(userSelectElement)
      .empty()
      .append(`<option value="" selected>請選擇</option>`);
  } else {
    $(changeElement).empty().append(`<option value="">請選擇</option>`);
  }

  response.result.departmentOptions.forEach((department) => {
    $(changeElement).append(
      `<option value="${department.value}">${department.label}</option>`
    );
  });

  if (response.result.type == "reserveDept") {
    $(changeElement).val($("#reserveDeptId").val());
  }
}

function departmentChangeEvent(departmentId, dropDownType) {
  const data = {
    departmentId: departmentId,
    type: dropDownType,
  };

  const ajax = {
    url: "/meeting/reserve/departmentChange",
    data: data,
    success: departmentChangeSuccess,
  };

  ajaxSubmit(ajax);
}

function departmentChangeSuccess(response) {
  let changeElement = "#" + response.result.type + "-user";

  $(changeElement).empty().append(`<option value="" selected>請選擇</option>`);

  response.result.userOptions.forEach((user) => {
    $(changeElement).append(
      `<option value="${user.value}" data-name="${user.thrValue}" data-phone="${user.secValue}">${user.label}</option>`
    );
  });
}

// 切換會議室顯示選項
function changeRoomItem() {
  $("input[name=floorBtn]").each(function () {
    if ($(this).hasClass("active")) {
      $("#floor-" + $(this).attr("id"))
        .css("display", "")
        .children("input")
        .addClass("active");
    } else {
      $("#floor-" + $(this).attr("id"))
        .css("display", "none")
        .children("input")
        .removeClass("active");
    }
  });
}

/**
 * 預約會議
 */
function insertMeetingEvent() {
  const error = validateInsert(reserveModalType);

  let startTime = $("#beginTime").val();
  let startTimeFormatArr = startTime.split(":");
  if (startTimeFormatArr.length < 3) {
    startTime = startTime + ":00";
  }

  let endTime = $("#endTime").val();
  let endTimeFormatArr = endTime.split(":");
  if (endTimeFormatArr.length < 3) {
    endTime = endTime + ":00";
  }

  if (!error) {
    const type = $("#reserveChange").prop("checked")
      ? "week"
      : $("#dailyReserveChange").prop("checked")
      ? "daily"
      : $("#monthReserveChange").prop("checked")
      ? "month"
      : "";

    const data = {
      theme: $("#theme").val(),
      roomId: $("#room").val().split("-")[0],
      floor: $("#floor").val(),
      hostName: $("#hostName").val(),
      contactName: $("#contact-name").val(),
      contactPhone: $("#contact-phone").val(),
      reserveDeptId: $("#reserveDeptId").val(),
      reserveDeptName: $("#reserveDeptName").val(),
      nameValue: nameValue,
      attendDept: attendDept,
      meetingMode: $("input[name=meetingMode]:checked").val(),
      situationMode: $("input[name=situationMode]:checked").val(),
      url: $("#url").val(),
      members: $("#members").val(),
      personal: $("input[name=personal]:checked").val(),
      beginTime: $("#reserveDate").val() + " " + startTime,
      endTime: $("#reserveDate").val() + " " + endTime,
      type: type,
    };
    // 週、日
    if (type == "week" || type == "daily") {
      data.cycleBeginTime = $("#reserveDateEnd").val() + " " + startTime;
      data.cycleEndTime = $("#reserveDateEnd").val() + " " + endTime;
    }
    // 月
    if (type == "month") {
      // 取得當下時間
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, "0");
      const day = String(today.getDate()).padStart(2, "0");
      const formattedDate = `${year}-${month}-${day}`;

      data.beginTime = formattedDate + " " + startTime;
      data.endTime = formattedDate + " " + endTime;
      data.dayOfWeek = $("#dayOfWeek").val();
      data.weekOfMonth = $("#weekOfMonth").val();
    }
    const ajax = {
      url: "/meeting/reserve/add",
      data: data,
      success: editCallBack,
      error: addError,
    };
    ajaxSubmit(ajax);
  }
}

// 檢核
function validateInsert(reserveModalType) {
  let error = false;
  // 會議主題
  if ($("#theme").val() == "") {
    error = true;
    $("#theme_error").text("此欄位必填");
  } else {
    $("#theme_error").text("");
  }
  // 開始日期
  if ($("#reserveDate").val() == "") {
    error = true;
    $("#reserveDate_error").text("此欄位必填");
  } else {
    $("#reserveDate_error").text("");
  }
  // 週期性結束日期(每週)
  if ($("#reserveChange").prop("checked")) {
    if ($("#reserveDateEnd").val() == "") {
      error = true;
      $("#reserveDateEnd_error").text("此欄位必填");
    } else {
      $("#reserveDateEnd_error").text("");
    }
  } else {
    $("#reserveDateEnd_error").text("");
  }
  // 週期性結束日期(每日)
  if ($("#dailyReserveChange").prop("checked")) {
    if ($("#reserveDateEnd").val() == "") {
      error = true;
      $("#reserveDateEnd_error").text("此欄位必填");
    } else {
      $("#reserveDateEnd_error").text("");
    }
  } else {
    $("#reserveDateEnd_error").text("");
  }

  // 週期性結束日期(每月)
  if ($("#monthReserveChange").prop("checked")) {
    if ($("#weekOfMonth").val() == "") {
      error = true;
      $("#weekOfMonth_error").text("此欄位必填");
    } else {
      $("#weekOfMonth_error").text("");
    }
  } else {
    $("#weekOfMonth_error").text("");
  }

  // 開始時間
  if ($("#beginTime").val() == "") {
    error = true;
    $("#beginTime_error").text("此欄位必填");
  } else {
    $("#beginTime_error").text("");
  }
  // 結束時間
  if ($("#endTime").val() == "") {
    error = true;
    $("#endTime_error").text("此欄位必填");
  } else {
    $("#endTime_error").text("");
  }

  if (!$("#monthReserveChange").prop("checked")) {
    // 開始時間與結束時間
    if (
      $("#reserveDate").val() != "" &&
      $("#beginTime").val() != "" &&
      $("#endTime").val() != ""
    ) {
      let newBeginDate = new Date(
        $("#reserveDate").val() + " " + $("#beginTime").val()
      );
      let newEndDate = new Date(
        $("#reserveDate").val() + " " + $("#endTime").val()
      );

      if (newBeginDate >= newEndDate) {
        error = true;
        $("#timeSet_error").text("開始時間不可晚於結束時間");
      } else {
        $("#timeSet_error").text("");
      }
      if (reserveModalType == "addMeeting") {
        if (
          $("#reserveDate").val() == nowDateTimeStr &&
          newBeginDate < nowDateTime
        ) {
          error = true;
          $("#timeSet_error").text("不可早於現在時間");
        }
      } else {
        if (
          $("#reserveDate").val() == nowDateTimeStr &&
          newEndDate < nowDateTime
        ) {
          error = true;
          $("#timeSet_error").text("結束時間不可早於現在時間");
        }
      }
    }
  }

  //週期性預約結束日期上限3個月
  if (
    $("#reserveChange").prop("checked") ||
    $("#dailyReserveChange").prop("checked")
  ) {
    if ($("#reserveDate").val() != "" && $("#reserveDateEnd").val() != "") {
      if ($("#reserveDate").val() < $("#reserveDateEnd").val()) {
        let startDate = new Date($("#reserveDate").val());
        let endDate = new Date($("#reserveDateEnd").val());
        let maxEndDate = startDate.setMonth(startDate.getMonth() + 3);

        if (endDate > maxEndDate) {
          error = true;
          $("#reserveDateEnd_error").text("結束日期至多3個月");
        } else {
          $("#reserveDateEnd_error").text("");
        }
      }
    }
    //週期性預約會議結束時間上限2小時
    if (
      $("#reserveDate").val() != "" &&
      $("#beginTime").val() != "" &&
      $("#endTime").val() != ""
    ) {
      let newBeginDate = new Date(
        $("#reserveDate").val() + " " + $("#beginTime").val()
      );
      let newEndDate = new Date(
        $("#reserveDate").val() + " " + $("#endTime").val()
      );
      const dateDiff = newEndDate - newBeginDate;
      const hourDiff = dateDiff / (1000 * 60 * 60);
      if (hourDiff > 2) {
        error = true;
        $("#timeSet_error").text("週期性預約上限2小時");
      } else {
        $("#timeSet_error").text("");
      }
    }
  }
  // 會議室名稱
  if ($("#room").val() == "") {
    error = true;
    $("#roomId_error").text("此欄位必填");
  } else {
    $("#roomId_error").text("");
  }
  // 主持人
  if ($("#hostName").val() == "") {
    error = true;
    $("#hostName_error").text("此欄位必填");
  } else {
    $("#hostName_error").text("");
  }
  // 聯絡人
  if ($("#contact-name").val() == "" && $("#contact-phone").val() == "") {
    error = true;
    $("#contact_error").text("下方兩欄為必填");
  }

  if ($("#contact-name").val() == "" && $("#contact-phone").val() != "") {
    error = true;
    $("#contact_error").text("聯絡人為必填");
  }

  if ($("#contact-name").val() != "" && $("#contact-phone").val() == "") {
    error = true;
    $("#contact_error").text("聯絡電話為必填");
  } else {
    $("#contact_error").text("");
  }
  // 預約單位
  if ($("#reserveDeptName").val() == "") {
    error = true;
    $("#reserveDeptName_error").text("此欄位必填");
  } else {
    $("#reserveDeptName_error").text("");
  }
  // 與會單位
  if ($("#dept-name").find("button").length == 0) {
    error = true;
    $("#attendDept_error").text("此欄位至少填一個");
  } else {
    $("#attendDept_error").text("");
  }
  // 預估人數
  if ($("#members").val() == "") {
    error = true;
    $("#members_error").text("此欄位必填");
  } else if (!isNumber($("#members").val())) {
    error = true;
    $("#members_error").text("請填入數字");
  } else if (
    Number($("#room").val().split("-")[1]) < Number($("#members").val())
  ) {
    error = true;
    $("#members_error").text("會議室容納人數不足");
  } else {
    $("#members_error").text("");
  }

  return error;
}

function isNumber(input) {
  const regex = /^\d+$/; // 正則表達式,檢查是否全為數字
  return regex.test(input);
}

function isValidPhoneNumber(phone) {
  const regex =
    /(\d{2,3}-?|\(\d{2,3}\))\d{3,4}-?\d{4}|09\d{2}(\d{6}|-\d{3}-\d{3})/; // 正則表達式,檢查是否為數字()+-
  return regex.test(phone);
}

/**
 * 更新會議
 */
function updateMeetingEvent(meetingId) {
  const error = validateInsert(reserveModalType);

  let startTime = $("#beginTime").val();
  let startTimeFormatArr = startTime.split(":");
  if (startTimeFormatArr.length < 3) {
    startTime = startTime + ":00";
  }

  let endTime = $("#endTime").val();
  let endTimeFormatArr = endTime.split(":");
  if (endTimeFormatArr.length < 3) {
    endTime = endTime + ":00";
  }

  if (!error) {
    const data = {
      meetingId: meetingId,
      theme: $("#theme").val(),
      beginTime: $("#reserveDate").val() + " " + startTime,
      endTime: $("#reserveDate").val() + " " + endTime,
      roomId: $("#room option:selected").val().split("-")[0],
      hostName: $("#hostName").val(),
      contactName: $("#contact-name").val(),
      contactPhone: $("#contact-phone").val(),
      reserveDeptId: $("#reserveDeptId").val(),
      reserveDeptName: $("#reserveDeptName").val(),
      nameValue: nameValue,
      attendDept: attendDept,
      meetingMode: $("input[name=meetingMode]:checked").val(),
      situationMode: $("input[name=situationMode]:checked").val(),
      url: $("#url").val(),
      members: $("#members").val(),
      personal: $("input[name=personal]:checked").val(),
    };
    const ajax = {
      url: "/meeting/reserve/update",
      data: data,
      success: editCallBack,
      error: addError,
    };

    ajaxSubmit(ajax);
  }
}

/**
 * 查詢會議
 */
function queryMeetingEvent(calendarType) {
  const floor = [];
  const room = [];
  $("input[name=floorBtn]").each(function (index) {
    if ($(this).hasClass("active")) {
      floor[index] = $(this).attr("id");
    }
  });
  $("input[name=roomBtn]").each(function (index) {
    if ($(this).hasClass("active")) {
      room[index] = $(this).attr("id");
    }
  });

  const data = {
    floor: floor,
    room: room,
    date: $("#date").val(),
    type: calendarType,
  };
  const ajax = {
    url: "/meeting/reserve/query",
    data: data,
    beforeSend: queryBeforeSend,
    success: querySuccess,
  };

  ajaxSubmit(ajax);
}

/**
 * 執行查詢的API接口錢要執行的動作
 */
function queryBeforeSend() {
  // 刷新行事曆的橫軸
  drawCalendarHeader($("#date").val());
  // 刷新行事曆的內容
  drawCalendarContent($("#date").val());
}

//查詢回傳
function querySuccess(resp) {
  const { queryType, pageContent } = resp.result;
  const allTimeReserveCountMap = resp.result.reserveMap;
  // 計算此次查詢所挑選的會議室數量
  let selectRoomCount = 0;
  $("input[name=roomBtn]").each(function () {
    if ($(this).hasClass("active")) {
      selectRoomCount++;
    }
  });

  // 日曆
  if (queryType == "daily" && pageContent != null) setDaily(pageContent);

  //週曆
  if (queryType == "weekly") {
    if (pageContent != null) {
      pageContent.forEach((item) => {
        const startHour = item.reserveStartTime.substring(0, 2);
        const startMinute = parseInt(item.reserveStartTime.substring(3, 5));
        const endHour = item.reserveEndTime.substring(0, 2);
        const endMinute = parseInt(item.reserveEndTime.substring(3, 5));
        const startTotalMinutes = startHour * 60 + startMinute;
        const endTotalMinutes = endHour * 60 + endMinute;
        const differenceInMinutes = (endTotalMinutes - startTotalMinutes) / 60;
        const heightDifference = differenceInMinutes * 101;
        const divMarginTop = (startMinute / 60) * 110;

        const timeReserveList = allTimeReserveCountMap[item.reserveDate];

        for (let i = parseInt(startHour); i < parseInt(endHour); i++) {
          const timeStr = String(i).padStart(2, "0");
          const weekDayTd = $(
            "#weekly_table tbody tr td[data-info=" +
              `${item.reserveDate}` +
              "-" +
              `${timeStr}]`
          );

          if (i > startHour && i < endHour) {
            continue;
          }
          let roomCount = 0;

          timeReserveList.forEach((time) => {
            if (time.hour == timeStr) {
              roomCount = time.roomCount;
              return;
            }
          });

          let fontSize = 100 - roomCount * 6.8;
          if (roomCount >= 3) {
            if (weekDayTd.children("input").length == 3) {
              if (weekDayTd.children(".moreTag").length == 0) {
                weekDayTd.append(
                  `<div id="moreMeeting" class="moreTag m-2" style="float: right;>
									<h5 data-bs-toggle="tooltip" title="顯示更多會議" style="color: white;cursor: pointer;">更<br>多</h5>
								</div>`
                );
              }
            } else {
              weekDayTd.append(
                `<input type="button" title="${item.theme}\n${item.contactName}\n${item.contactPhone}" class="fs-7" name="${item.own}"
						style="width: ${
              80 / 3
            }%;height:${heightDifference}%;border-radius: 4px; border-width:1px;white-space:pre-wrap; padding-left: 2px;
						vertical-align: top;font-size: ${fontSize}%;margin-top:${divMarginTop}px;
						${item.theme === "不公開" ? "pointer-events: none;" : ""}" data-meetingid=${
                  item.meetingId
                } value="${item.roomName}"/>`
              );
            }
          } else {
            weekDayTd.append(
              `<input type="button" title="${item.theme}\n${item.contactName}\n${item.contactPhone}"
                class="fs-7" name="${item.own}"
						style="width: ${
              80 / roomCount
            }%;height:${heightDifference}%;border-radius: 4px; border-width:1px;white-space:pre-wrap; padding-left: 2px;
						vertical-align: top;font-size: ${fontSize}%;margin-top:${divMarginTop}px;
						${item.theme === "不公開" ? "pointer-events: none;" : ""}" data-meetingid=${
                item.meetingId
              } value="${item.roomName}"/>`
            );
          }
        }
      });
    }

    $.each(allTimeReserveCountMap, function (key, value) {
      value.forEach((time) => {
        if (time.roomCount == selectRoomCount) {
          const weekDayTd = $(
            "#weekly_table tbody tr td[data-info=" +
              `${key}` +
              "-" +
              `${time.hour}]`
          );
          weekDayTd.attr("data-type", "maxReserve");
          weekDayTd.css("background-color", "gray");
          //weekDayTd.css('cursor', 'not-allowed');
          weekDayTd.attr("data-bs-toggle", "tooltip");
          weekDayTd.attr("title", "預約已滿");
        }
      });
    });
  }

  //月曆
  if (queryType == "monthly") {
    if (pageContent != null) {
      const dataMap = {};
      pageContent.forEach((item) => {
        if (!dataMap[item.reserveDate]) {
          dataMap[item.reserveDate] = [];
        }
        dataMap[item.reserveDate].push(item);
      });

      for (let key in dataMap) {
        const data = dataMap[key];
        let contentHtml = "";
        for (let i = 0; i < data.length; i++) {
          if (i == 2 && data.length > 3) {
            contentHtml += `<div id="moreMeeting" class="moreTag m-2">
											<h5 data-bs-toggle="tooltip" title="顯示更多會議" style="color: white;cursor: pointer;">更多</h5>
										</div>`;
            break;
          } else {
            contentHtml += `<input type="button" class="fs-6 edit"
										style="width: 100%; border-radius: 8px; text-align: left; padding-left: 10px;
										${data[i].theme === "不公開" ? "pointer-events: none;" : ""}"
										name="${data[i].own}"
										title="${data[i].theme}\n${data[i].contactName}\n${data[i].contactPhone}"
						          	 	value="${data[i].roomName} ${data[i].theme} ${
              data[i].reserveStartTime
            }"
				                        data-meetingId= ${data[i].meetingId} />`;
          }
        }
        $("#monthly_table tbody tr td[data-info=" + `${key}]`).append(
          contentHtml
        );
      }
    }

    $.each(allTimeReserveCountMap, function (key, value) {
      let oneDayTimeCount = 0;
      value.forEach((time) => {
        if (time.roomCount == selectRoomCount) {
          oneDayTimeCount++;
        }
      });

      if (oneDayTimeCount == 24) {
        const monthDayTd = $(
          "#monthly_table tbody tr td[data-info=" + `${key}]`
        );
        monthDayTd.attr("data-type", "maxReserve");
        monthDayTd.css("background-color", "gray");
        //monthDayTd.css('cursor', 'not-allowed');
        monthDayTd.attr("data-bs-toggle", "tooltip");
        monthDayTd.attr(
          "title",
          selectRoomCount != 0 ? "預約已滿" : "未挑選會議室，不可預約"
        );
      }
    });
  }
}

/**
 * 取得日曆內容
 * @param {*} content 會議訊息
 */
function setDaily(content) {
  // 取得時間
  const getTime = (t) => {
    return {
      hour: t.substring(0, 2),
      minute: t.substring(3),
    };
  };
  // 時間計算
  const calculate = (s, e) => {
    const startTotal = Number(s.hour) * 60 + Number(s.minute);
    const endTotal = Number(e.hour) * 60 + Number(e.minute);
    const differ = (endTotal - startTotal) / 60;
    const height = differ * 101;
    const top = (Number(s.minute) / 60) * 100;
    return { differ, height, top };
  };
  content.forEach((item) => {
    // 會議內容
    const {
      reserveStartTime,
      reserveEndTime,
      roomId,
      theme,
      contactName,
      contactPhone,
      own,
      meetingId,
    } = item;
    // 會議主題
    let title = theme.length > 5 ? theme.substring(0, 5) + "..." : theme;
    // 開始
    const start = getTime(reserveStartTime);
    // 結束
    const end = getTime(reserveEndTime);
    // 位置計算
    const data = calculate(start, end);

    if (start.hour == end.hour && data.differ > 0) end.hour = end.hour++;

    // 會議時間
    const times = Array.from(
      { length: end.hour - start.hour },
      (v, k) => k + Number(start.hour)
    );

    // 依照時間建立會議元素
    times.forEach((time) => {
      const info = `${roomId}-${time.toString().padStart(2, '0')}`;
      let body = "";
      if (time <= start.hour || time >= end.hour) {
        // 內容
        let detail = `<div class="calendar-chart mt-1" style="height: 30px">
                        <p>${title}</p>
                      </div>`;
        // 如果高度超過 100 聯絡人名稱
        if (data.height >= 100) {
          detail += `<div class="calendar-chart" style="height: 30px">
                        <p>${contactName}</p>
                     </div>
                     <div class="calendar-chart" style="height: 30px">
                        <p>${contactPhone}</p>
                     </div>
                     `;
        }
        body = `<div
                  class="d-flex flex-column"
                  name="${own}"
                  style="
                    width: 100%;
                    position: absolute;
                    height: ${data.height}%;
                    top: ${data.top};
                    left: 0;
                    border-style: solid;
                    border-width: 1px 0px;
                    border-color: #FFFFFF;
                    z-index: 1;
                  "
                  data-type="edit"
                  ${theme == "不公開" ? "pointer-events: none;" : ""}
                >
                  ${detail}
                </div>`;
        const td = `<td
                      class="border-end border-bottom border-white"
                      data-info="${info}"
                      data-meetingid="${meetingId}"
                      style="
                        flex: 0.0 auto;
	                      position: relative;
                        ${theme == "不公開" ? "pointer-events: none;" : ""}
                      "
                    >
                      ${body}
                    </td>`;
        $(`#daily_table tbody tr td[data-info=${info}]`).replaceWith(td);
      }
    });
  });
}

/**
 * 查詢更多會議 (月曆/週曆+3以上細節)
 */
function queryDetailEvent(date, startTime, endTime) {
  const floor = [];
  const room = [];
  $("input[name=floorBtn]").each(function (index) {
    if ($(this).hasClass("active")) {
      floor[index] = $(this).attr("id");
    }
  });
  $("input[name=roomBtn]").each(function (index) {
    if ($(this).hasClass("active")) {
      room[index] = $(this).attr("id");
    }
  });
  const data = {
    floor: floor,
    room: room,
    date: date,
    startTime: startTime,
    endTime: endTime,
  };
  const ajax = {
    url: "/meeting/reserve/queryDayReserveDetails",
    data: data,
    success: queryDetailSuccess,
  };
  ajaxSubmit(ajax);
}

/*
 * 月曆/週曆+3以上細節
 */
function queryDetailSuccess(resp) {
  const pageContent = resp.result.pageContent;
  $("#details").empty();

  pageContent.forEach((item) => {
    const buttonHtml = `
                <div class="d-flex justify-content-center mb-2">
                  <input type="button" class="fs-6 btn btn-light detailBtn" name="${item.own}"
                      style="width: 80%; border-radius: 8px; padding: 5px; font-size: 14px;
                      ${item.theme === "不公開" ? "pointer-events: none;" : ""}"
                      data-meetingId='${item.meetingId}'
                      value="${item.roomName}  ${item.theme}  ${item.reserveStartTime} ~ ${item.reserveEndTime}"
                  />
                </div>
            `;
    $("#details").append(buttonHtml);
  });

  $("#detailModal").modal("show");
}

/**
 * 刪除會議
 */
function deleteMeetingEvent(meetingId) {
  const data = {
    meetingId: meetingId,
  };
  const ajax = {
    url: "/meeting/reserve/delete",
    data: data,
    success: editCallBack,
    error: addError,
  };
  ajaxSubmit(ajax);
}

// 修改會議
function editCallBack(resp) {
  $("#reserveModal").modal("hide");
  $("#callBack").replaceWith(
    `<div class="d-flex justify-content-center mb-2" id="callBack">
		  <h4>${resp.statusMessage}</h4>
		</div>`
  );
  $("#editCallBack").modal("show");
}

function addError(error) {
  if (error.result) {
    $("#theme_error").text("");
    $("#reserveDate_error").text("");
    $("#beginTime_error").text("");
    $("#endTime_error").text("");
    $("#floor_error").text("");
    $("#roomId_error").text("");
    $("#hostName_error").text("");
    $("#contact_error").text("");
    $("#reserveDeptName_error").text("");
    $("#attendDept_error").text("");
    $("#members_error").text("");
    $("#timeSet_error").text("");

    error.result.forEach((errorObj) => {
      if (
        errorObj.errorKey == "contactName" ||
        errorObj.errorKey == "contactPhone"
      ) {
        $("#contact_error").text(errorObj.errorMessage);
      } else if (errorObj.errorKey == "alert") {
        callbackAlert(errorObj.errorMessage);
      } else {
        let errorId = "#" + errorObj.errorKey + "_error";
        $(errorId).text(errorObj.errorMessage);
      }
    });
  }
}

/**
 * 呈現訊息
 * @param {string} msg 訊息
 */
function callbackAlert(msg) {
  $("#reserveModal").modal("hide");
  $("#callBack").replaceWith(
    `<div class="d-flex justify-content-center mb-2" id="callBack">
		  <h4>${msg}</h4> 
		</div>`
  );
  $("#editCallBack").modal("show");
}

function reserveTimeQueryEvent(queryType, reserveDate, startTime, endTime) {
  if (startTime != null && endTime != null) {
    startTime = startTime + ":00";
    endTime = endTime + ":00";
  }

  const data = {
    queryType: queryType,
    date: reserveDate,
    startTime: startTime,
    endTime: endTime,
  };

  const ajax = {
    url: "/meeting/reserve/reservetime/query",
    data: data,
    success: reserveTimeQuerySuccess,
  };

  ajaxSubmit(ajax);
}
// 會議預約模板
function reserveTimeQuerySuccess(response) {
  $("#reserve-modal-title").text("會議預約");
  $("#confirmBtn").text("預約");
  $("#type").text("預約類型：");
  $("#oneceReserveChange").show();
  $("#dailyReserveChange").show();
  $("#reserveChange").show();
  $("#monthReserveChange").show();
  $("#onceReserveChangeText").text("一次性預約");
  $("#dailyReserveChangeText").text("每日預約");
  $("#reserveChangeText").text("每週預約");
  $("#monthReserveChangeText").text("每月預約");
  editAndAddFloorOptions = response.result.floorOptions;

  if (response.result.queryType != "daily") {
    $("#room").empty().append(`<option value="" selected>請選擇</option>`);
  } else {
    $("#room").empty().append(`<option value="">請選擇</option>`);
  }

  editAndAddFloorOptions.forEach((floorSelect) => {
    if (response.result.queryType == "daily") {
      floorSelect.subOptions.forEach((roomSelect) => {
        if (dailyAddRoomId == roomSelect.value) {
          roomSelectChangeEvent();
          $("#room").val(`${roomSelect.value}-${roomSelect.secValue}`);
          $("#floor").val(
            findParentLabelBySubValue(editAndAddFloorOptions, $("#room").val())
          );
        }
      });
    } else {
      roomSelectChangeEvent();
    }
  });

  $("#contact-name").val(response.result.contactName);
  $("#contact-phone").val(response.result.contactPhone);
  $("#reserveCompanyId").val(response.result.companyId);
  $("#reserveDeptId").val(response.result.departmentId);
  $("#reserveDeptName").val(response.result.departmentName);
  $('input[name="meetingMode"]:first').attr("checked", true);
  $('input[name="situationMode"]:first').attr("checked", true);
  $('input[name="personal"]:first').attr("checked", true);

  attendDept.push(response.result.departmentId.toString());
  $(
    "select[id=attendDept] option[value='" + response.result.departmentId + "']"
  ).prop("disabled", true);
  nameValue.push(response.result.departmentName);
  appendAttendDeptButton(
    response.result.departmentId,
    response.result.departmentName
  );
}

/**
 * 各會議細節
 */
function meetingDetailEvent(meetingId, detailType) {
  const data = {
    meetingId: meetingId,
    reserveType: detailType,
  };
  const ajax = {
    url: "/meeting/reserve/details",
    data: data,
    success: meetingDetailSuccess,
  };
  ajaxSubmit(ajax);
}

function meetingDetailSuccess(response) {
  if (
    response.result.reserveType == "ownMeeting" ||
    response.result.reserveType == "otherMeeting"
  ) {
    writeModalForRead(response.result);
  }
  $("#editBtn").off("click");
  //修改Edit按鈕
  $("#editBtn").on("click", function () {
    $("#reserveDetailModal").modal("hide");
    writeModalForEdit(response.result);
    $("#reserveModal").modal("show");
  });

  // QRCode按鈕
  $("#QRCodeBtn").on("click", function () {
    const qrCode = response.result.qrCode;
    if (qrCode) {
      $("#qrCodeImage").attr("src", qrCode);
      $("#qrCodeModal").modal("show");
    } else {
      $("#qrCodeImage").hide();
      $("#qrCodeContainer").text("此會議沒有連結");
      $("#qrCodeModal").modal("show");
    }
  });
}

/*
新增[+]Button並寫入text
*/
const attendDept = [];
const nameValue = [];

$("#addBtn").on("click", function () {
  const deptId = $("#attendDept").val();
  const deptName = $("#attendDept").find("option:selected").text();

  //判斷deptId是否存在
  if (deptId && !attendDept.includes(deptId)) {
    attendDept.push(deptId);
    $("select[id=attendDept] option[value='" + deptId + "']").prop(
      "disabled",
      true
    );
    nameValue.push(deptName);
    //寫入test
    appendAttendDeptButton(deptId, deptName);
  }

  if ($("#attendDeptInput").val() != "") {
    const deptName = $("#attendDeptInput").val();
    attendDept.push(nameValue.length + 100);
    nameValue.push(deptName);
    appendAttendDeptButton(nameValue.length + 100, deptName);
    $("#attendDeptInput").val("");
  }
});

function appendAttendDeptButton(deptId, deptName) {
  //新增div
  const nameBlock = document.createElement("div");
  nameBlock.classList.add("name-block");
  nameBlock.classList.add("col-3");
  nameBlock.style.display = "flex";
  nameBlock.style.borderRadius = "5px";
  nameBlock.style.position = "relative";
  nameBlock.style.marginBottom = "5px";
  nameBlock.className = "block mx-1";
  //新增input
  const nameText = document.createElement("input");
  nameText.setAttribute("type", "text");
  nameText.setAttribute("readonly", true);
  nameText.value = deptName;
  nameBlock.appendChild(nameText);
  //新增button
  const deleteBtn = document.createElement("button");
  deleteBtn.textContent = "x";
  deleteBtn.style.border = "none";
  deleteBtn.style.backgroundColor = "transparent";
  deleteBtn.style.position = "absolute";
  deleteBtn.style.right = "0";
  //按下刪除按鈕
  deleteBtn.onclick = function () {
    const index = nameValue.indexOf(deptName);
    if (index !== -1) {
      nameValue.splice(index, 1); //刪除名稱
      attendDept.splice(index, 1); //刪除ID
    }
    nameBlock.remove();
    $("select[id=attendDept] option[value='" + deptId + "']").prop(
      "disabled",
      false
    );
  };
  nameBlock.appendChild(deleteBtn);
  $("#dept-name").append(nameBlock);
}

// 清除模板
function cleanModal() {
  $("#theme").val("");
  $("#editReserveDate");
  $("#oneceReserveChange").prop("checked", true);
  $("#dailyReserveChange").prop("checked", false);
  $("#reserveChange").prop("checked", false);
  $("#monthReserveChange").prop("checked", false);
  $("#reserveDateEnd").prop("disabled", true);
  $("div[name=weekOfMonthSelect]").hide();
  $("div[name=dayOfWeekSelect]").hide();
  $("#weekOfWeek").val("");
  $("#dayOfWeek").val("");
  $("#beginTime").val("");
  $("#endTime").val("");
  $("#floor").val("");
  $("#room").val("");
  $("#room").empty().append(`<option value="" selected>會議室</option>`);
  $("#hostName").val("");
  $("#contact-name").val("");
  $("#contact-phone").val("");
  $("#reserveDeptName").val("");
  $("#urlDiv").addClass("d-none");
  $("input[name=personal]").filter("[value=false]").prop("checked", true);
  $("input[name=meetingMode]").filter("[value=NORMAL]").prop("checked", true);
  $("input[name=situationMode]").filter("[value=NORMAL]").prop("checked", true);
  $("#members").val("");
  nameValue.length = 0;
  attendDept.length = 0;
  $("#attendDept").val("");
  $(".block").remove();
  $("select[id=attendDept] option").prop("disabled", false);
  $("#theme_error").text("");
  $("#reserveDate_error").text("");
  $("#reserveDateEnd_error").text("");
  $("#beginTime_error").text("");
  $("#endTime_error").text("");
  $("#floor_error").text("");
  $("#roomId_error").text("");
  $("#hostName_error").text("");
  $("#contact_error").text("");
  $("#reserveDeptName_error").text("");
  $("#attendDept_error").text("");
  $("#members_error").text("");
  $("#timeSet_error").text("");
  reserveChange();
}

//const editValue = [];
// 修改會議模板
function writeModalForEdit(result) {
  editAndAddFloorOptions = result.floorOptions;
  let resultJson = result;
  delete resultJson["floorOptions"];

  $("#reserve-modal-title").text("修改會議");
  $("#confirmBtn").text("修改");
  $("#reserveChange").hide();
  $("#oneceReserveChange").hide();
  $("#dailyReserveChange").hide();
  $("#monthReserveChange").hide();
  $("div[name=weekOfMonthSelect]").hide();
  $("div[name=dayOfWeekSelect]").hide();
  $("#onceReserveChangeText").text("");
  $("#dailyReserveChangeText").text("");
  $("#reserveChangeText").text("");
  $("#monthReserveChangeText").text("");
  $("#type").text("");
  $("div[name=startDate]").show();
  $("div[name=endDate]").show();
  $("#theme").val(result.theme);

  dateTimePickerInit(
    result.reserveDate,
    result.reserveStartTime,
    result.reserveEndTime
  );

  $("#room").empty().append(`<option value="">請選擇</option>`);

  editAndAddFloorOptions.forEach((floorSelect) => {
    floorSelect.subOptions.forEach((roomSelect) => {
      if (result.roomId == roomSelect.value) {
        const value = roomSelect.value + "-" + roomSelect.secValue;
        const label =
          roomSelect.label + "(容納人數：" + roomSelect.secValue + ")";
        const disabled = roomSelect.disabled ? "disabled" : "";
        $("#room").append(
          `<option value="${value}" ${disabled}>${label}</option>`
        );
        roomSelectChangeEvent();
        $("#floor").val(
          findParentLabelBySubValue(editAndAddFloorOptions, result.roomId)
        );
        $("#room").val(roomSelect.value + "-" + roomSelect.secValue);
      }
    });
  });

  $("#hostName").val(result.hostName);

  $("#contact-name").val(result.contactName);
  $("#contact-phone").val(result.contactPhone);

  $("#reserveCompanyId").val(result.companyId);
  $("#reserveDeptId").val(result.departmentId);
  $("#reserveDeptName").val(result.departmentName);

  let text = [];
  result.reserveDepartments.forEach((value, index) => {
    attendDept.push(value);
    //新增div
    const nameBlock = document.createElement("div");
    nameBlock.classList.add("name-block");
    nameBlock.style.display = "flex";
    nameBlock.style.borderRadius = "5px";
    nameBlock.style.position = "relative";
    nameBlock.className = "block mx-1";
    //新增input
    const nameText = document.createElement("input");
    nameText.setAttribute("type", "text");
    nameText.setAttribute("readonly", true);

    const len = text.length;

    resp.departmentSelects.forEach((item) => {
      if (value == item.value && !text.includes(item.label)) {
        text.push(item.label);
        $("select[id=attendDept] option[value='" + value + "']").prop(
          "disabled",
          true
        );
      }
    });
    if (len == text.length) text.push(result.reserveDepartmentsName[index]);

    nameText.value = text[index];
    nameBlock.appendChild(nameText);
    //新增button
    const editDeleteBtn = document.createElement("button");
    editDeleteBtn.textContent = "x";
    editDeleteBtn.style.border = "none";
    editDeleteBtn.style.backgroundColor = "transparent";
    editDeleteBtn.style.position = "absolute";
    editDeleteBtn.style.right = "0";
    //按下刪除按鈕
    editDeleteBtn.onclick = function () {
      const index = attendDept.indexOf(value);
      if (index !== -1) {
        attendDept.splice(index, 1);
      }
      nameBlock.remove();
      $("select[id=attendDept] option[value='" + value + "']").prop(
        "disabled",
        false
      );
    };
    nameBlock.appendChild(editDeleteBtn);
    $("#dept-name").append(nameBlock);
  });
  nameValue.push(...text);

  $('input[name="meetingMode"]').each(function () {
    if ($(this).val() == result.meetingMode) {
      $(this).prop("checked", true);
    }
  });

  if (result.meetingMode == "MIX") {
    $("#url").val(result.url);
    $("#urlDiv").removeClass("d-none");
  }

  $('input[name="situationMode"]').each(function () {
    if ($(this).val() == result.situationMode) {
      $(this).prop("checked", true);
    }
  });

  $("#members").val(result.members);
  $('input[name="personal"]').each(function () {
    if ($(this).val() == result.personal + "") {
      $(this).prop("checked", true);
    }
  });

  $("#deleteBtn").attr("data-meetingid", result.meetingId);
}

function writeModalForRead(result) {
  $("#detail-theme").text(result.theme);
  $("#detail-time").text(
    result.reserveDate +
      " " +
      result.reserveStartTime +
      " ~ " +
      result.reserveEndTime
  );
  $("#detail-floor").text(result.floor);
  $("#detail-roomname").text(result.roomName);
  $("#detail-hostname").text(result.hostName);
  $("#detail-contactname").text(result.contactName);
  $("#detail-contactphone").text(result.contactPhone);
  $("#detail-deptart").text(result.departmentName);

  let attendDept = "";
  result.reserveDepartmentsName.forEach((value, index) => {
    attendDept += value;
    if (index != result.reserveDepartmentsName.length - 1) {
      attendDept += " , ";
    }
  });
  $("#detail-attenddeptart").text(attendDept);

  $("#detail-meetingmode").text(result.meetingModeName);

  if (reserveModalType == "ownMeeting") {
    $("#detail-meetingmode-link").text(result.url);
  }

  $("#detail-situationmode").text(result.situationModeName);
  $("#detail-members").text(result.members);
}

// 查詢樓層
function findParentLabelBySubValue(options, value) {
  let data = null;
  if (!options || options.length === 0) return data;
  options.forEach(option => {
    if (option.subOptions) {
      const subOption = option.subOptions.find(sub => `${sub.value}-${sub.secValue}` == value);
      if (subOption) data = option.label;
    }
  })
  return data;
}

/**
 * 會議室下拉選單變動事件
 */
function roomSelectChangeEvent() {
  $("#room").empty().append(`<option value="" selected>請選擇</option>`);

  editAndAddFloorOptions.forEach((floorSelect) => {
    floorSelect.subOptions.forEach((roomSelect) => {
      const value = roomSelect.value + "-" + roomSelect.secValue;
      const label =
        roomSelect.label + "(容納人數：" + roomSelect.secValue + ")";
      const disabled = roomSelect.disabled ? "disabled" : "";
      $("#room").append(
        `<option value="${value}" ${disabled}>${label}</option>`
      );
    });
  });
}

function meetingUrl(radio) {
  if (radio.value == "MIX") {
    $("#url,#editUrl").show();
    $("#urlDiv").removeClass("d-none");
  } else {
    $("#url,#editUrl").hide();
    $("#urlDiv").addClass("d-none");
  }
}

/*
換深淺版
*/
function colorChangeInit() {
  if ($("#colorChange").prop("checked")) {
    $("#colorChangeText").text("淺色版");
    $("#colorChangeText").removeClass("text-light");
    $("#colorChangeText").addClass("text-dark");
    $("#calendar-date").removeClass("text-white");
    $("#calendar-date").addClass("text-dark");
    $("#calendar").addClass("light-model");
    document.getElementById("reduceDate").src =
      "../assets/meeting/images/leftArrowLightMode.png";
    document.getElementById("addDate").src =
      "../assets/meeting/images/rightArrowLightMode.png";
  } else {
    $("#colorChangeText").text("深色版");
    $("#colorChangeText").removeClass("text-dark");
    $("#colorChangeText").addClass("text-light");
    $("#calendar-date").removeClass("text-dark");
    $("#calendar-date").addClass("text-white");
    $("#calendar").removeClass("light-model");
    document.getElementById("reduceDate").src =
      "../assets/meeting/images/chevron-left-solid.svg";
    document.getElementById("addDate").src =
      "../assets/meeting/images/chevron-right-solid.svg";
  }
}

/**
 * 換週期性預約
 */
function reserveChange() {
  // 週、日週期性預約
  if (
    $("#reserveChange").prop("checked") ||
    $("#dailyReserveChange").prop("checked")
  ) {
    $("div[name=weekOfMonthSelect]").hide();
    $("div[name=dayOfWeekSelect]").hide();
    $("div[name=startDate]").show();
    $("div[name=endDate]").show();
    $("#reserveDateEnd").prop("disabled", false);
    $("#reserveDateEnd").val("");
  } else if ($("#monthReserveChange").prop("checked")) {
    // 月週期性
    $("div[name=startDate]").hide();
    $("div[name=endDate]").hide();
    $("div[name=weekOfMonthSelect]").show();
    $("div[name=dayOfWeekSelect]").show();
    $("#weekOfMonth").val("");
    $("#dayOfWeek").val("");
  } else {
    // 一次性
    $("div[name=weekOfMonthSelect]").hide();
    $("div[name=dayOfWeekSelect]").hide();
    $("div[name=startDate]").show();
    $("div[name=endDate]").show();
    $("#reserveDateEnd").prop("disabled", true);
    $("#reserveDateEnd").val("");
  }
}

/*
日曆區塊scroll 設定初始位置
*/
function scrollToTarget(type, containerSelector, targetRow) {
  if (type == "weekly") targetRow += 1;
  else if (type == "monthly") return;
  const container = document.querySelector(containerSelector);
  const targetElement = container.querySelector(
    `#${type}_table tbody tr:nth-child(${targetRow})`
  );
  if (container && targetElement) {
    const offsetTop = targetElement.offsetTop - container.offsetTop;
    container.scrollTop = offsetTop;
  }
}
