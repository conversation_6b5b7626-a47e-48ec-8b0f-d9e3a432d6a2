$(function () {
  // 日曆選單
  $("#calendar_select").on("change", function () {
    setCalendarSelectAndTableChange($(this).val(), false);
  });
});

let calendarChangeFunc;

function setCalendarSelectChanged(func) {
  calendarChangeFunc = func;
}

function setCalendarSelectAndTableChange(type, changeSelect) {
  if (changeSelect) $("#calendar_select").val(type);
  $("#calendar_select")
    .find("option")
    .each(function () {
      if ($(this).val() == type) {
        $("#" + $(this).val() + "_table").css("display", "");
      } else {
        $("#" + $(this).val() + "_table").css("display", "none");
      }
    });
  calendarChangeFunc(type);
}

// 動態生成日曆和周曆表頭
function drawCalendarHeader(nowDate) {
  let tableHeaderHtml = `<th class="py-3" style="width: 5.5%; flex: 0.0 "></th>`;

  const nowDateTime = new Date(nowDate);
  const year = nowDateTime.getFullYear();
  const month = nowDateTime.getMonth() + 1;
  const date = nowDateTime.getDate();

  $("#calendar-date").html(
    changeChineseDate($("#calendar_select").val(), year, month, date)
  );
  if ($("#calendar_select").val() == "daily") {
    $("input[name=roomBtn]").each(function () {
      if ($(this).hasClass("active")) {
        const text = $(this).val();
        tableHeaderHtml += `<th class="text-center py-3" style="flex: 0.0 auto">
            ${text.substring(0, text.lastIndexOf("("))}
          </th>`;
      }
    });
    $("#daily_table thead tr").html(tableHeaderHtml);
  }

  if ($("#calendar_select").val() == "weekly") {
    const day = ["一", "二", "三", "四", "五", "六", "日"];
    let dayIndex = 0;
    const dates = getWeekOfDate(new Date(nowDate));
    dates.forEach((date) => {
      const convertDate = String(date.substring(date.length - 2));
      tableHeaderHtml += `<th class="text-center py-3" style="width: 13.5%; flex: 0.0 auto">${day[dayIndex]}<br />${convertDate}</th>`;
      dayIndex++;
    });
    $("#weekly_table thead tr").html(tableHeaderHtml);
  }
}

// 繪製空白表格
function drawCalendarContent(nowDate) {
  let tableBodyHtml = "";
  const calendar = $("#calendar_select").val();

  // 日曆
  if (calendar == "daily") $("#daily_table tbody").html(getDaily());

  if ($("#calendar_select").val() == "weekly") {
    const dates = getWeekOfDate(new Date(nowDate));
    for (let time = 0; time < 24; time++) {
      const convertTime = String(time).padStart(2, "0");
      tableBodyHtml += `<tr style="height: 100px;max-height: 120px">`;
      tableBodyHtml += `<td class=" border-end  p-0 text-end align-top pe-2" style="width: 5.5%; flex: 0.0 auto">${convertTime}:00</td>`;
      for (let i = 0; i < 7; i++) {
        tableBodyHtml += `<td valign="top" height="110px" class="border-end border-bottom " name="noMeeting" data-info="${
          dates[i] + "-" + convertTime
        }" style="flex: 0.0 auto" data-type="weeklyDay"></td>`;
      }
      tableBodyHtml += `</tr>`;
    }
    $("#weekly_table tbody").html(tableBodyHtml);
  }

  if ($("#calendar_select").val() == "monthly") {
    const date = new Date(nowDate);
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDayOfMonth = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate(); // 當月總天數

    let dayCounter = 1;
    const weeks = Math.ceil((daysInMonth + firstDayOfMonth) / 7);
    let calendarHtml = "";

    for (let i = 0; i < weeks; i++) {
      let weekHtml = '<tr style="height: 100px">';
      for (let j = 0; j < 7; j++) {
        if (i === 0 && j < firstDayOfMonth) {
          weekHtml +=
            '<td valign="top" height="143px" class="border-end border-bottom" name="noMeeting" style="flex: 0.0 auto;background: linear-gradient(to bottom right, transparent calc(50% - 1px), white , transparent calc(50% + 1px) )"></td>';
        } else if (dayCounter <= daysInMonth) {
          weekHtml += `<td valign="top" height="143px" class="border-end border-bottom" name="noMeeting" data-info="${year}-${String(
            month + 1
          ).padStart(2, "0")}-${String(dayCounter).padStart(
            2,
            "0"
          )}" style="flex: 0.0 auto;" data-type="monthlyDay">
					        	<div class="fs-6 p-1 m-1" ;">
					            	${dayCounter}
					       		</div>
                         </td>`;
          dayCounter++;
        } else {
          weekHtml +=
            '<td valign="top" height="143px" class="border-end border-bottom" name="noMeeting" style="flex: 0.0 auto;background: linear-gradient(to bottom right, transparent calc(50% - 1px), white , transparent calc(50% + 1px) )"></td>';
        }
      }
      weekHtml += "</tr>";
      calendarHtml += weekHtml;
    }

    $("#monthly_table tbody").html(calendarHtml);
  }
}

// 日曆
function getDaily() {
  // 24 小時
  const times = Array.from({ length: 24 }, (v, k) => k + 1);
  // 畫面內容
  let body = "";
  times.forEach((v) => {
    const time = `${String(v).padStart(2, "0")}`;
    const style =
      $("#daily_table thead tr").children("th").length > 1
        ? "height: 100px;"
        : "height: 100px; width: 5.5%;";
    body += `<tr style="${style}">`;

    // 行事曆表格
    body += `<td class=" border-end p-0 text-end align-top pe-2" style="width: 5.5%; flex: 0.0 auto;">${time}:00</td>`;

    // 已選擇會議室
    $("input[name=roomBtn]").each(function () {
      if ($(this).hasClass("active")) {
        const info = `${$(this).attr("id")}-${time}`;
        body += `<td 
                  class="border-end border-bottom" 
                  name="noMeeting" 
                  data-info="${info}" 
                  style="flex: 0.0 auto" data-type="dailyDay"
                 ></td>
        `;
      }
    });

    body += "</tr>";
  });
  return body;
}

// 周曆

// 月曆

function previousCalendar(dateStr) {
  const calendarSelect = $("#calendar_select").val();
  let dateTime = new Date(dateStr);
  if (calendarSelect == "daily") {
    dateTime.setDate(dateTime.getDate() - 1);
    dateTime = new Date(dateTime);
  }
  if (calendarSelect == "weekly") {
    dateTime.setDate(dateTime.getDate() - 7);
    dateTime = new Date(dateTime);
  }
  if (calendarSelect == "monthly") {
    dateTime.setMonth(dateTime.getMonth() - 1);
    dateTime = new Date(dateTime);
  }

  let dateTimeY = dateTime.getFullYear();
  let dateTimeM = dateTime.getMonth() + 1;
  let dateTimeD = dateTime.getDate();
  dateTimeM = dateTimeM >= 10 ? dateTimeM : "0" + dateTimeM;
  dateTimeD = dateTimeD >= 10 ? dateTimeD : "0" + dateTimeD;
  let nowDateTimeStr = dateTimeY + "-" + dateTimeM + "-" + dateTimeD;

  return nowDateTimeStr;
}

function nextCalendar(dateStr) {
  const calendarSelect = $("#calendar_select").val();
  let dateTime = new Date(dateStr);
  if (calendarSelect == "daily") {
    dateTime.setDate(dateTime.getDate() + 1);
    dateTime = new Date(dateTime);
  }
  if (calendarSelect == "weekly") {
    dateTime.setDate(dateTime.getDate() + 7);
    dateTime = new Date(dateTime);
  }
  if (calendarSelect == "monthly") {
    dateTime.setMonth(dateTime.getMonth() + 1);
    dateTime = new Date(dateTime);
  }

  let dateTimeY = dateTime.getFullYear();
  let dateTimeM = dateTime.getMonth() + 1;
  let dateTimeD = dateTime.getDate();
  dateTimeM = dateTimeM >= 10 ? dateTimeM : "0" + dateTimeM;
  dateTimeD = dateTimeD >= 10 ? dateTimeD : "0" + dateTimeD;
  let nowDateTimeStr = dateTimeY + "-" + dateTimeM + "-" + dateTimeD;

  return nowDateTimeStr;
}

function getWeekOfDate(date) {
  let week = [];
  date.setDate(date.getDate() - date.getDay());

  for (let i = 0; i < 7; i++) {
    date.setDate(date.getDate() + 1);
    const y = date.getFullYear();
    const m = String(date.getMonth() + 1).padStart(2, "0");
    const d = String(date.getDate()).padStart(2, "0");
    week[i] = `${y}-${m}-${d}`;
  }

  return week;
}

function changeChineseDate(calendarType, year, month, date) {
  const y = year - 1911;
  const m = String(month).padStart(2, "0");
  const d = String(date).padStart(2, "0");

  let newDate = new Date(year, month - 1, date);
  const dates = getWeekOfDate(newDate);
  const weekOfStartDate = new Date(dates[0]);
  const weekOfEndDate = new Date(dates[dates.length - 1]);
  const convertWeekOfStartMonth = weekOfStartDate.getMonth() + 1;
  const convertWeekOfEndMonth = weekOfEndDate.getMonth() + 1;
  const convertWeekOfStartDate = weekOfStartDate.getDate();
  const convertWeekOfEndDate = weekOfEndDate.getDate();

  const chineseStartDate =
    String(convertWeekOfStartMonth).padStart(2, "0") +
    "/" +
    String(convertWeekOfStartDate).padStart(2, "0");

  const chineseEndDate =
    String(convertWeekOfEndMonth).padStart(2, "0") +
    "/" +
    String(convertWeekOfEndDate).padStart(2, "0");

  switch (calendarType) {
    case "daily":
      return `${y}年 ${m}月 ${d}日`;
    case "weekly":
      return `${y}年 ${chineseStartDate} ~ ${chineseEndDate}`;
    case "monthly":
      return `${y}年 ${m}月`;
  }
}
