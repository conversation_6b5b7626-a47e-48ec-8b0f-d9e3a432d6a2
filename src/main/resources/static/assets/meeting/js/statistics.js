/**
 * line
 */

function chart() {
  const ajax = {
    url: "/meeting/statistics/echart",
    success: echartSuccess,
    method: "GET",
  };

  ajaxSubmit(ajax);
}
function echartSuccess(chartDatas) {
  const dom = document.getElementById("line");
  const myChart = echarts.init(dom, "dark");

  option = {
    backgroundColor: "",
    title: {
      text: chartDatas.month + "月份預約次數統計",
      left: "center",
      top: "5%",
    },
    xAxis: {
      type: "category",
      data: ["星期一", "星期二", "星期三", "星期四", "星期五"],
      axisLabel: {
        color: "#add8e6",
      },

      name: "星期",
      nameGap: 15,
      nameLocation: "end",
      nameTextStyle: {},
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#add8e6",
      },

      name: "次數",
      nameGap: 20,
      nameLocation: "end",
      nameTextStyle: {},
    },
    series: [
      {
        data: [
          chartDatas.lineChartDatas.mondayCount,
          chartDatas.lineChartDatas.tuesdayCount,
          chartDatas.lineChartDatas.wednesdayCount,
          chartDatas.lineChartDatas.thursdayCount,
          chartDatas.lineChartDatas.fridayCount,
        ],
        type: "line",
        color: "#48d1cc",
      },
    ],
  };
  myChart.setOption(option, true);

  const myPieChart = echarts.init(document.getElementById("pie"), "dark");
  optionPie = {
    backgroundColor: "",
    title: {
      text: chartDatas.month + "月份預約紀錄",
      left: "center",
      top: "5%",
    },
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      left: "left",
    },
    series: [
      {
        type: "pie",
        radius: "60%",
        data: [
          {
            value: chartDatas.pieChartCancel,
            name: "取消",
            itemStyle: { color: "#add8e6" },
            label: { color: "#48d1cc" },
          },
          {
            value: chartDatas.pieChartReserve,
            name: "預約",
            itemStyle: { color: "#4682b4" },
            label: { color: "#48d1cc" },
          },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };
  myPieChart.setOption(optionPie, true);

  const myBarChart = echarts.init(document.getElementById("bar"), "dark");
  const roomNameList = [];
  const roomCountList = [];
  chartDatas.barChartDatas.forEach((item) => {
    roomNameList.push(item.roomName), roomCountList.push(item.roomNameCount);
  });

  optionBar = {
    backgroundColor: "",
    title: {
      text: chartDatas.month + "月份會議室租借次數統計",
      left: "center",
      top: "5%",
    },

    xAxis: {
      type: "category",
      data: roomNameList,
      axisLabel: {
        color: "#48d1cc",
      },
      name: "會議室",
      nameGap: 15,
      nameLocation: "end",
      nameTextStyle: {
        color: "#FFFFFF",
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#48d1cc",
      },

      name: "次數",
      nameGap: 15,
      nameLocation: "end",
      nameTextStyle: {
        color: "#FFFFFF",
      },
    },
    series: [
      {
        data: roomCountList,
        type: "bar",
        color: "#add8e6",
      },
    ],
  };
  myBarChart.setOption(optionBar, true);
}

$(function () {
  $("#left_arrow").click(function () {
    prevPage();
  });

  $("#right_arrow").click(function () {
    nextPage();
  });

  $("#colorChange").change(function () {
    colorChangeInit();
  });

  $("#resultSearch").keydown(function (e) {
    if (e.key == "Enter") {
      search();
    }
  });

  $("#editPageSize").change(function () {
    changePageSize($("#editPageSize").val());
  });

  $("#downloadBtn").click(function () {
    const ajax = {
      url: "/meeting/statistics/exportXlsx",
      dataType: "binary",
      success: meetRecordFileSuccess,
    };
    ajaxSubmit(ajax);
  });
  //	chart();
  chartInit();
  search();
});

let paginationBtnClass = "text-light";
let paginationleftBtn = "../assets/meeting/images/leftArrow.png";
let paginationRightBtn = "../assets/meeting/images/rightArrow.png";
let fileDownloadBtn = "../assets/meeting/images/download-white.png";
let pageSizeClass = "text-light";

function chartInit() {
  const dom = document.getElementById("line");
  const myChart = echarts.init(dom, "dark");

  option = {
    backgroundColor: "",
    title: {
      text: resp.month + "月份預約次數統計",
      left: "center",
      top: "5%",
    },
    xAxis: {
      type: "category",
      data: ["星期一", "星期二", "星期三", "星期四", "星期五"],
      axisLabel: {
        color: "#add8e6",
      },

      name: "星期",
      nameGap: 15,
      nameLocation: "end",
      nameTextStyle: {},
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#add8e6",
      },

      name: "次數",
      nameGap: 20,
      nameLocation: "end",
      nameTextStyle: {},
    },
    series: [
      {
        data: [
          resp.lineChartDatas.mondayCount,
          resp.lineChartDatas.tuesdayCount,
          resp.lineChartDatas.wednesdayCount,
          resp.lineChartDatas.thursdayCount,
          resp.lineChartDatas.fridayCount,
        ],
        type: "line",
        color: "#48d1cc",
      },
    ],
  };
  myChart.setOption(option, true);

  const myPieChart = echarts.init(document.getElementById("pie"), "dark");
  optionPie = {
    backgroundColor: "",
    title: {
      text: resp.month + "月份預約紀錄",
      left: "center",
      top: "5%",
    },
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      left: "left",
    },
    series: [
      {
        type: "pie",
        radius: "60%",
        data: [
          {
            value: resp.pieChartCancel,
            name: "取消",
            itemStyle: { color: "#add8e6" },
            label: { color: "#48d1cc" },
          },
          {
            value: resp.pieChartReserve,
            name: "預約",
            itemStyle: { color: "#4682b4" },
            label: { color: "#48d1cc" },
          },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };
  myPieChart.setOption(optionPie, true);

  const myBarChart = echarts.init(document.getElementById("bar"), "dark");
  const roomNameList = [];
  const roomCountList = [];
  resp.barChartDatas.forEach((item) => {
    roomNameList.push(item.roomName), roomCountList.push(item.roomNameCount);
  });

  optionBar = {
    backgroundColor: "",
    title: {
      text: resp.month + "月份會議室租借次數統計",
      left: "center",
      top: "5%",
    },

    xAxis: {
      type: "category",
      data: roomNameList,
      axisLabel: {
        color: "#48d1cc",
      },
      name: "會議室",
      nameGap: 15,
      nameLocation: "end",
      nameTextStyle: {
        color: "#FFFFFF",
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#48d1cc",
      },

      name: "次數",
      nameGap: 15,
      nameLocation: "end",
      nameTextStyle: {
        color: "#FFFFFF",
      },
    },
    series: [
      {
        data: roomCountList,
        type: "bar",
        color: "#add8e6",
      },
    ],
  };
  myBarChart.setOption(optionBar, true);
}

//篩選
$(function () {
  // 查詢欄位清除
  $("#cleanBtn").click(function () {
    $("select[name=query-form]").val("");
    $("input[name=query-form]").val("");
    $("#room").empty().append(`<option value="" selected>會議室</option>`);

    select.meetingRoomSelects.forEach((roomSelect) => {
      $("#room").append(
        `<option value="${roomSelect.value}">${roomSelect.label}</option>`
      );
    });

    search();
  });

  // 下拉選單查詢
  $("select[name=query-form]").change(function () {
    search();
  });

  // 日期元件查詢
  $("input[name=query-form]").change(function () {
    search();
  });

  // 排序查詢
  $("#sort").change(() => search());

  init();
});

/*
換深淺版
*/
function colorChangeInit() {
  if ($("#colorChange").prop("checked")) {
    paginationBtnClass = "text-dark";
    paginationleftBtn = "../assets/meeting/images/leftArrowLightMode.png";
    paginationRightBtn = "../assets/meeting/images/rightArrowLightMode.png";
    fileDownloadBtn = "../assets/meeting/images/download-black.png";
    pageSizeClass = "text-dark";
    $("#colorChangeText").text("淺色版");
    $("#colorChangeText").removeClass("text-light");
    $("#colorChangeText").addClass("text-dark");
    $("#datatable").addClass("light-model");
    $("button[name=page]").removeClass("text-light");
    $("#pageSize").removeClass("text-light");
  } else {
    paginationBtnClass = "text-light";
    paginationleftBtn = "../assets/meeting/images/leftArrow.png";
    paginationRightBtn = "../assets/meeting/images/rightArrow.png";
    fileDownloadBtn = "../assets/meeting/images/download-white.png";
    pageSizeClass = "text-light";
    $("#colorChangeText").text("深色版");
    $("#colorChangeText").removeClass("text-dark");
    $("#colorChangeText").addClass("text-light");
    $("#datatable").removeClass("light-model");
    $("button[name=page]").removeClass("text-dark");
    $("#pageSize").removeClass("text-dark");
  }

  $("button[name=page]").addClass(paginationBtnClass);
  $("#paging #left_arrow img").attr("src", paginationleftBtn);
  $("#paging #right_arrow img").attr("src", paginationRightBtn);
  $("#downloadBtn").attr("src", fileDownloadBtn);
  $("#pageSize").addClass(pageSizeClass);
}

/**
 * 查詢事件
 */
function search() {
  const data = {
    sort: $("#sort").val(),
    room: $("#room").val(),
    startDate: $("#startDate").val(),
    endDate: $("#endDate").val(),
    keyword: $("#resultSearch").val(),
  };

  const ajax = {
    url: "/meeting/statistics/query",
    data: data,
    success: searchSuccess,
  };

  query(ajax);
}

function searchSuccess(resp) {
  const pageContent = resp.result;

  if (pageContent.totalSize == 0) {
    $("#noDataTxt").css("display", "block");
    $("#paging").addClass("d-none");
  } else {
    $("#noDataTxt").css("display", "none");
    $("#paging").removeClass("d-none");
  }
  // 組合資料版面
  let tbodyHtml = "";
  pageContent.contents.forEach((item) => {
    let color = "";
    switch (item.status) {
      case "已預約":
        color = "#4AA785";
        break;
      case "進行中":
        color = "#8A8CD9";
        break;
      case "已結束":
        color = "#59A8D4";
        break;
      case "已取消":
        color = "gray";
        break;
    }

    tbodyHtml += `
		<tr class='row' style="${
      item.theme === "不公開" ? "pointer-events: none;" : ""
    }">	
			<td style="width: 17%;">${item.theme}</td>
			<td style="width: 12%;">${item.reserveDate}</td>
			<td style="width: 9%;">${item.reserveTime}</th>
			<td style="width: 14%;">${item.roomNameDisplay}</td>
			<td style="width: 9%;">${item.department}</td>
			<td style="width: 8%;">${item.contactUser}</th>
			<td style="width: 8%;">${item.hostUser}</td>
			<td style="width: 8%;">${item.personal}</td>
			<td style="width: 7%; color:${color}">${item.status}</td>
			<td style="width: 8%;">${item.members}</td>
		</tr>
		`;
  });
  $("#datatable tbody").html(tbodyHtml);

  $("div [name=page]").remove();
  // 組合分頁器版面
  let pageHtml = "";
  // 總頁數
  const totalNum = pageContent.totalPage;
  // 當前頁數
  const currentNum = pageContent.currentPage;
  // 最小值
  let minNum = 0;
  if (currentNum > 2) minNum = currentNum - 2;

  for (let i = minNum; i < minNum + 5; i++) {
    if (i > totalNum - 1) {
      break;
    }
    pageHtml += getPageNum(currentNum == i ? "page_btn_active" : "", i);
  }
  if (totalNum - currentNum > 3) {
    pageHtml += `<div name="page" class="d-flex align-items-center">
		  ...
	  </div>`;
    pageHtml += getPageNum("", totalNum - 1);
  }
  $("#left_arrow").parent().after(pageHtml);

  // 是否顯示上一頁
  if (pageContent.isFirstPage) {
    $("#left_arrow").addClass("d-none");
  } else {
    $("#left_arrow").removeClass("d-none");
  }

  // 是否顯示下一頁
  if (pageContent.isLastPage) {
    $("#right_arrow").addClass("d-none");
  } else {
    $("#right_arrow").removeClass("d-none");
  }

  $("button[name=page]").addClass(paginationBtnClass);
  $("#paging #left_arrow img").attr("src", paginationleftBtn);
  $("#paging #right_arrow img").attr("src", paginationRightBtn);
  $("#downloadBtn").attr("src", fileDownloadBtn);
  $("#pageSize").addClass(pageSizeClass);
}

// 取得分頁樣式
function getPageNum(active, num) {
  return `<div name="page" class="d-flex align-items-center">
		  <button 
			  class="fs-5 ${paginationBtnClass} ${active} px-3 mx-1 page_btn" 
			  onclick="specifyPage(${num})">
			  ${num + 1}
		  </button>
	  </div>`;
}

function meetRecordFileSuccess(resp) {
  const downloadUrl = (window.URL || window.webkitURL).createObjectURL(resp);
  const download_link = document.createElement("a");
  download_link.href = downloadUrl;
  download_link.download = "會議室使用紀錄表.xlsx";
  document.body.appendChild(download_link);
  download_link.click();

  setTimeout(function () {
    URL.revokeObjectURL(downloadUrl);
    download_link.remove();
  }, 1000);
}

// 畫面初始化
function init() {
  colorChangeInit();
  search();
}
