'use strict';

let totalPage = 0;

let totalSize = 0;

let pageSize = 10;

let currentPage = 0;

let pageRequest = {
	'currentPage': currentPage,
	'pageSize': pageSize
};

let pageResponse = {
	currentPage: currentPage,
	totalPage: totalPage,
	totalSize: totalSize,
	pageSize: pageSize
};

let queryUrl = '';

let totalContents = [];

let data;

let queryCondition;

/**
 * 分頁查詢
 * @param {} ajaxData 
 */
const query = (ajaxData) => {
	data = ajaxData;
	pageRequest.currentPage = currentPage;
	pageRequest.pageSize = pageSize;
	ajaxData.data = Object.assign(pageRequest, ajaxData.data);
	ajaxData.success = basicSuccess(ajaxData.success);
	ajaxSubmit(ajaxData);
}

/**
 * 清除重置
 */
const reset = () => {
	totalPage = 0;
	totalSize = 0;
	pageSize = 10;
	currentPage = 0;
}

/**
 * 設定分頁器參數
 * @param {} pageOption 
 */
const setPaginationOption = (pageOption) => {
	totalPage = pageOption.totalPage;
	totalSize = pageOption.totalSize;
	pageSize = pageOption.pageSize;
	currentPage = pageOption.currentPage;
	console.log('totalPage : ' + totalPage);
	console.log('totalSize : ' + totalSize);
	console.log('pageSize : ' + pageSize);
	console.log('currentPage : ' + currentPage);

	pageResponse.currentPage = pageOption.currentPage;
	pageResponse.totalPage = pageOption.totalPage;
	pageResponse.totalSize = pageOption.totalSize;
	pageResponse.pageSize = pageOption.pageSize;
}

/**
 * 切換分頁頁數事件
 * @param {} size 
 */
const changePageSize = (size) => {
	console.log('changePageSize');
	pageSize = size;
	currentPage = 0;
	pageRequest.pageSize = pageSize;
	pageRequest.currentPage = currentPage;
	return query(data);
}

/**
 * 指定頁按鈕事件
 * @param {} page 
 */
const specifyPage = (page) => {
	currentPage = page;
	return query(data);
}

/**
 * 上一頁按鈕事件
 */
const prevPage = () => {
	console.log('prevPage');
	currentPage = currentPage > 0 ? currentPage - 1 : currentPage;
	pageRequest.currentPage = currentPage;
	return query(data);
}

/**
 * 下一頁按鈕事件
 */
const nextPage = () => {
	console.log('nextPage');
	currentPage = currentPage < totalPage - 1 ? currentPage + 1 : currentPage;
	pageRequest.currentPage = currentPage;
	return query(data);
}

/**
 * 第一頁按鈕事件
 */
const firstPage = () => {
	console.log('firstPage');
	currentPage = 0;
	pageRequest.currentPage = currentPage;
	console.log(pageRequest);
	return query(queryCondition, queryUrl);
}

/**
 * 最後一頁按鈕事件
 */
const lastPage = () => {
	console.log('lastPage');
	currentPage = totalPage - 1;
	pageRequest.currentPage = currentPage;
	console.log(pageRequest);
	return query(queryCondition, queryUrl);
}

function basicSuccess(func) {
	return function(resp) {
		const pageContent = resp.result;
		totalSize = pageContent.totalSize;
		totalPage = pageContent.totalPage;
		func(resp);
	}
}