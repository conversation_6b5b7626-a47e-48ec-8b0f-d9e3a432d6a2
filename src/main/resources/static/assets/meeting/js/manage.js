"use strict";

let selectPortId = "";
let selectOfficeId = "";
let selectFloor = "";
let selectRoomId = "";

let portOptions = [];
let officeOptions = [];
let floorOptions = [];
let roomOptions = [];

let portOptHTML = "";
let officeOptHTML = "";
let floorOptHTML = "";
let roomOptHTML = "";

let modalType = "add";

let modalSelectPortId = "";
let modalSelectOfficeId = "";
let modalFloor = "";
let modalRoomName = "";
let modalCapacity = "";
let modalOnlineMeeting = false;
let modalEnvDevice = false;
let modalEnable = false;

let modalOptions = [];
let modalPortOptions = [];
let modalOfficeOptions = [];

let modalPortOptHTML = "";
let modalOfficeOptHTML = "";

let queryContentResult = [];
let editRoomInfo = null;

let paginationBtnClass = "text-light";
let paginationleftBtn = "../assets/meeting/images/leftArrow.png";
let paginationRightBtn = "../assets/meeting/images/rightArrow.png";

$(function () {
  init();
});

/* 各按鈕和下拉選單的觸發事件 START */

//開啟新增會議室Btn
document.getElementById("addRoom").addEventListener("click", function () {
  $("#port-alart").text("");
  $("#office-alart").text("");
  $("#floor-alart").text("");
  $("#name-alart").text("");
  $("#capacity-alart").text("");
  $("#roomModal").modal("show");
});

// 清除按鈕點擊事件
document.getElementById("cleanBtn").addEventListener("click", function () {
  init();
});

// 港別選單切換事件
document.getElementById("port").addEventListener("change", function (e) {
  selectPortId = e.target.value;
  selectOfficeId = "";
  selectFloor = "";
  selectRoomId = "";
  officeOptions = [];
  floorOptions = [];
  roomOptions = [];
  officeOptHTML = "";
  floorOptHTML = "";
  roomOptHTML = "";

  document.getElementById("office").value = selectOfficeId;
  document.getElementById("floor").value = selectFloor;
  document.getElementById("room").value = selectRoomId;

  options.forEach((port) => {
    if (
      selectPortId != "" &&
      selectPortId != null &&
      port.value != selectPortId
    ) {
      return;
    }

    setOfficeOptionData(port.subOptions);
    port.subOptions.forEach((office) => {
      setFloorOptionData(office.subOptions, office.label);
      office.subOptions.forEach((floor) => {
        setRoomOptionData(
          floor.subOptions,
          office.label,
          floor.value,
          floor.label
        );
      });
    });
  });

  setOfficeOptions();
  setFloorOptions();
  setRoomOptions();
  search();
});

// 新增會議室Modal的港別下拉選單切換事件
document.getElementById("modal-port").addEventListener("change", function (e) {
  let portId = e.target.value;

  modalPortOptions.forEach((opt) => {
    if (opt.value == portId) {
      setRoomModalOfficeOptions(opt.subOptions);
      return;
    }
  });

  if (portId == "") {
    modalOfficeOptions = [];
    setRoomModalOfficeOptions(modalOfficeOptions);
  }
});

// 辦公室別選單切換事件
document.getElementById("office").addEventListener("change", function (e) {
  selectOfficeId = e.target.value;
  selectFloor = "";
  selectRoomId = "";
  floorOptions = [];
  roomOptions = [];
  floorOptHTML = "";
  roomOptHTML = "";

  document.getElementById("floor").value = selectFloor;
  document.getElementById("room").value = selectRoomId;

  options.forEach((port) => {
    port.subOptions.forEach((office) => {
      if (
        selectOfficeId != "" &&
        selectOfficeId != null &&
        office.value != selectOfficeId
      ) {
        return;
      }

      setFloorOptionData(office.subOptions, office.label);
      office.subOptions.forEach((floor) => {
        setRoomOptionData(
          floor.subOptions,
          office.label,
          floor.value,
          floor.label
        );
      });
    });
  });

  setFloorOptions();
  setRoomOptions();
  search();
});

// 樓層選單切換事件
document.getElementById("floor").addEventListener("change", function (e) {
  selectFloor = e.target.value;
  selectRoomId = "";
  roomOptions = [];
  roomOptHTML = "";

  options.forEach((port) => {
    port.subOptions.forEach((office) => {
      office.subOptions.forEach((floor) => {
        if (
          selectFloor != "" &&
          selectFloor != null &&
          floor.value != selectFloor
        ) {
          return;
        }

        setRoomOptionData(
          floor.subOptions,
          office.label,
          floor.value,
          floor.label
        );
      });
    });
  });

  setRoomOptions();
  search();
});

// 會議室名稱選單切換事件
document.getElementById("room").addEventListener("change", function (e) {
  selectRoomId = e.target.value;
  search();
});

// 排訓查詢
document.getElementById("sort").addEventListener("change", function() {
  search();
})

// 前一頁按鈕點擊事件
document.getElementById("left_arrow").addEventListener("click", function () {
  prevPage();
});

// 下一頁按鈕點擊事件
document.getElementById("right_arrow").addEventListener("click", function () {
  nextPage();
});

/**
 * 顏色設定初始化
 */
document.getElementById("colorChange").addEventListener("change", function () {
  if (document.getElementById("colorChange").checked) {
    paginationBtnClass = "text-dark";
    paginationleftBtn = "../assets/meeting/images/leftArrowLightMode.png";
    paginationRightBtn = "../assets/meeting/images/rightArrowLightMode.png";
    document.getElementById("colorChangeText").innerHTML = "淺色版";
    document.getElementById("colorChangeText").classList.add("text-dark");
    document.getElementById("colorChangeText").classList.remove("text-light");
    document.getElementById("datatable").classList.add("light-model");
  } else {
    paginationBtnClass = "text-light";
    paginationleftBtn = "../assets/meeting/images/leftArrow.png";
    paginationRightBtn = "../assets/meeting/images/rightArrow.png";
    document.getElementById("colorChangeText").innerHTML = "深色版";
    document.getElementById("colorChangeText").classList.remove("text-dark");
    document.getElementById("colorChangeText").classList.add("text-light");
    document.getElementById("datatable").classList.remove("light-model");
  }

  document.getElementById("left_arrow").children[0].src = paginationleftBtn;
  document.getElementById("right_arrow").children[0].src = paginationRightBtn;

  let pageBtnCollection = document.getElementsByClassName("page_btn");
  Array.from(pageBtnCollection).forEach((pageBtn) => {
    pageBtn.classList.remove("text-light");
    pageBtn.classList.remove("text-dark");
    pageBtn.classList.add(paginationBtnClass);
  });
});

// 新增會議室按鈕的點擊事件
document.getElementById("addRoom").addEventListener("click", function () {
  modalType = "add";
  $("#roomModal").modal("show");
});

document
  .getElementById("roomModal")
  .addEventListener("show.bs.modal", function () {
    console.log("room modal show");
    console.log(modalType);
    roomModalInit();
  });

document
  .getElementById("roomModal")
  .addEventListener("hide.bs.modal", function () {
    modalType = "add";
  });

document
  .getElementById("roomSuccessModal")
  .addEventListener("hide.bs.modal", function () {
    init();
  });

// 新增會議室Modal點擊重選按鈕的點擊事件
document.getElementById("modalCleanBtn").addEventListener("click", function () {
  roomModalInit();
});

/* 各按鈕和下拉選單的觸發事件 END */

$("#resultSearch").keydown(function (e) {
  console.log(e.key);
  if (e.key == "Enter") {
    search();
  }
});

const init = () => {
  setInitOptionData();
  setInitOption();
  search();
};

const roomModalInit = () => {
  setRoomModalInitData();
  setRoomModalPortOptions(modalPortOptions);
  setRoomModalOfficeOptions(modalOfficeOptions);

  console.log(document.getElementById("confirmBtn"));
  document
    .getElementById("confirmBtn")
    .removeEventListener("click", addRoomInfo);
  document
    .getElementById("confirmBtn")
    .removeEventListener("click", updateRoomInfo);

  if (modalType == "add") {
    document.getElementById("modal-port").disabled = false;
    document.getElementById("modal-office").disabled = false;
    document.getElementById("modal-floor").disabled = false;
    document.getElementById("modal-room").disabled = false;
    document.getElementById("modal-onlineMeeting").disabled = false;
    document.getElementById("modal-envDevice").disabled = false;
    document.getElementById("modal-enable").disabled = false;

    document.getElementById("btnDelete").style.display = "none";
    document.getElementById("modalCleanBtn").style.display = "";
    document.getElementById("confirmBtn").innerHTML = "新增";

    // 會議室Modal點擊新增按鈕的點擊事件
    document
      .getElementById("confirmBtn")
      .addEventListener("click", addRoomInfo);
  } else {
    document.getElementById("modal-port").disabled = true;
    document.getElementById("modal-office").disabled = true;
    document.getElementById("modal-floor").disabled = true;
    document.getElementById("modal-room").disabled = false;
    document.getElementById("modal-capacity").disabled = false;
    document.getElementById("modal-onlineMeeting").disabled = false;
    document.getElementById("modal-envDevice").disabled = false;
    document.getElementById("modal-enable").disabled = false;

    document.getElementById("btnDelete").style.display = "";
    document.getElementById("modalCleanBtn").style.display = "none";
    document.getElementById("confirmBtn").innerHTML = "修改";

    // 會議室Modal點擊修改按鈕的點擊事件
    document
      .getElementById("btnDelete")
      .addEventListener("click", function () {
        $("#roomModal").modal("hide");
        $("#deleteModal").modal("show");
      });
 
    document
      .getElementById("deleteConfirmBtn")
      .addEventListener("click", deleteRoomInfo);
    document
      .getElementById("confirmBtn")
      .addEventListener("click", updateRoomInfo);
  }
};

// 設定初始化選單資料
const setInitOptionData = () => {
  portOptions = [];
  officeOptions = [];
  floorOptions = [];
  roomOptions = [];

  setPortData(options);
  options.forEach((port) => {
    setOfficeOptionData(port.subOptions);
    port.subOptions.forEach((office) => {
      setFloorOptionData(office.subOptions, office.label);
      office.subOptions.forEach((floor) => {
        setRoomOptionData(
          floor.subOptions,
          office.label,
          floor.value,
          floor.label
        );
      });
    });
  });
};

// 設定會議室modal的初始化資料
const setRoomModalInitData = () => {
  modalPortOptions = [];
  modalOfficeOptions = [];
  let modalTitle = "";

  options.forEach((port) => {
    let officeOptions = [];
    port.subOptions.forEach((office) => {
      officeOptions.push({
        value: office.value,
        label: office.label,
      });
    });
    modalPortOptions.push({
      value: port.value,
      label: port.label,
      subOptions: officeOptions,
    });
  });

  if (modalType == "add") {
    modalTitle = "新增會議室";
    modalSelectPortId = "";
    modalSelectOfficeId = "";
    modalFloor = "";
    modalRoomName = "";
    modalCapacity = "";
    modalOnlineMeeting = false;
    modalEnvDevice = false;
    modalEnable = false;
  } else {
    modalTitle = "修改會議室";
    modalPortOptions.forEach((port) => {
      if (port.label == editRoomInfo.portName) {
        modalSelectPortId = port.value;
        modalOfficeOptions = port.subOptions;

        port.subOptions.forEach((office) => {
          if (office.label == editRoomInfo.officeName) {
            modalSelectOfficeId = office.value;
          }
        });
      }
    });

    console.log(editRoomInfo);
    modalFloor = editRoomInfo.floor;
    modalRoomName = editRoomInfo.name;
    modalOnlineMeeting = editRoomInfo.onlineMeeting;
    modalEnvDevice = editRoomInfo.envDevice;
    modalEnable = editRoomInfo.enabled;
    modalCapacity = editRoomInfo.capacity;
  }

  modalPortOptHTML = "";
  modalOfficeOptHTML = "";

  document.getElementById("modalTitle").innerHTML = modalTitle;
  document.getElementById("modal-port").value = modalSelectPortId;
  document.getElementById("modal-office").value = modalSelectOfficeId;
  document.getElementById("modal-floor").value = modalFloor;
  document.getElementById("modal-room").value = modalRoomName;
  document.getElementById("modal-onlineMeeting").checked = modalOnlineMeeting;
  document.getElementById("modal-envDevice").checked = modalEnvDevice;
  document.getElementById("modal-enable").checked = modalEnable;
  document.getElementById("modal-capacity").value = modalCapacity;
};

// 渲染會議室modal的初始化下拉選單
const setRoomModalPortOptions = (portOptions) => {
  if (modalSelectPortId == "") {
    modalPortOptHTML = '<option value="" selected>請選擇</option>';

    portOptions.forEach((port) => {
      modalPortOptHTML +=
        '<option value="' + port.value + '">' + port.label + "</option>";
    });
  } else {
    modalPortOptHTML = '<option value="">請選擇</option>';

    portOptions.forEach((port) => {
      modalPortOptHTML +=
        '<option value="' +
        port.value +
        '" ' +
        (modalSelectPortId == port.value ? "selected" : "") +
        ">" +
        port.label +
        "</option>";
    });
  }

  document.getElementById("modal-port").innerHTML = modalPortOptHTML;
};

// 渲染會議室modal的辦公室下拉選單
const setRoomModalOfficeOptions = (officeOptions) => {
  if (modalSelectOfficeId == "") {
    modalOfficeOptHTML = '<option value="" selected>請選擇</option>';

    officeOptions.forEach((office) => {
      modalOfficeOptHTML +=
        '<option value="' + office.value + '">' + office.label + "</option>";
    });
  } else {
    modalOfficeOptHTML = '<option value="">請選擇</option>';

    officeOptions.forEach((office) => {
      modalOfficeOptHTML +=
        '<option value="' +
        office.value +
        '" ' +
        (modalSelectOfficeId == office.value ? "selected" : "") +
        ">" +
        office.label +
        "</option>";
    });
  }
  document.getElementById("modal-office").innerHTML = modalOfficeOptHTML;
};

// 渲染初始化的下拉選單
const setInitOption = () => {
  selectPortId = "";
  selectOfficeId = "";
  selectFloor = "";
  selectRoomId = "";

  portOptHTML = "";
  officeOptHTML = "";
  floorOptHTML = "";
  roomOptHTML = "";

  document.getElementById("port").value = selectPortId;
  document.getElementById("office").value = selectOfficeId;
  document.getElementById("floor").value = selectFloor;
  document.getElementById("room").value = selectRoomId;

  setPortOptions();
  setOfficeOptions();
  setFloorOptions();
  setRoomOptions();
};

// 設定港別的選單資料
const setPortData = (portOptData) => {
  portOptData.forEach((port) => {
    portOptions.push({
      value: port.value,
      label: port.label,
    });
  });
};

// 設定辦公室別的選單資料
const setOfficeOptionData = (officeOptData) => {
  officeOptData.forEach((office) => {
    officeOptions.push({
      value: office.value,
      label: office.label,
    });
  });
};

// 設定樓層的選單資料
const setFloorOptionData = (floorOptData) => {
  floorOptData.forEach((floor) => {
    floorOptions.push({
      value: floor.value,
      label: floor.label,
    });
  });
};

// 設定會議室名稱的選單資料
const setRoomOptionData = (
  roomOptData,
  officeLabel,
  floorValue,
  floorLabel
) => {
  roomOptData.forEach((room) => {
    roomOptions.push({
      value: room.value,
      label: room.label + "(" + officeLabel + " " + floorLabel + ")",
      sortValue: floorValue,
    });
  });
};

// 渲染港別的下拉選單
const setPortOptions = () => {
  portOptHTML = '<option value="" selected>請選擇</option>';
  portOptions.forEach((port) => {
    portOptHTML +=
      '<option value="' + port.value + '">' + port.label + "</option>";
  });
  document.getElementById("port").innerHTML = portOptHTML;
};

// 渲染辦公室別的下拉選單
const setOfficeOptions = () => {
  console.log("setOfficeOptions");
  officeOptHTML = '<option value="" selected>請選擇</option>';
  officeOptions.forEach((office) => {
    officeOptHTML +=
      '<option value="' + office.value + '">' + office.label + "</option>";
  });
  document.getElementById("office").innerHTML = officeOptHTML;
};

// 渲染樓層的下拉選單
const setFloorOptions = () => {
  let filterFloorOptions = [
    ...new Set(floorOptions.map((item) => JSON.stringify(item))),
  ].map((item) => JSON.parse(item));

  filterFloorOptions.sort(function (a, b) {
    return a.value - b.value;
  });

  floorOptHTML = '<option value="" selected>請選擇</option>';
  filterFloorOptions.forEach((floor) => {
    floorOptHTML +=
      '<option value="' + floor.value + '">' + floor.label + "</option>";
  });
  document.getElementById("floor").innerHTML = floorOptHTML;
};

// 渲染會議室名稱的下拉選單
const setRoomOptions = () => {
  roomOptions.sort(function (a, b) {
    return a.sortValue - b.sortValue;
  });
  roomOptHTML = '<option value="" selected>請選擇</option>';
  roomOptions.forEach((room) => {
    roomOptHTML +=
      '<option value="' + room.value + '">' + room.label + "</option>";
  });
  document.getElementById("room").innerHTML = roomOptHTML;
};

// 查詢
const search = () => {
  const data = {
    sort: $("#sort").val(),
    currentPage: 0,
    pageSize: 10,
    portId: selectPortId,
    officeId: selectOfficeId,
    floor: selectFloor,
    roomId: selectRoomId,
    keyword: $("#resultSearch").val(),
  };

  const ajax = {
    url: "/meeting/manage/query",
    data: data,
    success: searchSuccess,
  };

  query(ajax);
};

// 查詢API接口回傳處理
const searchSuccess = (resp) => {
  if (resp.result.totalSize == 0) {
    queryContentResult = [];
    document.getElementById("result").style.display = "none";
    document.getElementById("paging").classList.add("d-none");
    document.getElementById("noDataTxt").style.display = "";
  } else {
    queryContentResult = resp.result.contents;
    document.getElementById("result").style.display = "";
    document.getElementById("paging").classList.remove("d-none");
    document.getElementById("noDataTxt").style.display = "none";

    // 渲染datatable版面
    createDataTableContents(queryContentResult);
    // 渲染分頁器版面
    createPaginationPages(resp.result.currentPage, resp.result.totalPage);

    // 是否顯示上一頁
    if (resp.result.isFirstPage) {
      document.getElementById("left_arrow").classList.add("d-none");
    } else {
      document.getElementById("left_arrow").classList.remove("d-none");
    }

    // 是否顯示下一頁
    if (resp.result.isLastPage) {
      document.getElementById("right_arrow").classList.add("d-none");
    } else {
      document.getElementById("right_arrow").classList.remove("d-none");
    }

    setEditRoomModal(queryContentResult);
  }
};

// 渲染清單列表
const createDataTableContents = (contents) => {
  let tbody = document.getElementById("result").lastElementChild;

  var tbodyHTML = "";
  var trS = '<tr class="row" style="margin-left: 0">';
  var trE = "</tr>";

  contents.forEach((res) => {
    var contentHTML = trS;
    contentHTML =
      contentHTML +
      '<td style="width: 10%;flex: 0 0 auto">' +
      res.portName +
      "</td>";
    contentHTML =
      contentHTML +
      '<td style="width: 16%;flex: 0 0 auto">' +
      res.officeName +
      "</td>";
    contentHTML =
      contentHTML +
      '<td style="width: 10%;flex: 0 0 auto">' +
      res.floor +
      "</td>";
    contentHTML =
      contentHTML +
      '<td style="width: 20%;flex: 0 0 auto">' +
      res.name +
      "</td>";
    contentHTML =
      contentHTML +
      '<td style="width: 10%;flex: 0 0 auto">' +
      (res.enabled ? "啟用" : "不啟用") +
      "</td>";
    contentHTML =
      contentHTML +
      '<td style="width: 12%;flex: 0 0 auto">' +
      (res.onlineMeeting ? "有" : "無") +
      "</td>";
    contentHTML =
      contentHTML +
      '<td style="width: 12%;flex: 0 0 auto">' +
      (res.envDevice ? "有" : "無") +
      "</td>";
    contentHTML =
      contentHTML +
      '<td style="width: 10%;flex: 0 0 auto"><input id="' +
      res.id +
      '" type="button" class="btn rounded-pill btn-purple edit-room" value="修改"/></td>';
    contentHTML += trE;
    tbodyHTML += contentHTML;
  });

  tbody.innerHTML = tbodyHTML;
};

const setEditRoomModal = (contents) => {
  for (
    var i = 0;
    i < document.getElementsByClassName("edit-room").length;
    i++
  ) {
    document
      .getElementsByClassName("edit-room")
      [i].addEventListener("click", function (event) {
        modalType = "edit";
        contents.forEach((content) => {
          if (content.id == event.target.getAttribute("id")) {
            editRoomInfo = content;
            return;
          }
        });

        $("#roomModal").modal("show");
      });
  }
};

// 渲染分頁器
const createPaginationPages = (currentPage, totalPage) => {
  // 先清除舊的分頁按鈕
  let pageList = document.querySelectorAll(".page-btn");
  for (var i = 0; i < pageList.length; i++) {
    console.log(document.getElementById(pageList[i].id));
    document.getElementById(pageList[i].id).remove();
  }

  for (let i = 0; i < totalPage; i++) {
    let pageDiv = document.createElement("div");
    pageDiv.classList.add("d-flex", "align-items-center", "page-btn");
    pageDiv.id = "page";
    let pageBtn = document.createElement("button");
    pageBtn.classList.add("fs-5", "text-light", "px-3", "mx-1", "page_btn");
    if (currentPage == i) {
      pageBtn.classList.add("page_btn_active");
    }

    pageBtn.innerHTML = i + 1;

    pageBtn.addEventListener("click", function () {
      specifyPage(i);
    });

    pageDiv.append(pageBtn);
    document.getElementById("right_arrow").parentNode.before(pageDiv);
  }
};

// 新增會議室
const addRoomInfo = () => {
  modalSelectPortId = document.getElementById("modal-port").value;
  modalSelectOfficeId = document.getElementById("modal-office").value;
  modalFloor = document.getElementById("modal-floor").value;
  modalRoomName = document.getElementById("modal-room").value;
  modalCapacity = document.getElementById("modal-capacity").value;
  modalOnlineMeeting = document.getElementById("modal-onlineMeeting").checked;
  modalEnvDevice = document.getElementById("modal-envDevice").checked;
  modalEnable = document.getElementById("modal-enable").checked;

  // 錯誤訊息
  let error = false;
  // 欄位檢核方式
  const validateAdd = (msg) => {
    if (msg) 
      return '';
    else 
      error = true 
      return '此欄位必填'
  };
  // 港別檢核
  $("#port-alert").text(() => validateAdd(modalSelectPortId));
  // 港別檢核
  $("#office-alert").text(() => validateAdd(modalSelectOfficeId));
  // 港別檢核
  $("#floor-alert").text(() => validateAdd(modalFloor));
  // 港別檢核
  $("#name-alert").text(() => validateAdd(modalRoomName));
  // 港別檢核
  $("#capacity-alert").text(() => validateAdd($("#modal-capacity").val()));

  if (!error) {
	const ajax = {
		url: '/meeting/manage/add',
		data: {
			portId: modalSelectPortId,
			officeId: modalSelectOfficeId,
			floor: modalFloor,
			roomName: modalRoomName,
			capacity: modalCapacity,
			onlineMeeting: modalOnlineMeeting,
			envDevice: modalEnvDevice,
			roomEnable: modalEnable,
		},
		success: addSuccess,
	}
	ajaxSubmit(ajax)
  }
};

const addSuccess = (response) => {
  document.getElementById("room-port").innerHTML = response.result.portName;
  document.getElementById("room-office").innerHTML = response.result.officeName;
  document.getElementById("room-floor").innerHTML = response.result.floor;
  document.getElementById("room-name").innerHTML = response.result.roomName;
  document.getElementById("room-capacity").innerHTML = response.result.capacity;
  document.getElementById("room-onlineMeeting").innerHTML = response.result
    .onlineMeeting
    ? "有"
    : "無";
  document.getElementById("room-envDevice").innerHTML = response.result
    .envDevice
    ? "有"
    : "無";
  document.getElementById("room-enable").innerHTML = response.result.roomEnable
    ? "啟用"
    : "未啟用";

  if (response.result.envDevice) {
    document.getElementById("roomCodeLayout").style.display = "";
    document.getElementById("room-code").innerHTML = response.result.roomCode;
  } else {
    document.getElementById("roomCodeLayout").style.display = "none";
  }

  document.getElementById("successModalTitle").innerHTML = "會議室新增成功";
  document.getElementById("successModalBody").style.display = "";

  $("#roomModal").modal("hide");
  $("#roomSuccessModal").modal("show");
};

const updateRoomInfo = () => {
  console.log("update room to server");
  modalRoomName = document.getElementById("modal-room").value;
  modalCapacity = document.getElementById("modal-capacity").value;
  modalEnable = document.getElementById("modal-enable").checked;
  modalEnvDevice = document.getElementById("modal-envDevice").checked;
  modalOnlineMeeting = document.getElementById("modal-onlineMeeting").checked;

  const data = {
    roomId: editRoomInfo.id,
    roomName: modalRoomName,
    capacity: modalCapacity,
    roomEnable: modalEnable,
    onlineMeetingEnable: modalOnlineMeeting,
    envDeviceEnable: modalEnvDevice,
  };

  console.log(data);

  const ajax = {
    url: "/meeting/manage/update",
    data: data,
    success: updateSuccess,
  };

  ajaxSubmit(ajax);
};


const updateSuccess = (response) => {
  console.log(response);
  document.getElementById("successModalTitle").innerHTML = "會議室修改成功";
  document.getElementById("successModalBody").style.display = "none";

  $("#roomModal").modal("hide");
  $("#roomSuccessModal").modal("show");
};

const deleteRoomInfo = () => {
  console.log("delete room to server");
  const data = {
    roomId: editRoomInfo.id,
  };

  const ajax = {
    url: "/meeting/manage/delete",
    data: data,
    success: function(response){
      console.log(response);
      document.getElementById("successModalTitle").innerHTML = "會議室刪除成功";
      document.getElementById("successModalBody").style.display = "none";
      $("#roomModal").modal("hide");
      $("#roomSuccessModal").modal("show");
    },
  };

  ajaxSubmit(ajax);
};
