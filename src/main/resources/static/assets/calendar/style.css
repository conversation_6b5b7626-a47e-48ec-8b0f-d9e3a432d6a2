
p {
  color: #b3b3b3;
  font-weight: 300; 
}

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: "Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; 
}

a {
  -webkit-transition: .3s all ease;
  -o-transition: .3s all ease;
  transition: .3s all ease; 
}

a, a:hover {
  text-decoration: none !important; 
}

.content {
  padding: 7rem 0; 
}

h2 {
  font-size: 20px; 
}

.form-control:active, .form-control:focus {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none; }

.calendar {
  margin: 0 auto; 
}

.calendar .fc-view-container {
  background-color: #fff;
  -webkit-box-shadow: 0 15px 30px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 15px 30px 0 rgba(0, 0, 0, 0.2); 
}

.calendar .fc-toolbar.fc-header-toolbar .fc-center {
  display: block; 
}

.fc-header-toolbar {
  /*
    the calendar will be butting up against the edges,
    but let's scoot in the header's buttons
    */
  padding-top: 1em;
  padding-left: 1em;
  padding-right: 1em; }

@media (max-width: 767.98px) {
  .fc-toolbar {
    display: block !important;
    text-align: center; 
  }
  .fc-toolbar .fc-center {
    display: block;
    margin-top: 20px;
    margin-bottom: 20px; 
  } 
}
