server.port=8082

spring.config.activate.on-profile=dev

uploadfile.location=./upload/

spring.datasource.url=*********************************************
spring.datasource.username=postgres
spring.datasource.password=P@ssw0rd!!!
spring.datasource.driver-class-name=org.postgresql.Driver

#spring.jpa.hibernate.ddl-auto=create
spring.jpa.database=POSTGRESQL
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.show-sql=true

spring.ldap.urls=ldap://localhost:8389
spring.ldap.username=changwy
spring.ldap.password=1qaz@WSX
spring.ldap.base=dc=twport,dc=com,dc=tw

# spring.ldap.embedded.ldif=classpath:test-server.ldif
# spring.ldap.embedded.base-dn=dc=springframework,dc=org
# spring.ldap.embedded.port=8389

# spring.ldap.embedded.ldif=classpath:prod-server.ldif
# spring.ldap.embedded.base-dn=dc=twport,dc=com,dc=tw
# spring.ldap.embedded.port=8389