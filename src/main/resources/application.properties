server.port=8080
server.servlet.context-path=/meeting-reserve
#\u505C\u7528\u9810\u8A2D\u932F\u8AA4\u756B\u9762
server.error.whitelabel.enabled=false
server.error.path=/error

spring.application.name=meeting_backend
spring.profiles.active=dev

database.schema=public

#logging.config = classpath:log4j2-${spring.profiles.active}.properties

admin.sso.url=http://10.21.1.225/sso/login
admin.backup.sso.url=http://10.21.1.227/sso/login/backup
admin.sso.connect.timeout=60
admin.sso.redirect.url=http://10.21.1.21:8080/meeting-reserve/admin/login

admin.token.validate.url=http://10.21.1.225:9322/api/account/general_token_validate
admin.access.account=kpc001,kpc002,kpc003,kpc004,kpc005,kpc008,kpc012
