<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Properties>
        <Property name="basePath">C:\Program Files\Apache Software Foundation\Tomcat 10.1\logs</Property>
        <Property name="filename">application.log</Property>
        <Property name="charset">utf-8</Property>
    </Properties>

    <Appenders>
        <!-- Console Appender -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%-5level] %logger{36} - %msg%n" charset="${charset}"/>
        </Console>

        <!-- File Appender -->
        <RollingFile name="FileAppender" fileName="${basePath}/${filename}" 
                     filePattern="${basePath}/${filename}.%d{yyyy-MM-dd}">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%-5level] %logger{36} - %msg%n" charset="${charset}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"/>
                <SizeBasedTriggeringPolicy size="20MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>

        <!-- AOP 專用的 Appender -->
        <RollingFile name="AopAppender" fileName="${basePath}/aop.log" 
                     filePattern="${basePath}/aop.log.%d{yyyy-MM-dd}">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%-5level] %logger{36} - %msg%n" charset="${charset}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"/>
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <!-- AOP 相關的 Logger -->
        <Logger name="com.twport.meeting_backend.aspect" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AopAppender"/>
            <AppenderRef ref="FileAppender"/>
        </Logger>

        <!-- 應用程式 Logger -->
        <Logger name="com.twport.meeting_backend" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Logger>

        <!-- Spring Framework Logger -->
        <Logger name="org.springframework" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Logger>

        <!-- Hibernate Logger -->
        <Logger name="org.hibernate" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Logger>

        <!-- Root Logger -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Root>
    </Loggers>
</Configuration>
