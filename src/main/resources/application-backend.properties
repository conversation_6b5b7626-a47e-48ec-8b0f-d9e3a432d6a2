spring.config.activate.on-profile=backend

spring.datasource.url=*******************************************************=${database.schema}
spring.datasource.username=postgres
spring.datasource.password=123456
spring.datasource.driver-class-name=org.postgresql.Driver

#spring.jpa.hibernate.ddl-auto=update
spring.jpa.database=POSTGRESQL
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.show-sql=true
