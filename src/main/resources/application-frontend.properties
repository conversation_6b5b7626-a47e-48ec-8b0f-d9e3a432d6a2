spring.config.activate.on-profile=frontend

uploadfile.location=C:\\Users\\<USER>\\Pictures\\sign_img\\

spring.datasource.url=*******************************************************=${database.schema}
spring.datasource.username=postgres
spring.datasource.password=123456
spring.datasource.driver-class-name=org.postgresql.Driver

#spring.jpa.hibernate.ddl-auto=update
spring.jpa.database=POSTGRESQL
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.show-sql=true

#management.health.ldap.enabled=false

spring.ldap.urls=ldap://**********:3268
spring.ldap.username=<EMAIL>
spring.ldap.password=Aa123456789
spring.ldap.base=OU=\u7E3D\u7D93\u7406\u5BA4(\u7D44\u7E54),OU=\u8463\u4E8B\u9577\u5BA4(\u7D44\u7E54),OU=\u81FA\u7063\u6E2F\u52D9\u7E3D\u516C\u53F8,DC=twport,DC=com,DC=tw

log4j2.basePath=C:\\Program Files\\Apache Software Foundation\\Tomcat 10.1\\logs