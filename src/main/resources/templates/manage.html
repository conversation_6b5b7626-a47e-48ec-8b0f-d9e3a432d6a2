<!DOCTYPE html>
<html lang="en" layout:decorate="~{common/layout}">
  <div layout:fragment="content">
    <style>
      .datatable {
        padding-top: 20px;
        margin-top: 20px;
        margin-bottom: 40px;
      }

      .light-model {
        background-color: white;
        border-radius: 8px;

        ::placeholder {
          color: white;
        }

        .content-search {
          background-color: rgba(218, 220, 224, 0.6);
          input [type="text"] {
            background-color: rgba(59, 80, 90, 1);
            background-image: url("../assets/meeting/images/search-light.png");
          }
        }

        table thead tr th {
          color: rgba(73, 80, 87, 1);
        }

        table tbody tr td {
          color: rgba(73, 80, 87, 1);
        }

        table tbody tr:hover {
          background-color: rgba(218, 220, 224, 0.6);
          border-radius: 8px;
        }
      }
      .select-div {
        width: 12%;
        flex: 0 0 auto;
      }

      .select-div-long {
        width: 18%;
        flex: 0 0 auto;
      }

      .shadow-gray {
        border: solid 1px;
        border-color: rgba(198, 199, 248, 1);
        box-shadow: 0px 0px 0px 3.2px rgba(198, 199, 248, 0.25);
      }

      .content-search {
        margin-top: 5px;
        margin-left: 15px;
        margin-right: 15px;
        padding: 10px;
        border-radius: 5px;
        background-color: rgba(255, 255, 255, 0.098);
      }

      .content-search input[type="text"] {
        border: 1px solid;
        border-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        background-color: rgba(28, 28, 28, 0.4);
        background-image: url("../assets/meeting/images/search.png");
        background-position: 10px 11px;
        background-repeat: no-repeat;
        padding-left: 30px;
        padding-right: 4px;
        padding-top: 8px;
        padding-bottom: 8px;
        color: white;
      }

      .content-search input[type="checkbox"] {
        box-shadow: none;
        border: none;
      }

      .content-search input[type="checkbox"]:checked {
        background-color: gray;
      }

      .content-search input:focus {
        outline: none;
        border-color: aliceblue;
      }

      .datatable-table {
        margin-top: 20px;
        margin-left: 15px;
        margin-right: 15px;
      }

      table thead tr th {
        font-weight: 400;
        color: rgba(255, 255, 255, 0.4);
        padding-left: 8px;
        padding-right: 8px;
        padding-top: 12px;
        padding-bottom: 12px;
        flex: 0 0 auto;
      }

      table tbody tr td {
        font-weight: 400;
        color: rgba(255, 255, 255);
        padding-left: 8px;
        padding-right: 8px;
        padding-top: 12px;
        padding-bottom: 12px;
        flex: 0 0 auto;
      }

      table tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
      }

      .datatable-h3 {
        color: rgba(122, 125, 130, 1);
        font-weight: 700;
        margin-top: 50px;
      }

      .page_btn {
        background-color: rgba(218, 220, 224, 0);
        border: none;
        border-radius: 8px;
      }

      .page_btn:hover,
      .page_btn_active:hover {
        background-color: rgba(218, 220, 224, 0.4);
      }

      .page_btn_active {
        background-color: rgba(218, 220, 224, 0.6);
        border: none;
        border-radius: 8px;
      }

      .add_meeting_btn {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: space-around;
        gap: 10px;
        width: 210px;
        padding: 15px;
        box-sizing: border-box;
        border: none;
        background: linear-gradient(to right, rgb(57, 67, 89), rgb(35, 39, 49));
        cursor: pointer;
        border-radius: 10px;
      }

      .add_meeting_btn:hover {
        background: linear-gradient(
          to right,
          rgb(57, 67, 89, 0.5),
          rgb(35, 39, 49, 0.5)
        );
      }

      .user_txt {
        color: rgb(255, 255, 255, 0.95);
      }

      .add_meeting_img {
        width: 40px;
        height: 40px;
        background-size: cover;
        border-radius: 50%;
      }

      .add_meeting_txt {
        font-size: 16px;
        font-weight: 500;
        color: rgb(255, 255, 255);
      }

      label.required::after {
        content: "＊";
        color: #ff5400;
      }
    </style>

    <div class="container">
      <!-- 標題 -->
      <div class="row">
        <div class="col-12 row">
          <span class="text-light col-10" style="font-size: 32px"
            >會議室管理</span
          >
          <div class="col-2 d-flex justify-content-between align-items-center">
            <button type="button" class="add_meeting_btn" id="addRoom">
              <div class="add_meeting_txt">新增會議室</div>
              <img th:src="@{/assets/meeting/images/plus.png}" alt="" />
            </button>
          </div>
        </div>
        <div class="col-12 mt-3">
          <span class="text-light" style="font-size: 15px"
            >搜尋現有會議室資訊</span
          >
        </div>
      </div>

      <!-- 查詢選單 -->
      <div class="mt-4">
        <div class="row">
          <div class="select-div">
            <label class="text-light">港別</label>
            <select id="port" name="port" class="form-select shadow-gray">
              <option value="" selected>請選擇</option>
            </select>
          </div>

          <div class="select-div">
            <label class="text-light">辦公室別</label>
            <select id="office" name="office" class="form-select shadow-gray">
              <option value="" selected>請選擇</option>
            </select>
          </div>

          <div class="select-div">
            <label class="text-light">樓層</label>
            <select id="floor" name="floor" class="form-select shadow-gray">
              <option value="" selected>請選擇</option>
            </select>
          </div>
          <div class="select-div-long">
            <label class="text-light">會議室名稱</label>
            <select id="room" name="room" class="form-select shadow-gray">
              <option value="" selected>請選擇</option>
            </select>
          </div>
          <div class="col-1 mt-4">
            <input
              id="cleanBtn"
              type="button"
              class="btn rounded-pill btn-purple"
              value="清除"
            />
            <!-- <input id="searchBtn" type="button" class="btn rounded-pill btn-purple" value="送出查詢" onclick="search()" /> -->
          </div>
        </div>
      </div>

      <!-- 資料表 -->
      <div id="datatable" class="datatable">
        <div class="content-search row d-flex justify-content-between">
          <div
            class="row col-10"
            style="
              display: flex;
              flex-direction: row;
              justify-content: flex-start;
              align-items: center;
            "
          >
            <div class="row col-3">
              <input type="text" placeholder="搜尋" id="resultSearch" />
            </div>

            <div class="row col-3 ms-1">
              <div class="col-auto col-form-label">
                <label class="text-light" for="sort">排序：</label>
              </div>
              <div class="col-auto">
                <select id="sort" class="form-select shadow-gray">
                  <option value="port">港別</option>
                  <option value="office">辦公室別</option>
                  <option value="floor">會議室樓層</option>
                  <option value="roomName">會議室名稱</option>
                  <option value="status">啟用狀態</option>
                </select>
              </div>
            </div>
          </div>

          <div class="col-2 d-flex align-items-center justify-content-end">
            <div class="form-check form-switch">
              <input
                class="form-check-input"
                type="checkbox"
                id="colorChange"
              />
              <label
                class="form-check-label text-light"
                id="colorChangeText"
                for="colorChange"
                >深色版</label
              >
            </div>
          </div>
        </div>
        <table id="result" class="datatable-table row">
          <thead class="border-gray-bottom">
            <tr class="row" style="margin-left: 0">
              <th style="width: 10%; flex: 0 0 auto">港別</th>
              <th style="width: 16%; flex: 0 0 auto">辦公室別</th>
              <th style="width: 10%; flex: 0 0 auto">會議室樓層</th>
              <th style="width: 20%; flex: 0 0 auto">會議室名稱</th>
              <th style="width: 10%; flex: 0 0 auto">啟用狀態</th>
              <th style="width: 12%; flex: 0 0 auto">視訊設備</th>
              <th style="width: 12%; flex: 0 0 auto">環控設備</th>
              <th style="width: 10%; flex: 0 0 auto">功能</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
        <div
          id="paging"
          class="column d-flex justify-content-end text-light fs-1 mt-3 d-none"
        >
          <div
            class="d-flex align-items-center"
            style="width: 4%; flex: 0 0 auto"
          >
            <button
              id="left_arrow"
              class="d-flex align-items-center justify-content-center"
              style="border: none; background-color: rgba(225, 225, 225, 0)"
            >
              <img
                th:src="@{/assets/meeting/images/leftArrow.png}"
                style="width: 35%"
                alt=""
              />
            </button>
          </div>
          <div
            class="d-flex align-items-center"
            style="width: 4%; flex: 0 0 auto"
          >
            <button
              id="right_arrow"
              class="d-flex align-items-center justify-content-center"
              style="border: none; background-color: rgba(225, 225, 225, 0)"
            >
              <img
                th:src="@{/assets/meeting/images/rightArrow.png}"
                style="width: 35%"
                alt=""
              />
            </button>
          </div>
        </div>
        <h3 id="noDataTxt" class="datatable-h3 text-center">暫無會議室空間</h3>
      </div>
    </div>

    <div
      class="modal fade"
      id="roomModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="addRoomModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h2 class="modal-title" id="modalTitle">新增會議室</h2>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>

          <div class="modal-body">
            <div class="card">
              <div class="card-body">
                <h5 class="card-title">會議室資訊</h5>
                <span style="color: red; font-size: 0.75rem">
                  標有<span style="padding: 0 0.25rem">＊</span
                  >的欄位為必要資料，請正確填寫。
                </span>

                <div class="row" style="margin-top: 5px">
                  <div class="col-12 mb-3">
                    <label class="required">港別</label>
                    <span
                      id="port-alert"
                      style="color: red; font-size: 0.75rem"
                    ></span>
                    <select id="modal-port" class="form-select shadow-gray">
                      <option value="" selected>請選擇</option>
                    </select>
                  </div>
                  <div class="col-12 mb-3">
                    <label class="required">辦公室別</label>
                    <span
                      id="office-alert"
                      style="color: red; font-size: 0.75rem"
                    ></span>
                    <select id="modal-office" class="form-select shadow-gray">
                      <option value="" selected>請選擇</option>
                    </select>
                  </div>
                  <div class="col-6 mb-3">
                    <label class="required">會議室樓層</label>
                    <span
                      id="floor-alert"
                      style="color: red; font-size: 0.75rem"
                    ></span>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="樓層"
                      id="modal-floor"
                    />
                  </div>
                  <div class="col-6 mb-3">
                    <label class="required">會議室名稱</label>
                    <span
                      id="name-alert"
                      style="color: red; font-size: 0.75rem"
                    ></span>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="會議室名稱"
                      id="modal-room"
                    />
                  </div>
                  <div class="col-6 mb-3">
                    <label class="required">會議室可容納人數</label>
                    <span
                      id="capacity-alert"
                      style="color: red; font-size: 0.75rem"
                    ></span>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="會議室可容納人數"
                      id="modal-capacity"
                    />
                  </div>
                  <div class="col-6 mb-3">
                    <label>是否有視訊設備</label>
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="modal-onlineMeeting"
                      />
                    </div>
                  </div>
                  <div class="col-6 mb-3">
                    <label>是否有環控設備</label>
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="modal-envDevice"
                      />
                    </div>
                  </div>
                  <div class="col-6 mb-3">
                    <label>是否啟用會議室</label>
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="modal-enable"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button class="btn btn-danger" id="btnDelete">刪除</button>
            <button class="btn btn-secondary" id="modalCleanBtn">重選</button>
            <button class="btn btn-primary" id="confirmBtn">確定</button>
          </div>
        </div>
      </div>
    </div>

    <div
      class="modal fade"
      id="roomSuccessModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="addRoomSuccessModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h2 class="modal-title" id="successModalTitle">會議室新增成功</h2>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>

          <div class="modal-body" id="successModalBody">
            <div class="card">
              <div class="card-body">
                <h5 class="card-title">會議室資訊</h5>

                <div class="row">
                  <div class="col-6 mb-3">
                    <label>港別 : </label>
                    <h3 id="room-port"></h3>
                  </div>
                  <div class="col-6 mb-3">
                    <label>辦公室別 : </label>
                    <h3 id="room-office"></h3>
                  </div>
                  <div class="col-6 mb-3">
                    <label>會議室樓層 : </label>
                    <h3 id="room-floor"></h3>
                  </div>
                  <div class="col-6 mb-3">
                    <label>會議室名稱 : </label>
                    <h3 id="room-name"></h3>
                  </div>
                  <div class="col-6 mb-3">
                    <label>會議室可容納人數 : </label>
                    <h3 id="room-capacity"></h3>
                  </div>
                  <div class="col-6 mb-3">
                    <label>是否有視訊設備 : </label>
                    <h3 id="room-onlineMeeting"></h3>
                  </div>
                  <div class="col-6 mb-3">
                    <label>是否有環控設備 : </label>
                    <h3 id="room-envDevice"></h3>
                  </div>
                  <div class="col-6 mb-3">
                    <label>是否啟用會議室 : </label>
                    <h3 id="room-enable"></h3>
                  </div>
                  <div id="roomCodeLayout" class="col-12 mb-3">
                    <label>會議室代號 : </label>
                    <h1 id="room-code"></h1>
                    <h5 style="color: red">
                      請記得將「會議室代號」提供給會議室環控系統廠商設定
                    </h5>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">
              關閉
            </button>
          </div>
        </div>
      </div>
    </div>

    <div id="deleteModal" class="modal fade" aria-hidden="true" aria-labelledby="exampleModalToggleLabel2" tabindex="-1" >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-body">
            <h3 id="globalMessage">請確認是否刪除該會議室</h3>
          </div>
          <div class="modal-footer">
            <button
              id="cancelDeleteBtn"
              class="btn rounded-pill btn-secondary"
              data-bs-target="#deleteModal"
              data-bs-toggle="modal"
              data-bs-dismiss="modal"
            >
              取消
            </button>
            <button
              class="btn rounded-pill btn-danger"
              id="deleteConfirmBtn"
              data-bs-dismiss="modal"
            >
              確定
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- javascript -->
    <script th:inline="javascript">
      let options = [[${options}]];
      console.log(options);
    </script>
    <script th:src="@{/assets/meeting/js/datatable.js}"></script>
    <script th:src="@{/assets/meeting/js/manage.js}"></script>
  </div>
  
</html>
