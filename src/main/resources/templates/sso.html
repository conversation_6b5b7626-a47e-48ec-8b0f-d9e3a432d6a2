<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<title>旅運中心 - 會議室預約系統</title>
	<script src="/assets/js/core/jquery-3.7.1.min.js"></script>
</head>

<body>
	<script th:inline="javascript">
        $(function() {
			var ssoUrl = [[${ssoUrl}]];
			console.log("ssoUrl : " + ssoUrl);
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), 60000);
			fetch(ssoUrl, {signal: controller.signal}).then(response => {
				console.log("status : " + response.status);
				if (response.status == '200') {
					location.href = ssoUrl;
				} else {
					ssoErrorEvent();
				}
			}).catch(error => {
				console.log("error : " + error);
				ssoErrorEvent();
			})
        });
        
        function ssoErrorEvent() {
			location.href = "http://localhost:8080/admin/login?ssoTimeOut=true";
		}
    </script>
</body>

</html>