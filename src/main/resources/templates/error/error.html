<!DOCTYPE html>
<html lang="en" layout:decorate="~{common/layout}">

<div layout:fragment="content" style="color: white;width: 100%;height: 100%;display: flex;flex-direction: column;justify-content: space-around;align-items:center;">
	<h1 th:text="${#ctx.getExchange().getNativeResponseObject().getStatus()}"></h1>
	<h2 th:text="${message}"></h2>
</div>

<th:block layout:fragment="custom-script">
	<script th:inline="javascript">
		var statusCode = [[${#ctx.getExchange().getNativeResponseObject().getStatus()}]];
        console.log(statusCode);
		
        function redirectLoginPage() {
        	var redirectUrl = [[${redirectUrl}]];
    		window.location.href = redirectUrl;
		}
        
        if(statusCode == 401) {
			setTimeout(redirectLoginPage, 3000);
		}
    </script>
</th:block>
</html>