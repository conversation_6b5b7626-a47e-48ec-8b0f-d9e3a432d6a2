<!DOCTYPE html>

<html lang="en" layout:decorate="~{common/layout}">
  <div layout:fragment="content">
    <style>
      .datatable {
        padding-top: 20px;
        margin-top: 20px;
        margin-bottom: 40px;
      }

      .light-model {
        background-color: white;
        border-radius: 8px;

        ::placeholder {
          color: white;
        }

        .content-search {
          background-color: rgba(218, 220, 224, 0.6);

          input[type="text"] {
            background-color: rgba(59, 80, 90, 1);
            background-image: url("../assets/meeting/images/search-light.png");
          }
        }

        table thead tr th {
          color: rgba(73, 80, 87, 1);
        }

        table tbody tr td {
          color: rgba(73, 80, 87, 1);
        }

        table tbody tr:hover {
          background-color: rgba(218, 220, 224, 0.6);
          border-radius: 8px;
        }
      }

      /*.select-div {*/
      /*	width: 12%;*/
      /*	flex: 0 0 auto;*/
      /*}*/

      .select-div-long {
        width: 18%;
        flex: 0 0 auto;
      }

      .shadow-gray {
        border: solid 1px;
        border-color: rgba(198, 199, 248, 1);
        box-shadow: 0px 0px 0px 3.2px rgba(198, 199, 248, 0.25);
      }

      .content-search {
        margin-top: 5px;
        margin-left: 15px;
        margin-right: 15px;
        padding: 10px;
        border-radius: 5px;
        background-color: rgba(255, 255, 255, 0.098);
      }

      .content-search input[type="text"] {
        border: 1px solid;
        border-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        background-color: rgba(28, 28, 28, 0.4);
        background-image: url("../assets/meeting/images/search.png");
        background-position: 10px 11px;
        background-repeat: no-repeat;
        padding-left: 30px;
        padding-right: 4px;
        padding-top: 8px;
        padding-bottom: 8px;
        color: white;
      }

      .content-search input[type="checkbox"] {
        box-shadow: none;
        border: none;
      }

      .content-search input[type="checkbox"]:checked {
        background-color: gray;
      }

      .content-search input:focus {
        outline: none;
        border-color: aliceblue;
      }

      .datatable-table {
        margin-top: 20px;
        margin-left: 15px;
        margin-right: 15px;
      }

      table thead tr th {
        font-weight: 400;
        color: rgba(255, 255, 255, 0.4);
        padding-left: 8px;
        padding-right: 8px;
        padding-top: 12px;
        padding-bottom: 12px;
        flex: 0 0 auto;
        word-wrap: break-word;
        word-break: break-all;
      }

      table tbody tr td {
        font-weight: 400;
        color: rgba(255, 255, 255);
        padding-left: 8px;
        padding-right: 8px;
        padding-top: 12px;
        padding-bottom: 12px;
        flex: 0 0 auto;
        word-wrap: break-word;
        word-break: break-all;
      }

      table tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
      }

      .datatable-h3 {
        color: rgba(122, 125, 130, 1);
        font-weight: 700;
        margin-top: 50px;
      }

      .page_btn {
        background-color: rgba(218, 220, 224, 0);
        border: none;
        border-radius: 8px;
      }

      .page_btn:hover,
      .page_btn_active:hover {
        background-color: rgba(218, 220, 224, 0.4);
      }

      .page_btn_active {
        background-color: rgba(218, 220, 224, 0.6);
        border: none;
        border-radius: 8px;
      }

      .signInModal table {
        /* padding-top: 20px; */
        /* margin-top: 20px; */
        margin-bottom: 40px;
        margin-left: 10px;
        margin-right: 10px;
      }

      .signInModal table thead tr th {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.4);
        padding-left: 8px;
        padding-right: 8px;
        padding-top: 12px;
        padding-bottom: 12px;
        flex: 0 0 auto;
      }

      .signInModal table tbody tr td {
        font-weight: 400;
        color: rgba(0, 0, 0);
        padding-left: 8px;
        padding-right: 8px;
        padding-top: 12px;
        padding-bottom: 12px;
        flex: 0 0 auto;
      }

      .signInModal table tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 8px;
      }
    </style>

    <div class="container">
      <!-- 標題 -->
      <div class="column">
        <div class="col">
          <span class="text-light" style="font-size: 32px">會議查詢</span>
        </div>
        <div class="col mt-3">
          <span class="text-light" style="font-size: 15px"
            >搜尋當前所有會議室資訊</span
          >
        </div>
      </div>

      <!-- 查詢選單 -->
      <div class="mt-4">
        <div class="row">
          <div class="select-div" style="width: 12%; flex: 0 0 auto">
            <div class="col-md-12 mb-3">
              <label class="text-light">預約單位 </label>
              <select
                id="department"
                name="query-form"
                class="form-select shadow-gray"
              >
                <option value="" selected>請選擇</option>
                <option
                  th:each="departSelect : ${resp.departmentSelects}"
                  th:value="${departSelect.value}"
                  th:text="${departSelect.label}"
                ></option>
              </select>
            </div>
          </div>
          <div class="select-div" style="width: 12%; flex: 0 0 auto">
            <label class="text-light">樓層</label>
            <select
              id="floor"
              name="query-form"
              class="form-select shadow-gray"
            >
              <option value="" selected>請選擇</option>
              <option
                th:each="floorSelect : ${resp.floorSelects}"
                th:value="${floorSelect.value}"
                th:text="${floorSelect.label}"
              ></option>
            </select>
          </div>
          <div class="select-div-long">
            <label class="text-light">會議室</label>
            <select id="room" name="query-form" class="form-select shadow-gray">
              <option value="" selected>請選擇</option>
              <option
                th:each="roomSelect : ${resp.meetingRoomSelects}"
                th:value="${roomSelect.value}"
                th:text="${roomSelect.label}"
              ></option>
            </select>
          </div>
          <div class="col-4 row">
            <div class="col-6">
              <label class="text-light">開始日期</label>
              <input
                id="startDate"
                name="query-form"
                type="date"
                class="form-control shadow-gray"
              />
            </div>
            <div class="col-6">
              <label class="text-light">結束日期</label>
              <input
                id="endDate"
                name="query-form"
                type="date"
                class="form-control shadow-gray"
              />
            </div>
          </div>
          <div class="col-1 mt-4">
            <input
              id="cleanBtn"
              type="button"
              class="btn rounded-pill btn-purple"
              value="清除"
            />
          </div>
        </div>
      </div>

      <!-- 資料表 -->
      <div id="datatable" class="datatable">
        <div class="content-search row d-flex justify-content-between">
          <div class="row col-10">
            <input type="text" class="col-3" placeholder="搜尋" id="resultSearch" />
            <div class="row col-3 ms-1">
              <div class="col-auto col-form-label">
                <label class="text-light" for="sort">排序：</label>
              </div>
              <div class="col-auto">
                <select id="sort" class="form-select shadow-gray">
                  <option value="theme" selected>會議主題</option>
                  <option value="roomName">會議室</option>
                  <option value="hostName">主持人</option>
                  <option value="departmentName">預約單位</option>
                  <option value="contactName">聯絡人</option>
                  <option value="contactPhone">聯絡電話</option>
                  <option value="reserveDateStart">預約時間</option>
                  <option value="status">使用狀態</option>
                </select>
              </div>
            </div>
          </div>
          <div class="col-2 d-flex align-items-center justify-content-end">
            <div class="form-check form-switch">
              <input
                class="form-check-input"
                type="checkbox"
                id="colorChange"
              />
              <label
                class="form-check-label text-light"
                id="colorChangeText"
                for="colorChange"
                >深色版</label
              >
            </div>
          </div>
        </div>
        <table class="datatable-table row">
          <thead class="border-gray-bottom">
            <tr class="row">
              <th style="width: 16%">會議主題</th>
              <th style="width: 14%">會議室</th>
              <th style="width: 6%">主持人</th>
              <th style="width: 10%">預約單位</th>
              <th style="width: 14%">
                聯絡人/
                <br />
                聯絡電話
              </th>
              <th style="width: 10%; text-align: center">預約日期</th>
              <th style="width: 11%; text-align: center">預約時段</th>
              <th style="width: 7%; text-align: center">使用狀態</th>
              <th style="width: 12%; text-align: center">功能</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
        <div th:replace="~{common/page}"></div>
        <h3 id="noDataTxt" class="datatable-h3 text-center">
          目前暫無會議資訊
        </h3>
      </div>
    </div>

    <!--明細MODAL START-->
    <div
      class="modal fade"
      id="reserveDetailModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="reserveModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered" style="max-width: 1200px">
        <div class="modal-content">
          <div class="modal-header">
            <h2 class="modal-title">會議細節</h2>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>

          <div class="modal-body">
            <div class="row">
              <div class="col-6 mb-3">
                <label>會議主題 : </label>
                <h5 id="detail-theme"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>預約時間 : </label>
                <h5 id="detail-time"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議室樓層 : </label>
                <h5 id="detail-floor"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議室名稱 : </label>
                <h5 id="detail-roomname"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議主持人 : </label>
                <h5 id="detail-hostname"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議聯絡人 : </label>
                <h5 id="detail-contactname"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議聯絡人電話 : </label>
                <h5 id="detail-contactphone"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議預約單位 : </label>
                <h5 id="detail-deptart"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議與會單位 : </label>
                <h5 id="detail-attenddeptart"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議模式 : </label>
                <h5 id="detail-meetingmode"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議情境 : </label>
                <h5 id="detail-situationmode"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>預估與會人數 : </label>
                <h5 id="detail-members"></h5>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--明細MODAL END-->

    <!-- 簽到紀錄MODAL START -->
    <div
      class="modal fade signInModal"
      id="signInModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="signInModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h2 class="modal-title">簽到紀錄</h2>
            <div class="d-flex align-items-center">
              <input
                type="image"
                id="fileDownloadBtn"
                th:src="@{/assets/meeting/images/download-black.png}"
                alt=""
              />
              <button
                type="button"
                class="btn-close ms-2"
                aria-label="Close"
                data-bs-dismiss="modal"
              ></button>
            </div>
          </div>
          <div class="modal-body">
            <table class="row" id="signInRecord">
              <thead class="border-gray-bottom">
                <tr class="row">
                  <th style="width: 20%">單位</th>
                  <th style="width: 20%">職稱</th>
                  <th style="width: 20%">簽到方式</th>
                  <th style="width: 20%">簽到時間</th>
                  <th style="width: 20%">功能</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
            <h3 id="noSignInRecord" class="text-center" style="display: none">
              目前暫無簽到紀錄
            </h3>
          </div>
        </div>
      </div>
    </div>
    <!-- 簽到紀錄MODAL END -->

    <!-- 簽到紀錄編輯MODAL START -->
    <div
      class="modal fade signInEditModal"
      id="signInEditModal"
      role="dialog"
      aria-labelledby="signInEditModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div id="sign-record" class="modal-content">
          <div class="modal-header">
            <h2 class="modal-title">簽到紀錄編輯</h2>
            <div class="d-flex align-items-center">
              <button
                id="signEditModalClose"
                type="button"
                class="btn-close ms-2"
                aria-label="Close"
                data-bs-dismiss="modal"
              ></button>
            </div>
          </div>

          <div class="modal-body">
            <div class="card m-3">
              <div class="card-body">
                <h5 class="card-title">與會單位</h5>

                <div class="row">
                  <div class="col-6">
                    <label>公司</label>
                    <div class="form-floating mb-3">
                      <div class="select-div">
                        <select
                          id="modal-edit-company-select"
                          class="form-select shadow-gray"
                        >
                          <option value="">請選擇</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="col-6">
                    <label>單位</label>
                    <div class="form-floating mb-3">
                      <div class="select-div">
                        <select
                          id="modal-edit-department-select"
                          class="form-select shadow-gray"
                        >
                          <option value="">請選擇</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div
                    class="col-12"
                    id="modalEditDepartText"
                    style="display: none"
                  >
                    <input
                      id="modal-edit-department-text"
                      type="text"
                      class="form-control"
                      placeholder="與會單位"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div class="card m-3">
              <div class="card-body">
                <h5 class="card-title">職稱</h5>

                <div class="row">
                  <div class="col-12">
                    <input
                      id="modal-edit-jobTitle"
                      type="text"
                      class="form-control"
                      placeholder="職稱"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div class="card m-3">
              <div class="card-body">
                <h5 class="card-title">姓名</h5>

                <div class="row">
                  <div class="col-12">
                    <label>簽到方式</label>
                    <div style="display: flex; flex-direction: row">
                      <div style="margin-right: 10px">
                        <input
                          id="signInType"
                          type="radio"
                          class="form-check-input"
                          value="SIGNIN"
                          disabled="disabled"
                        />
                        <label class="form-check-label">手動</label>
                      </div>
                      <div style="margin-right: 10px">
                        <input
                          id="signInType"
                          type="radio"
                          class="form-check-input"
                          value="IDCARD"
                          disabled="disabled"
                        />
                        <label class="form-check-label">識別證</label>
                      </div>
                    </div>
                  </div>
                  <div class="col-12">
                    <label>簽名</label>
                    <div id="modal-edit-name"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button class="btn btn-secondary" id="modalCleanBtn">重選</button>
            <button class="btn btn-primary" id="modalConfirmBtn">確定</button>
          </div>
        </div>
      </div>
    </div>
    <!-- 簽到紀錄編輯MODAL END -->

    <!-- javascript -->
    <script th:inline="javascript">
      let initSelectOption = [[${resp}]];
    </script>
    <script th:src="@{/assets/meeting/js/recordFunction.js}"></script>
    <script th:src="@{/assets/meeting/js/record.js}"></script>
    <script th:src="@{/assets/meeting/js/datatable.js}"></script>
  </div>
</html>
