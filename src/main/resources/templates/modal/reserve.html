<!-- 外誆 -->
<div
  class="modal fade"
  id="reserveModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="reserveModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" style="max-width: 1200px">
    <!-- 內容 -->
    <div class="modal-content">
        <!-- HEADER -->
        <div class="modal-header d-flex flex-row justify-content-between">
            <!-- 會議預約 / 修改會議 -->
            <h3 class="modal-title" id="reserve-modal-title">會議預約</h2>
            <!-- 刪除 / 關閉 -->
            <div class="d-flex flex-row">
                <input type="image" 
                    id="deleteBtn"
                    class="me-1 col-6 p-1"
                    th:src="@{/assets/meeting/images/trash.svg}" 
                    style="display: none;"
                />
                <input type="image"
                    class="col-6"
                    th:src="@{/assets/meeting/images/delete.svg}"
                    data-bs-dismiss="modal"
                    style="height: 40px; width: 30px;"
                />
            </div>
        </div>

        <!-- BODY -->
        <div class="modal-body flex-row pb-0">

            <!-- 標題 -->
            <div class="d-flex flex-row justify-content-between col-12">
                <!-- 備註（左） -->
                <h6 class="text-danger col-4 align-content-center">標有＊的欄位為必要資料，請正確填寫</h6>
                <!-- 預約類型（右） -->
                <div class="col-8 d-flex flex-row justify-content-between">
                    <div class="col-10 d-flex flex-row">
                        <span id="type" class="align-content-center" style="flex: 0 0 auto; width: 15%;">預約類型：</span>
                        <div class="form-check col-2 align-content-center">
                            <input class="form-check-input" type="radio" name="reserveType" id="oneceReserveChange" checked />
                            <label class="form-check-label" id="onceReserveChangeText" for="oneceReserveChange">一次性預約</label>
                        </div>
                        <div class="form-check col-2 align-content-center">
                            <input class="form-check-input" type="radio" name="reserveType" id="dailyReserveChange" />
                            <label class="form-check-label" id="dailyReserveChangeText" for="dailyReserveChange">每日預約</label>
                        </div>
                        <div class="form-check col-2 align-content-center">
                            <input class="form-check-input" type="radio" name="reserveType" id="reserveChange" />
                            <label class="form-check-label" id="reserveChangeText" for="reserveChange">每週預約</label>
                        </div>
                        <div class="form-check col-2 align-content-center">
                            <input class="form-check-input" type="radio" name="reserveType" id="monthReserveChange" />
                            <label class="form-check-label" id="monthReserveChangeText" for="monthReserveChange">每月預約</label>
                        </div>
                    </div>
                    <input class="btn btn-danger col-2" id="cleanBtn" value="全部清除" />
                </div>
            </div>

            <!-- 內容 -->
            <div class="d-flex flex-row col-12 justify-content-center mt-4 ">

                <!-- 會議基本資訊 -->
                <div class="col-5 card me-1">
                    <div class="card-body">
                        <!-- 標題 -->
                        <h6 class="card-title">會議基本資訊</h6>
                        <!-- 內容 -->
                        <div class="row">
                            <!-- 會議主題 -->
                            <div class="col-12 row">
                                <label class="required col-6" for="theme">會議主題</label>
                                <span class="text-danger col-6 text-end" id="theme_error"></span>
                            </div>
                            <!-- 會議主題 -->
                            <div class="col-12 mt-1">
                                <input type="text" class="form-control" placeholder="會議主題" id="theme" />
                            </div>

                            <!-- 開始日期 -->
                            <div class="col-6 row mt-2" name="startDate">
                                <label class="required col-6" for="reserveDate">開始日期</label>
                                <span class="text-danger col-6 text-end" id="reserveDate_error"></span>
                            </div>
                            <!-- 週期性結束日期 -->
                            <div class="col-6 row mt-2 ms-2" name="endDate">
                                <label class="col-12" for="reserveDateEnd">週期性結束日期</label>
                            </div>
                            <!-- 開始日期 -->
                            <div class="col-6 mt-1" name="startDate">
                                <input type="date" class="form-control flatp-date-picker" id="reserveDate" />
                            </div>
                            <!-- 週期性結束日期 -->
                            <div class="col-6 mt-1" name="endDate">
                                <input type="date" class="form-control flatp-date-picker" id="reserveDateEnd" disabled="disabled" />
                            </div>

                            <!-- 每月第幾週 -->
                            <div class="col-6 row mt-2" name="weekOfMonthSelect" style="display: none;">
                                <label class="required col-7" for="weekOfMonth">每月第幾週</label>
                                <span class="text-danger col-5 text-end" id="weekOfMonth_error"></span>
                            </div>
                            <!-- 星期幾 -->
                            <div class="col-6 row mt-2 ms-2" name="dayOfWeekSelect" style="display: none;">
                                <label class="required col-7" for="dayOfWeek">星期幾</label>
                                <span class="text-danger col-5 text-end" id="dayOfWeek_error"></span>
                            </div>
                            <!-- 每月第幾週 -->
                            <div class="col-6 mt-1" name="weekOfMonthSelect" style="display: none;">
                                <select class="form-select shadow-gray" id="weekOfMonth">
                                    <option value="" selected>請選擇</option>
                                    <option value="1">第 1 週</option>
                                    <option value="2">第 2 週</option>
                                    <option value="3">第 3 週</option>
                                    <option value="4">第 4 週</option>
                                    <option value="5">最後一週</option>
                                </select>
                            </div>
                            <!-- 星期幾 -->
                            <div class="col-6 mt-1" name="dayOfWeekSelect" style="display: none;">
                                <select class="form-select shadow-gray" id="dayOfWeek">
                                    <option value="" selected>請選擇</option>
                                    <option value="1">星期一</option>
                                    <option value="2">星期二</option>
                                    <option value="3">星期三</option>
                                    <option value="4">星期四</option>
                                    <option value="5">星期五</option>
                                    <option value="6">星期六</option>
                                    <option value="7">星期日</option>
                                </select>
                            </div>

                            <!-- 開始時間 -->
                            <div class="col-6 row mt-2">
                                <label class="required col-7" for="beginTime">開始時間</label>
                                <span class="text-danger col-5 text-end" id="beginTime_error"></span>
                            </div>
                            <!-- 結束時間 -->
                            <div class="col-6 row mt-2 ms-2">
                                <label class="required col-7" for="endTime">結束時間</label>
                                <span class="text-danger col-5 text-end" id="endTime_error"></span>
                            </div>
                            <!-- 開始時間 -->
                            <div class="col-6 mt-1">
                                <input type="date" class="form-control flatp-time-picker" id="beginTime" />
                            </div>
                            <!-- 結束時間 -->
                            <div class="col-6 mt-1">
                                <input type="date" class="form-control flatp-time-picker" id="endTime" />
                            </div>

                            <!-- 會議室名稱 -->
                            <div class="col-6 row mt-2">
                                <label class="required col-7" for="room">會議室名稱</label>
                                <span class="text-danger col-5 text-end" id="roomId_error"></span>
                            </div>
                            <!-- 樓層 -->
                            <div class="col-6 row mt-2 ms-2">
                                <label class="required col-7" for="floor">樓層</label>
                                <span class="text-danger col-5 text-end" id="floor_error"></span>
                            </div>
                            <!-- 會議室名稱 -->
                            <div class="col-6 mt-1">
                                <select id="room" class="form-select shadow-gray">
                                    <option value="" selected>請選擇</option>
                                </select>
                            </div>
                            <!-- 樓層 -->
                            <div class="col-6 mt-1">
                                <input type="text" class="form-control shadow-gray" id="floor" disabled="disabled" />
                            </div>

                        </div>
                    </div>
                </div>
                
                <!-- 會議細節 -->
                <div class="col-7 card">
                    <!-- 標題 -->
                    <div class="card-body">
                        <h6 class="card-title">會議細節</h6>
                        <!-- 內容 -->
                        <div class="row">
                            <!-- 主持人 -->
                            <div class="col-6 row">
                                <label class="required col-7" for="host">主持人</label>
                                <span class="text-danger col-5 text-end" id="hostName_error"></span>
                                <div class="input-group mt-1">
                                    <!-- 手動輸入匡 -->
                                    <input 
                                        type="text" 
                                        class="form-control shadow-gray" 
                                        aria-label="Text input with 2 dropdown buttons" 
                                        id="hostName"
                                        placeholder="主持人姓名" 
                                    />
                                    <!-- 下拉選單按鈕 -->
                                    <button 
                                        type="button" 
                                        class="btn btn-primary dropdown-toggle"
                                        id="host-select-btn"
                                        data-bs-toggle="dropdown"
                                        aria-haspopup="true" 
                                        aria-expanded="false" 
                                    ></button>
                                    <!-- 下拉選單 -->
                                    <div class="dropdown-menu" style="width: 400px">
                                        <div class="card-body">
                                            <h6 class="card-title">快速帶入 - 主持人</h6>
                                            <div class="row">
                                                <div class="col-12 mb-3">
                                                    <label class="required" for="host-company">公司</label>
                                                    <div class="select-div">
                                                        <select id="host-company" class="form-select shadow-gray">
                                                            <option value="" selected>請選擇</option>
                                                            <option
                                                                th:each="companySelect : ${resp.companySelects}"
                                                                th:value="${companySelect.value}"
                                                                th:text="${companySelect.label}"
                                                            >
                                                            </option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-12 mb-3">
                                                    <label class="required" for="host-depart">單位</label>
                                                    <div class="select-div">
                                                        <select id="host-depart" class="form-select shadow-gray">
                                                            <option value="" selected>請選擇</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-12 mb-3">
                                                    <label class="required" for="host-user">人員</label>
                                                    <div class="select-div">
                                                        <select id="host-user" class="form-select shadow-gray">
                                                            <option value="" selected>請選擇</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-12 mb-3" style="text-align: right;">
                                                    <button type="button" class="btn btn-secondary"
                                                        id="host-user-confirm" disabled="disabled">確定</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 聯絡人 -->
                            <div class="col-6 row">
                                <label class="required col-7" for="contact">聯絡人</label>
                                <span class="text-danger col-5 text-end" id="contact_error"></span>
                                <div class="input-group mt-1">
                                    <!-- 手動輸入匡 -->
                                    <input 
                                        type="text" 
                                        class="form-control shadow-gray" 
                                        aria-label="Text input with 2 dropdown buttons" 
                                        id="contact-name"
                                        placeholder="聯絡人姓名" 
                                    />
                                    <!-- 下拉選單按鈕 -->
                                    <button 
                                        type="button" 
                                        class="btn btn-primary dropdown-toggle"
                                        id="contact-select-btn"
                                        data-bs-toggle="dropdown"
                                        aria-haspopup="true" 
                                        aria-expanded="false" 
                                    ></button>
                                    <!-- 下拉選單 -->
                                    <div class="dropdown-menu" style="width: 400px">
                                        <div class="card-body">
                                            <h6 class="card-title">快速帶入 - 聯絡人</h6>
                                            <div class="row">
                                                <div class="col-12 mb-3">
                                                    <label class="required" for="contact-company">公司</label>
                                                    <div class="select-div">
                                                        <select id="contact-company" class="form-select shadow-gray">
                                                            <option value="" selected>請選擇</option>
                                                            <option
                                                                th:each="companySelect : ${resp.companySelects}"
                                                                th:value="${companySelect.value}"
                                                                th:text="${companySelect.label}"
                                                            >
                                                            </option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-12 mb-3">
                                                    <label class="required" for="contact-depart">單位</label>
                                                    <div class="select-div">
                                                        <select id="contact-depart" class="form-select shadow-gray">
                                                            <option value="" selected>請選擇</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-12 mb-3">
                                                    <label class="required" for="contact-user">人員</label>
                                                    <div class="select-div">
                                                        <select id="contact-user" class="form-select shadow-gray">
                                                            <option value="" selected>請選擇</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-12 mb-3" style="text-align: right;">
                                                    <button type="button" class="btn btn-secondary" id="contact-user-confirm" disabled="disabled">確定</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 預約單位 -->
                            <div class="col-6 row mt-1">
                                <label class="required col-7" for="reserveDept">預約單位</label>
                                <span class="text-danger col-5 text-end" id="reserveDeptName_error"></span>
                                <input type="hidden" id="reserveCompanyId" />
                                <input type="hidden" id="reserveDeptId" />
                                <div class="input-group">
                                    <!-- 手動輸入匡 -->
                                    <input 
                                        type="text" 
                                        class="form-control shadow-gray" 
                                        aria-label="Text input with 2 dropdown buttons" 
                                        id="reserveDeptName"
                                        placeholder="預約單位" 
                                        disabled
                                    />
                                    <!-- 下拉選單按鈕 -->
                                    <button 
                                        type="button" 
                                        class="btn btn-primary dropdown-toggle"
                                        id="reserveDept-select-btn"
                                        data-bs-toggle="dropdown"
                                        aria-haspopup="true" 
                                        aria-expanded="false" 
                                    ></button>
                                    <!-- 下拉選單 -->
                                    <div class="dropdown-menu" style="width: 400px">
                                        <div class="card-body">
                                            <h6 class="card-title">快速帶入 - 預約單位</h6>
                                            <div class="row">
                                                <div class="col-12 mb-3">
                                                    <label class="required" for="reserveDept-company">公司</label>
                                                    <div class="select-div">
                                                        <select id="reserveDept-company" class="form-select shadow-gray">
                                                            <option value="" selected>請選擇</option>
                                                            <option
                                                                th:each="companySelect : ${resp.companySelects}"
                                                                th:value="${companySelect.value}"
                                                                th:text="${companySelect.label}"
                                                            >
                                                            </option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-12 mb-3">
                                                    <label class="required" for="reserveDept-depart">單位</label>
                                                    <div class="select-div">
                                                        <select id="reserveDept-depart" class="form-select shadow-gray">
                                                            <option value="" selected>請選擇</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-12 mb-3" style="text-align: right;">
                                                    <button type="button" class="btn btn-secondary"
                                                        id="reserveDept-depart-confirm">確定</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 聯絡人電話 -->
                            <div class="col-5 row mt-1">
                                <label class="required col-7" for="contact-phone">聯絡人電話</label>
                                <span class="text-danger col-5 text-end" id="contact_phone_error"></span>
                                <input type="text" class="form-control ms-3" placeholder="聯絡人電話" id="contact-phone" />
                            </div>
                            <!-- 預估人數 -->
                            <div class="col-6 row mt-1">
                                <label class="required col-7" for="members">預估人數</label>
                                <span class="text-danger col-5 text-end" id="members_error"></span>
                                <div class="col-10">
                                    <input type="text" class="form-control" placeholder="預估人數" id="members" />
                                </div>
                            </div>
                            <!-- 會議模式 -->
                            <div class="col-6 row mt-1">
                                <label class="required col-7">會議模式</label>
                                <div class="form-check">
                                    <div th:each="meetModeSelect : ${resp.meetingModeSelects}">
                                        <input 
                                            type="radio" 
                                            class="form-check-input ms-1"
                                            name="meetingMode" 
                                            th:value="${meetModeSelect.value}"
                                            onclick="meetingUrl(this)" 
                                        />
                                        <label class="form-check-label ms-1" th:text="${meetModeSelect.label}">
                                            會議模式
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <!-- 會議情境 -->
                            <div class="col-6 row mt-1">
                                <label class="required col-7">會議情境</label>
                                <div class="form-check">
                                    <div th:each="situationModeSelect : ${resp.situationModeSelects}">
                                        <input 
                                            type="radio" 
                                            class="form-check-input ms-1"
                                            name="situationMode"
                                            th:value="${situationModeSelect.value}"
                                        /> 
                                        <label class="form-check-label ms-1" th:text="${situationModeSelect.label}">
                                            會議情境
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <!-- 是否公開 -->
                            <div class="col-6 row mt-1">
                                <label class="required col-7">是否公開</label>
                                <div class="form-check">
                                    <div>
                                        <input 
                                            type="radio" 
                                            class="form-check-input ms-1"
                                            name="personal"
                                            value="false"
                                        />
                                        <label class="form-check-label ms-1">
                                            公開
                                        </label>
                                    </div>
                                    <div>
                                        <input 
                                            type="radio" 
                                            class="form-check-input ms-1"
                                            name="personal"
                                            value="true"
                                        />
                                        <label class="form-check-label ms-1">
                                            不公開
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <!-- 與會單位 -->
                            <div class="col-6 row mt-1">
                                <label class="required col-7">與會單位</label>
                                <span class="text-danger col-5 text-end" id="attendDept_error"></span>
                                <div class="col-10 mt-1">
                                    <select id="attendDept" class="form-select shadow-gray">
                                        <option value="" selected>請選擇</option>
                                        <option th:each="departSelect : ${resp.departmentSelects}"
                                            th:value="${departSelect.value}"
                                            th:text="${departSelect.label}"></option>
                                    </select>
                                </div>
                                <button class="form-control col-2 mt-1 mb-1" id="addBtn" type="button" style="width: 40px">+</button>
                            </div>
                            <!-- 會議連結 -->
                            <div class="col-6 row mt-1 d-none" id="urlDiv">
                                <label class="required col-7" for="url">會議連結</label>
                                <span class="text-danger col-5 text-end" id="url_error"></span>
                                <div class="col-10">
                                    <input type="text" id="url" class="form-control ms-2" placeholder="請輸入會議連結" />
                                </div>
                            </div>
                            <!-- 與會單位 -->
                            <div class="col-12 d-flex flex-row" style="flex-wrap: wrap;" id="dept-name"></div>
                    </div>
                </div>
            
            </div>
        
        </div>
    
        <!-- FOOTER -->
        <div class="modal-footer border-0">
            <input type="button" class="btn btn-lg rounded-pill btn-purple" value="預約" id="confirmBtn" />
        </div>
    </div>
  </div>
</div>
