<!DOCTYPE html>

<html lang="en" layout:decorate="~{common/layout}">
  <div layout:fragment="content">
    <!-- chart_css -->
    <link
      rel="stylesheet"
      type="text/css"
      th:href="@{/assets/meeting/css/chart.css}"
    />
    <link
      rel="stylesheet"
      type="text/css"
      th:href="@{/assets/meeting/css/statistics.css}"
    />

    <div class="container">
      <!-- 標題 -->
      <div class="column">
        <div class="col">
          <span class="text-light" style="font-size: 32px">統計報表</span>
        </div>
        <div class="col mt-3">
          <span class="text-light" style="font-size: 15px"
            >檢視會議室使用資訊</span
          >
        </div>
      </div>

      <!-- echart -->
      <div class="row1">
        <div id="pie" style="width: 98%; height: 300px"></div>
        <div id="line" style="width: 100%; height: 300px"></div>
      </div>
      <div class="row2">
        <div id="bar" class="mt-3"></div>
      </div>

      <!-- 查詢選單 -->
      <div class="mt-4">
        <div class="row">
          <div class="select-div-long">
            <label class="text-light">會議室</label>
            <select id="room" name="query-form" class="form-select shadow-gray">
              <option value="" selected>請選擇</option>
              <option
                th:each="roomSelect : ${select.meetingRoomSelects}"
                th:value="${roomSelect.value}"
                th:text="${roomSelect.label}"
              ></option>
            </select>
          </div>
          <div class="col-4 row">
            <div class="col-6">
              <label class="text-light">開始日期</label>
              <input
                id="startDate"
                name="query-form"
                type="date"
                class="form-control shadow-gray"
              />
            </div>
            <div class="col-6">
              <label class="text-light">結束日期</label>
              <input
                id="endDate"
                name="query-form"
                type="date"
                class="form-control shadow-gray"
              />
            </div>
          </div>
          <div class="col-1 mt-4">
            <input
              id="cleanBtn"
              type="button"
              class="btn rounded-pill btn-purple"
              value="清除"
            />
          </div>
        </div>
      </div>

      <!-- 資料表 -->
      <div id="datatable" class="datatable mt-2">
        <div class="content-search row d-flex justify-content-between">
          <div
            class="row col-10"
            style="
              display: flex;
              flex-direction: row;
              justify-content: flex-start;
              align-items: center;
            "
          >
            <div style="width: 1%; height: auto">
              <img
                id="downloadBtn"
                th:src="@{/assets/meeting/images/download-white.png}"
                style="width: 20px; height: 20px"
                title="匯出"
              />
            </div>
            <div class="col-3 ms-1">
              <input type="text" placeholder="會議主題搜尋" id="resultSearch" />
            </div>
            <div class="row col-3 ms-1">
              <div class="col-auto col-form-label">
                <label class="text-light" for="sort">排序：</label>
              </div>
              <div class="col-auto">
                <select id="sort" class="form-select shadow-gray">
                  <option value="theme">會議主題</option>
                  <option value="reserveDateStart">會議日期</option>
                  <option value="roomName">會議室</option>
                  <option value="departmentName">預約單位</option>
                  <option value="hostName">主持人</option>
                  <option value="personal">是否公開</option>
                  <option value="status">狀態</option>
                </select>
              </div>
            </div>
          </div>

          <div class="col-2 d-flex align-items-center justify-content-end">
            <div class="form-check form-switch">
              <input
                class="form-check-input"
                type="checkbox"
                id="colorChange"
              />
              <label
                class="form-check-label text-light"
                id="colorChangeText"
                for="colorChange"
                >深色版</label
              >
            </div>
          </div>
        </div>
        <table class="datatable-table row">
          <thead class="border-gray-bottom">
            <tr class="row">
              <th style="width: 17%">會議主題</th>
              <th style="width: 12%">會議日期</th>
              <th style="width: 9%">持續時間</th>
              <th style="width: 14%">會議室</th>
              <th style="width: 9%">預約單位</th>
              <th style="width: 8%">聯絡人</th>
              <th style="width: 8%">主持人</th>
              <th style="width: 8%">是否公開</th>
              <th style="width: 7%">狀態</th>
              <th style="width: 8%">預約人數</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
        <!-- <div id="paging"
					class="column d-flex justify-content-end text-light fs-1 mt-3 d-none">
				<div class="d-flex align-items-center ms-4">
					<span style="font-size: 20px;" id="pageSize" class="text-light">每頁筆數：</span>
				</div>
				<div style="flex: 1;">
					<select class="form-select" style="width: auto;height:40px;" id="editPageSize">
						<option value=10 selected>10</option>
						<option value=20>20</option>
						<option value=30>30</option>
					</select>
				</div>
				<div class="d-flex align-items-center"
					style="width: 4%; flex: 0 0 auto;">
					<button id="left_arrow"
						class="d-flex align-items-center justify-content-center"
						style="border: none; background-color: rgba(225, 225, 225, 0);">
						<img th:src="@{/assets/meeting/images/leftArrow.png}"
							style="width: 35%;" alt="" />
					</button>
				</div>
				<div class="d-flex align-items-center"
						style="width: 4%; flex: 0 0 auto;">
						<button id="right_arrow"
							class="d-flex align-items-center justify-content-center"
							style="border: none; background-color: rgba(225, 225, 225, 0);">
							<img th:src="@{/assets/meeting/images/rightArrow.png}"
								style="width: 35%;" alt="" />
						</button>
					</div>
				</div> -->
        <div th:replace="~{common/page}"></div>
        <h3 id="noDataTxt" class="datatable-h3 text-center">
          目前暫無會議資訊
        </h3>
      </div>
    </div>
    <script th:src="@{/assets/meeting/js/datatable.js}"></script>

    <!-- echart.js -->
    <script th:src="@{/assets/echart/js/echart.min.js}"></script>

    <script th:inline="javascript">
      var resp = [[${resp}]];
      var select = [[${select}]];
    </script>

    <script th:src="@{/assets/meeting/js/statistics.js}"></script>
  </div>
</html>
