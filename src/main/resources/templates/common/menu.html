<!DOCTYPE html>
<body>
	<div class="col-2">
		<span th:text="${#httpServletRequest}"></span>
		<div class="menu_Content">
			<img  class="menu_logo" th:src="@{/assets/meeting/images/menu-logo.png}" alt=""/>
			<div name="menuItem" th:each="menu : ${session.menus}">
				<a class="menuBtn" th:href="@{${menu.taskUrl}}">
					<img class="menuBtn_img" th:src="@{${menu.taskImage}}" alt="" />
					<span class="menuBtn_txt"  th:text="${menu.taskName}">會議室預約</span>
				</a>
			</div>
			<a th:href="@{/user/logout}">
				<button class="user">
					<div class="user_txt" sec:authentication="principal.name">王小明</div>
					<img th:src="@{/assets/meeting/images/logout.png}" alt="" />
				</button>
			</a>
		</div>
	</div>

	<script>
		$(function() {
			menuInit();
		})

		function menuInit() {
			$("div[name=menuItem] a").each(function() {
				if ($(this).attr("href") === window.location.pathname) {
					$(this).addClass("menuBtn-active");
				}
			});
		}
	</script>

</body>

</html>

