<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="https://www.thymeleaf.org"
	xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
	xmlns:sec="https://www.thymeleaf.org/thymeleaf-extras-springsecurity6">

<div th:replace="~{common/head}"></div>

<body class="layoutBackGround row">
	
	<!-- 通過ctx可取得HttpServletResponse -->
	<th:block th:if="${#ctx.getExchange().getNativeResponseObject().getStatus() == 200}">
		<!-- menu -->
		<div class="col-2" th:include="~{common/menu}"></div>

		<!-- content -->
		<div class="row col-10 p-5">
			<div layout:fragment="content"></div>

			<!-- footer -->
			<div th:replace="~{common/footer}"></div>
		</div>
	</th:block>

	<th:block th:unless="${#ctx.getExchange().getNativeResponseObject().getStatus() == 200}">
		<!-- content -->
		<div class="row col-12 p-5">
			<div layout:fragment="content"></div>

			<!-- footer -->
			<div th:replace="~{common/footer}"></div>
		</div>
	</th:block>

	<div class="modal" id="loadingModal" tabindex="-1" role="dialog"
		data-bs-backdrop="static" data-bs-keyboard="false"
		aria-labelledby="loadingModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="text-center" style="padding: 20px">
					<div class="spinner-border text-primary"
						style="width: 5rem; height: 5rem;" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
					<h3 style="margin: 20px">處理中，請稍候</h3>
				</div>
			</div>
		</div>
	</div>

	<script>
		const loadingModalShow = () => $('#loadingModal').modal('show');
		const loadingModalHide = () => $('#loadingModal').modal('hide');
	</script>

	<th:block layout:fragment="custom-script" />
</body>
</html>