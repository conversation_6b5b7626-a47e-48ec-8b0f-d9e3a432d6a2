<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
	<style>
	@page {
		size: A4;
		margin: 1cm;
	}
	
	body {
		font-family: Noto Serif TC ExtraLight;
		position: relative;
	}
	
	.pdf-content {
		align-content: center;
		padding: 10px;
	}
	
	table {
		border: 3px solid #6b675d;
		border-spacing: 0;
		text-align: center;
		border-collapse: collapse;
		font-size: 16px;
	}
	
	table thead th {
		border-right: 3px solid #6b675d;
	}
	table tbody tr td {
		border: 3px solid #6b675d;
	}
	</style>
</head>

<body>
	<div class="pdf-content">
		<h3 th:text="${theme}" style="text-align: center;">「高雄港埠旅運中心辦公裝修及智慧化應用統包工程」增設監視設備點交</h3>
		<h3>
			一、 時間：
			<th:block th:text="${reserveTime}">113年1月18日（星期四）下午2時</th:block>
		</h3>
		<h3>
			二、 地點：
			<th:block th:text="${reserveLocate}">旅運中心I-center</th:block>
		</h3>
		<h3>
			三、 主持人：
			<th:block th:text="${hostName}"></th:block>
		</h3>
		<h3>四、 出席單位及人員：</h3>
		<table>
			<thead>
				<tr>
					<th>單位</th>
					<th>職稱</th>
					<th>姓名</th>
					<th>職稱</th>
					<th>姓名</th>
				</tr>
			</thead>
			<tbody>
				<th:block th:each="recordMap: ${signRecord}">
					<tr th:each="i : ${#numbers.sequence(1, recordMap.value.size, 2)}">
						<th:block th:if="${recordMap.value.size ge 1 && i eq 1}">
							<td width="20%" th:rowspan="${recordMap.key.rowspanSize}">
								<th:block th:if="${recordMap.key.companyName}" th:text="${recordMap.key.companyName}"></th:block>
								<th:block th:text="${recordMap.key.departmentName}"></th:block>
							</td>
						</th:block>

						<th:block th:if="${recordMap.value.size gt i - 1}">			
							<td width="5%" th:text="${recordMap.value[i - 1].jobTitle}"></td>
							<th:block th:if="${recordMap.value[i - 1].signType eq 'IDCARD'}">						
								<td width="20%" th:text="${recordMap.value[i - 1].signName}"></td>
							</th:block>
							<th:block th:if="${recordMap.value[i - 1].signType eq 'SIGNIN'}">						
								<td width="20%">
									<img th:src="@{'data:image/png;base64,' + ${recordMap.value[i - 1].signName}}" width="120"></img>
								</td>
							</th:block>
						</th:block>
						
						<th:block th:if="${recordMap.value.size gt i}">			
							<td width="5%" th:text="${recordMap.value[i].jobTitle}"></td>
							<th:block th:if="${recordMap.value[i].signType eq 'IDCARD'}">						
								<td width="20%" th:text="${recordMap.value[i].signName}"></td>
							</th:block>
							<th:block th:if="${recordMap.value[i].signType eq 'SIGNIN'}">						
								<td width="20%">
									<img th:src="@{'data:image/png;base64,' + ${recordMap.value[i].signName}}" width="120"></img>
								</td>
							</th:block>
						</th:block>

						<th:block th:unless="${recordMap.value.size gt i}">
							<td width="5%"></td>
							<td width="20%"></td>
						</th:block>
					</tr>
				</th:block>
			</tbody>
		</table>
	</div>
</body>
</html>