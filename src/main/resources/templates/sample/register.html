<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>高雄港旅運中心</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css"
          rel="stylesheet" integrity="sha384-wEmeIV1mKuiNpC+IOBjI7aAzPcEZeedi5yW5f2yOq55WWLwNGmvvx4Um1vskeMj0"
          crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" th:href="@{/assets/meeting/css/login.css}"/>
    <link rel="icon" type="image/png" th:href="@{/assets/meeting/images/favicon.ico}">
</head>
<body class="loginBackGround">
    <img class="loginLogo" th:src="@{/assets/meeting/images/logo.png}">

    <div class="rect" style="height: 1000px">
        <img class="loginTitle" th:src="@{/assets/meeting/images/logoWithTxt.png}">
        <div class="content">
	        <p class="titleTxt">
	            會議室預約系統 - 帳號註冊
	        </p>
	        <!-- <h3 th:if="${param.error}" style="color: red">帳號或密碼錯誤</h3> -->
	        <form th:object="${user}" th:action="@{/sample/web/register}" method="post">
	        	<div class="form-group mx-1 mb-1">
                    <label class="inputText" for="account">帳號</label>
                    <input th:field="*{username}" type="text" class="form-control" name="username">
                	<h3 style="color: red" th:if="${#fields.hasErrors('username')}" th:errors="*{username}"></h3>
                </div>
                <div class="form-group mx-1 mb-1">
                    <label class="inputText" for="password">密碼</label>
                    <input th:field="*{password}" type="password" class="form-control" name="password">
                	<h3 style="color: red" th:if="${#fields.hasErrors('password')}" th:errors="*{password}"></h3>
                </div>
                <div class="form-group mx-1 mb-1">
                    <label class="inputText">姓名</label>
                    <input th:field="*{name}" type="text" class="form-control" name="name">
                    <h3 style="color: red" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></h3>
                </div>
                <div class="form-group mx-1 mb-1">
                    <label class="inputText">所屬公司</label>
                    <select id="company" class="form-select shadow-gray" name="company">
						<option value="" selected>請選擇</option>
						<option 
							th:each="companySelect : ${companySelect}" 
							th:value="${companySelect.value}"
							th:text="${companySelect.label}"></option>
					</select>
					<h3 style="color: red" th:if="${#fields.hasErrors('company')}" th:errors="*{company}"></h3>
                </div>
                <div class="form-group mx-1 mb-1">
                    <label class="inputText">所屬單位</label>
                    <select id="department" class="form-select shadow-gray" name="department">
						<option value="" selected>請選擇</option>
					</select>
					<h3 style="color: red" th:if="${#fields.hasErrors('department')}" th:errors="*{department}"></h3>
                </div>
                <div class="login">
                    <button class="btn btn-outline-light rounded-pill btn-rg loginBtn" type="submit">註冊</button>
                </div>
	        </form>
        </div>
    </div>

    <div class="copyright">
        Copyright © 2024 Kaohsiung Port Cruise Termial
    </div>
    
    <script th:inline="javascript">
		let options = [[${companySelect}]];
		console.log(options);
	</script>
    
    <script type="text/javascript">
    	document.getElementById('company').addEventListener('change', function(e) {
			let selectValue = e.target.value;
			console.log(selectValue);
			
			if(selectValue == '' || selectValue == null) {
				document.getElementById('department').innerHTML = '<option value="" selected>請選擇</option>';
			}
			
			options.forEach(opt => {
				console.log(opt)
				if(opt.value == selectValue) {
					let departmentOptionHTML = '<option value="" ' + (opt.subOptions.length == 0 ? 'selected' : '') + '>請選擇</option>';
					
					opt.subOptions.forEach((subOpt, index) => {
						console.log(index);
						departmentOptionHTML += '<option value="' + subOpt.value + '" ' + (index == 0 ? 'selected' : '') + '>' + subOpt.label + '</option>';
					});
					
					document.getElementById('department').innerHTML = departmentOptionHTML;
				}
			});
		});
    </script>
</body>
</html>