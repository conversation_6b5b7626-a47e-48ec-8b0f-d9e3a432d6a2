<!DOCTYPE html>
<html lang="en" layout:decorate="~{common/layout}">
<div layout:fragment="content">
	<style>
		.light-model {
			background-color: white;
			border-radius: 8px;

			::placeholder {
				color: white;
			}

			.content-search {
				background-color: rgba(218, 220, 224, 0.6);

				input[type=text] {
					background-color: rgba(59, 80, 90, 1);
					background-image: url("../assets/meeting/images/search-light.png");
				}
			}

			table thead tr th {
				color: rgba(73, 80, 87, 1);
			}

		}

		.select-div {
			width: 12%;
			flex: 0 0 auto;
		}

		.select-div-long {
			width: 18%;
			flex: 0 0 auto;
		}

		.shadow-gray {
			border: solid 1px;
			border-color: rgba(198, 199, 248, 1);
			box-shadow: 0px 0px 0px 3.2px rgba(198, 199, 248, 0.25);
		}

		.content-search {
			margin-top: 5px;
			margin-left: 15px;
			margin-right: 15px;
			padding: 10px;
			border-radius: 5px;
			background-color: rgba(255, 255, 255, 0.098);
		}

		.content-search input[type=text] {
			border: 1px solid;
			border-color: rgba(255, 255, 255, 0.1);
			border-radius: 8px;
			background-color: rgba(28, 28, 28, 0.4);
			background-image: url("../assets/meeting/images/search.png");
			background-position: 10px 11px;
			background-repeat: no-repeat;
			padding-left: 30px;
			padding-right: 4px;
			padding-top: 8px;
			padding-bottom: 8px;
			color: white;
		}

		.content-search input[type=checkbox] {
			box-shadow: none;
			border: none;
		}

		.content-search input[type=checkbox]:checked {
			background-color: gray;
		}

		.content-search input:focus {
			outline: none;
			border-color: aliceblue;
		}

		.datatable-table {
			margin-top: 20px;
			margin-left: 15px;
			margin-right: 15px;
		}

		table thead tr th {
			font-weight: 400;
			color: rgba(255, 255, 255, 0.4);
			padding-left: 8px;
			padding-right: 8px;
			padding-top: 12px;
			padding-bottom: 12px;
		}

		.datatable-h3 {
			color: rgba(122, 125, 130, 1);
			font-weight: 700;
			margin-top: 50px;
		}
	</style>
	
	<div class="container-fluid">

		<!-- 標題 -->
		<div class="column">
			<div class="col">
				<span class="text-light" style="font-size: 32px;">會議查詢</span>
			</div>
			<div class="col mt-3">
				<span class="text-light" style="font-size: 15px;">搜尋當前所有會議室資訊</span>
			</div>
		</div>

		<!-- 查詢選單 -->
		<div class="mt-4">
			<div class="row">1
				<div class="select-div">
					<select class="form-select shadow-gray">
						<option>預約單位</option>
					</select>
				</div>
				<div class="select-div">
					<select id="floor" class="form-select shadow-gray">
						<option value="" selected>樓層</option>
						<option th:each="floorSelect : ${resp.meetingRoomSelects}" th:value="${floorSelect.value}"
							th:text="${floorSelect.label}"></option>
					</select>
				</div>
				<div class="select-div-long">
					<select id="room" class="form-select shadow-gray">
						<option value="" selected>會議室</option>
					</select>
				</div>
				<div class="col-4 row">
					<div class="col-6">
						<input id="startDate" type="date" class="form-control shadow-gray" />
					</div>
					<div class="col-6">
						<input id="endDate" type="date" class="form-control shadow-gray" />
					</div>
				</div>
				<div class="col-1">
					<input id="searchBtn" type="button" class="btn rounded-pill btn-purple" value="送出查詢" />
				</div>
			</div>
		</div>

		<!-- 資料表 -->
		<div class="pt-2 mt-2" style="height: 600px;">
			<div class="content-search row d-flex justify-content-between">
				<div class="col-2 d-flex align-items-center">
					<div class="form-check form-switch">
						<input class="form-check-input" type="checkbox" id="colorChange">
						<label class="form-check-label text-light" id="colorChangeText" for="colorChange">深色版</label>
					</div>
				</div>
				<div class="row col-3">
					<input type="text" placeholder="搜尋" />
				</div>
			</div>
			<table class="datatable-table row" id="datatable" style="font-size: 20px; display: none">
				<thead class="border-gray-bottom">
					<tr class="row">
						<th style="width: 20%; flex: 0 0 auto">會議室</th>
						<th style="width: 10%; flex: 0 0 auto">會議主題</th>
						<th style="width: 10%; flex: 0 0 auto">主持人</th>
						<th style="width: 10%; flex: 0 0 auto">預約單位</th>
						<th style="width: 10%; flex: 0 0 auto">預約人</th>
						<th style="width: 10%; flex: 0 0 auto">預約日期</th>
						<th style="width: 10%; flex: 0 0 auto">預約時段</th>
						<th style="width: 10%; flex: 0 0 auto">使用狀態</th>
						<th style="width: 10%; flex: 0 0 auto">功能</th>
					</tr>
				</thead>
				<tbody id="data-body">
				</tbody>
			</table>
			<h3 id="noDataTxt" class="datatable-h3 text-center" style="display: none">目前暫無會議資訊</h3>
		</div>
	</div>

	<script th:src="@{/assets/meeting/js/datatable.js}"></script>
	<!-- javascript -->
	<script th:inline="javascript">
		let resp = [[${resp}]];
	</script>
	
	<script>
		function floorSelectChangeEvent(value) {
			console.log(value)
			let options = '<option value="" selected>會議室</option>';
			resp.meetingRoomSelects.forEach((floorSelect) => {
				if (floorSelect.value == value) {
					floorSelect.subOptions.forEach((roomSelect) => {
						var optionHTML = '<option value="' + roomSelect.value + '">' + roomSelect.label + '</option>';
						console.log(optionHTML);
						options += optionHTML;
					})
				}
			});
			
			document.getElementById('room').innerHTML = options;
		}
		
		function searchClickEvent() {
			const data = {
					floor: document.getElementById('floor').value,
					room: document.getElementById('room').value,
					startDate: document.getElementById('startDate').value,
					endDate: document.getElementById('endDate').value
				};

			const ajax = {
					url: '/sample/api/datatable/query',
					data: data,
					success: searchSuccess
				};

			query(ajax);
		}
		
		function searchSuccess(resp) {
			if(resp.contents.length > 0) {
				setTableBodyContent(resp.contents);
				document.getElementById('datatable').style.display = '';
                document.getElementById('noDataTxt').style.display = 'none';
			}else {
				document.getElementById('datatable').style.display = 'none';
                document.getElementById('noDataTxt').style.display = '';
			}
		}
	</script>
	
	<script type="text/javascript">
		document.getElementById('floor').addEventListener('change', function(e){
			floorSelectChangeEvent(e.target.value);
		});
		
		document.getElementById('searchBtn').addEventListener('click', searchClickEvent);
	</script>
	
	<script>
        const columnName = ['roomNameDisplay', 'theme', 'host', 'department', 'reserveUser', 'reserveDate', 'reserveTime', 'statusDisplay'];

        /**
         * table body 畫面內容渲染
         */
         const setTableBodyContent = (pageContent) => {
            console.log('setTableBodyContent');
            var trTagS = '<tr class="row" style="margin-top: 10px">';
            var trTagE = '</tr>';
            var bodyContent = '';

            pageContent.forEach((element) => {
                var trContent = '';
                trContent += trTagS;
                trContent += createTdContent(element);
                trContent += trTagE;
                bodyContent += trContent;
            });
            document.getElementById('data-body').innerHTML = bodyContent;
        }

        /**
         * 產出table body的內容
         * @param {} data 
         * @returns 
         */
        const createTdContent = (data) => {
            var tdTagS_first = '<td style="width: 20%; flex: 0 0 auto; color:white;"><span style="font-size: 16px;">';
            var tdTagS = '<td style="width: 10%; flex: 0 0 auto; color:white;"><span style="font-size: 16px;">';
            var tdTagE = '</span></td>';
            var tdContentList = '';
            columnName.forEach((item, index) => {
                var tdContent = '';
                if(index == 0) {
                	tdContent += tdTagS_first;
				}else {
					tdContent += tdTagS;
				}
                
                tdContent += data[item];
                tdContent += tdTagE;
                tdContentList += tdContent;
            });

            return tdContentList;
        }

        /**
         * 分頁器畫面渲染
         */
        const createPagination = (totalPage) => {
            for(var i = 0; i < totalPage; i++) {
                var pageLi = document.createElement('li');
                pageLi.classList.add("paginate_button", "page-item", "page-btn");
                pageLi.id = 'page-' + i;

                var pageA = document.createElement('li');
                pageA.classList.add("page-link");
                pageA.href = '#';
                pageA.innerHTML = i + 1;
                pageLi.append(pageA);
                document.getElementById('next').before(pageLi);

                pageLi.addEventListener('click', function() {
                    console.log('goPage');
                    pageClick(i);
                });
            }
        }

        /**
         * 設定分頁器各元素屬性
         */
        const setPaginationStyle = (currentPage, totalPage, pageSize, totalSize) => {
            if(currentPage == 0) {
                document.getElementById('previous').classList.add('disabled');
                document.getElementById('first').classList.add('disabled');
            }else {
                document.getElementById('previous').classList.remove('disabled');
                document.getElementById('first').classList.remove('disabled');
            }

            if(currentPage == totalPage - 1) {
                document.getElementById('next').classList.add('disabled');
                document.getElementById('last').classList.add('disabled');
            }else {
                document.getElementById('next').classList.remove('disabled');
                document.getElementById('last').classList.remove('disabled');
            }

            if(document.querySelector('.active') != null) {
                document.querySelector('.active').classList.remove('active');
            }

            document.getElementById('page-' + currentPage).classList.add('active');

            let firstPos = currentPage * pageSize + 1;

            let lastPos = (currentPage * pageSize) + pageSize > totalSize ? totalSize : (currentPage * pageSize) + pageSize;

            console.log(lastPos);

            document.getElementById('datatable-info').innerHTML = 'Showing ' + firstPos + ' to ' + lastPos + ' of ' + totalSize + ' entries';
        }
    </script>
</div>

</html>