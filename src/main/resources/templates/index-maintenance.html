<!DOCTYPE html>
<html lang="en">

<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<title>旅運中心 - 會議室預約系統</title>
	<meta content="width=device-width, initial-scale=1.0, shrink-to-fit=no" name="viewport" />
	<link rel="icon" href="../assets/img/favicon.ico" type="image/x-icon" />

	<!-- Fonts and icons -->
	<script src="../assets/js/plugin/webfont/webfont.min.js"></script>
	<script>
		WebFont.load({
			google: {families: ["Public Sans:300,400,500,600,700"]},
			custom: {
				families: [
					"Font Awesome 5 Solid",
					"Font Awesome 5 Regular",
					"Font Awesome 5 Brands",
					"simple-line-icons",
				],
				urls: ["../assets/css/fonts.min.css"],
			},
			active: function () {
				sessionStorage.fonts = true;
			},
		});
	</script>

	<!-- CSS Files -->
	<link rel="stylesheet" href="../assets/css/bootstrap.min.css" />
	<link rel="stylesheet" href="../assets/css/plugins.min.css" />
	<link rel="stylesheet" href="../assets/css/kaiadmin.min.css" />

	<!-- CSS Just for demo purpose, don't include it in your project -->
	<link rel="stylesheet" href="../assets/css/demo.css" />

	<style>
		.image-bg {
			background-image: url('./assets/img/Kaohsiung_Port_Cruise_Terminal.jpg');
			background-size: cover;
			background-color: rgba(26, 32, 53, .6);
			background-blend-mode: multiply;
		}
	</style>
</head>

<body>
	<div class="wrapper">
		<!-- Sidebar -->
		<div class="sidebar" data-background-color="dark">
			<div class="sidebar-logo">
				<!-- Logo Header -->
				<div class="logo-header" data-background-color="dark">
					<a href="index-maintenance.html" class="logo">
						<img src="../assets/img/logo-gray.png" alt="navbar brand" class="navbar-brand" height="30" />
					</a>
					<div class="nav-toggle">
						<button class="btn btn-toggle toggle-sidebar">
							<i class="gg-menu-right"></i>
						</button>
						<button class="btn btn-toggle sidenav-toggler">
							<i class="gg-menu-left"></i>
						</button>
					</div>
					<button class="topbar-toggler more">
						<i class="gg-more-vertical-alt"></i>
					</button>
				</div>
				<!-- End Logo Header -->
			</div>

			<div class="sidebar-wrapper scrollbar scrollbar-inner">
				<div class="sidebar-content">
					<ul class="nav nav-secondary">
						<li class="nav-item active">
							<a href="index-maintenance.html">
								<i class="fas fa-book"></i>
								<p style="font-size: large">會議室預約</p>
							</a>
						</li>
						<li class="nav-item">
							<a href="#">
								<i class="fas fa-book-open"></i>
								<p style="font-size: large">會議查詢</p>
							</a>
						</li>
					</ul>
				</div>
			</div>
		</div>
		<!-- End Sidebar -->

		<div class="main-panel">
			<div class="main-header" style="background: rgba(26, 32, 53, 1);">
				<!-- Navbar Header -->
				<nav class="navbar navbar-header navbar-header-transparent navbar-expand-lg border-bottom">
					<div class="container-fluid">
						<nav
							class="navbar navbar-header-left navbar-expand-lg navbar-form nav-search p-0 d-none d-lg-flex">
						</nav>

						<ul class="navbar-nav topbar-nav ms-md-auto align-items-center">
							<li class="nav-item topbar-user dropdown hidden-caret">
								<a class="dropdown-toggle profile-pic" data-bs-toggle="dropdown" href="#"
									aria-expanded="false">
									<div class="avatar-sm">
										<img src="../assets/img/profile.jpg" alt="..." class="avatar-img rounded-circle" />
									</div>
									<span class="profile-username">
										<!-- SSO帳號放在這 -->
										<span class="fw-bold" th:text="${account}" style="color: white;font-weight: bold;"></span>
									</span>
								</a>
								<ul class="dropdown-menu dropdown-user animated fadeIn">
									<div class="dropdown-user-scroll scrollbar-outer">
										<li>
											<div class="user-box">
												<div class="avatar-lg">
													<img src="../assets/img/profile.jpg" alt="image profile"
														class="avatar-img rounded" />
												</div>
												<div class="u-text">
												</div>
											</div>
										</li>
										<li>
											<a class="dropdown-item" href="#">登出</a>
										</li>
									</div>
								</ul>
							</li>
						</ul>
					</div>
				</nav>
				<!-- End Navbar -->
			</div>

			<div class="container" style="display: flex;justify-content: center;align-items: center;">
				<h1 style="font-size: xx-large;font-weight: bold;">系統維護中</h1>
			</div>
			<!-- End Custom template -->
		</div>
		<!--   Core JS Files   -->
		<script src="../assets/js/core/jquery-3.7.1.min.js"></script>
		<script src="../assets/js/core/popper.min.js"></script>
		<script src="../assets/js/core/bootstrap.min.js"></script>

		<!-- jQuery Scrollbar -->
		<script src="../assets/js/plugin/jquery-scrollbar/jquery.scrollbar.min.js"></script>

		<!-- Chart JS -->
		<script src="../assets/js/plugin/chart.js/chart.min.js"></script>

		<!-- jQuery Sparkline -->
		<script src="../assets/js/plugin/jquery.sparkline/jquery.sparkline.min.js"></script>

		<!-- Chart Circle -->
		<script src="../assets/js/plugin/chart-circle/circles.min.js"></script>

		<!-- Datatables -->
		<script src="../assets/js/plugin/datatables/datatables.min.js"></script>

		<!-- Bootstrap Notify -->
		<script src="../assets/js/plugin/bootstrap-notify/bootstrap-notify.min.js"></script>

		<!-- jQuery Vector Maps -->
		<script src="../assets/js/plugin/jsvectormap/jsvectormap.min.js"></script>
		<script src="../assets/js/plugin/jsvectormap/world.js"></script>

		<!-- Sweet Alert -->
		<script src="../assets/js/plugin/sweetalert/sweetalert.min.js"></script>

		<!-- Kaiadmin JS -->
		<script src="../assets/js/kaiadmin.min.js"></script>

		<!-- Kaiadmin DEMO methods, don't include it in your project! -->
		<script src="../assets/js/setting-demo.js"></script>
		<script src="../assets/js/demo.js"></script>
		<script>
			$("#lineChart").sparkline([102, 109, 120, 99, 110, 105, 115], {
				type: "line",
				height: "70",
				width: "100%",
				lineWidth: "2",
				lineColor: "#177dff",
				fillColor: "rgba(23, 125, 255, 0.14)",
			});

			$("#lineChart2").sparkline([99, 125, 122, 105, 110, 124, 115], {
				type: "line",
				height: "70",
				width: "100%",
				lineWidth: "2",
				lineColor: "#f3545d",
				fillColor: "rgba(243, 84, 93, .14)",
			});

			$("#lineChart3").sparkline([105, 103, 123, 100, 95, 105, 115], {
				type: "line",
				height: "70",
				width: "100%",
				lineWidth: "2",
				lineColor: "#ffa534",
				fillColor: "rgba(255, 165, 52, .14)",
			});
		</script>
</body>

</html>