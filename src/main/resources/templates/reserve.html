<!DOCTYPE html>
<html lang="en" layout:decorate="~{common/layout}">
  <div layout:fragment="content">
    <link
      rel="stylesheet"
      type="text/css"
      th:href="@{/assets/meeting/css/reserve.css}"
    />

    <div class="container-fluid">
      <!-- 樓層 -->
      <div>
        <div class="row">
          <div class="col-1 d-flex align-items-center text-light">
            <span style="font-size: 20px">樓層：</span>
          </div>
          <div class="col-11">
            <input
              type="button"
              name="floorBtnAll"
              class="btn btn-lg rounded-pill btn-outline-darkBlue-reverse btn-content active"
              value="全選"
            />
            <input
              th:each="floorSelect : ${resp.meetingRoomSelects}"
              th:id="${floorSelect.value}"
              type="button"
              name="floorBtn"
              class="btn btn-lg rounded-pill btn-outline-darkBlue-reverse btn-content ms-2 active"
              th:value="${floorSelect.label}"
            />
          </div>
        </div>
      </div>

      <!-- 會議室 -->
      <div class="mt-1">
        <div class="row">
          <div class="col-1 d-flex align-items-center text-light">
            <span style="font-size: 20px">會議室：</span>
          </div>
          <div class="col-11">
            <input
              type="button"
              name="roomBtnAll"
              class="btn btn-lg rounded-pill btn-outline-darkBlue-reverse btn-content active"
              value="全選"
            />
            <span
              th:each="floorSelect : ${resp.meetingRoomSelects}"
              th:id="'floor-' + ${floorSelect.value}"
            >
              <input
                th:each="roomSelect : ${floorSelect.subOptions}"
                th:id="${roomSelect.value}"
                type="button"
                name="roomBtn"
                class="btn btn-lg rounded-pill btn-outline-darkBlue-reverse btn-content ms-2 active"
                th:value="${roomSelect.label + '(' + roomSelect.tooltipTitle + ')'}"
                data-bs-toggle="tooltip"
                th:title="${roomSelect.tooltipTitle}"
              />
            </span>
          </div>
        </div>
      </div>

      <!-- 日期 -->
      <div class="mt-1">
        <div class="row">
          <div class="col-1"></div>
          <div class="col-8 row">
            <div class="col-4 pt-1">
              <input id="date" type="date" class="form-control" />
            </div>
          </div>
        </div>
      </div>

      <!-- 行事曆 -->
      <div class="mt-3">
        <div class="row border-gray rounded mt-3" id="calendar">
          <div class="content-search row d-flex justify-content-end">
            <div class="col-2 d-flex align-items-center justify-content-end">
              <div class="form-check form-switch">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="colorChange"
                />
                <label
                  class="form-check-label text-light"
                  id="colorChangeText"
                  for="colorChange"
                  >深色版</label
                >
              </div>
            </div>
          </div>

          <div
            class="d-flex justify-content-between align-items-center border-gray-bottom"
          >
            <div class="row">
              <div class="align-content-center col-1">
                <input
                  type="image"
                  id="reduceDate"
                  th:src="@{/assets/meeting/images/chevron-left-solid.svg}"
                  alt=""
                />
              </div>
              <div class="text-center col-10 row">
                <span id="calendar-date" class="fs-2 text-white ms-2"></span>
              </div>
              <div class="align-content-center col-1">
                <input
                  type="image"
                  id="addDate"
                  th:src="@{/assets/meeting/images/chevron-right-solid.svg}"
                  alt=""
                />
              </div>
            </div>
            <div>
              <select id="calendar_select" class="form-select">
                <option value="daily" selected>日</option>
                <option value="weekly">週</option>
                <option value="monthly">月</option>
              </select>
            </div>
          </div>

          <!-- 日 -->
          <table id="daily_table">
            <thead>
              <tr></tr>
            </thead>
            <tbody
              class="border-top border-bottom"
              id="scroll-container-daily"
            ></tbody>
          </table>

          <!-- 週 -->
          <table id="weekly_table" style="display: none">
            <thead>
              <tr></tr>
            </thead>
            <tbody
              class="border-top border-bottom"
              id="scroll-container-weekly"
            ></tbody>
          </table>

          <!-- 月 -->
          <table id="monthly_table" style="display: none">
            <thead>
              <tr>
                <th class="text-center py-3" style="flex: 0 auto">日</th>
                <th class="text-center py-3" style="flex: 0 auto">一</th>
                <th class="text-center py-3" style="flex: 0 auto">二</th>
                <th class="text-center py-3" style="flex: 0 auto">三</th>
                <th class="text-center py-3" style="flex: 0 auto">四</th>
                <th class="text-center py-3" style="flex: 0 auto">五</th>
                <th class="text-center py-3" style="flex: 0 auto">六</th>
              </tr>
            </thead>
            <tbody class="border-top border-bottom"></tbody>
          </table>
        </div>
      </div>

      <!--end-->
    </div>

    <!--預約MODAL START-->
    <div
      class="modal fade"
      id="reserveModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="reserveModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered" style="max-width: 1200px">
        <div class="modal-content">
          <div class="modal-header">
            <div class="d-flex align-items-center">
              <h2 class="modal-title" id="reserve-modal-title">會議預約</h2>
            </div>
            <div class="d-flex align-items-center">
              <input
                type="image"
                id="deleteBtn"
                th:src="@{/assets/meeting/images/trash.svg}"
                alt=""
                style="
                  display: none;
                  margin-right: 15px;
                  width: 25px;
                  height: 25px;
                "
              />
              <button
                type="button"
                class="btn-close"
                data-bs-target="#cancelModal"
                data-bs-toggle="modal"
                aria-label="Close"
              ></button>
            </div>
          </div>
          <div class="modal-body" style="padding: 5px">
            <div style="height: auto; width: 38%">
              <span
                style="
                  color: red;
                  font-size: 0.75rem;
                  margin-left: 15px;
                  align-items: flex-end;
                "
              >
                標有<span style="padding: 0 0.25rem">＊</span
                >的欄位為必要資料，請正確填寫。
              </span>
            </div>

            <!-- 預約類型 -->
            <div
              style="
                margin-left: 42%;
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
              "
            >
              <span id="type">預約類型：</span>
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="radio"
                  name="reserveType"
                  id="oneceReserveChange"
                  checked
                />
                <label
                  class="form-check-label text-dark"
                  id="onceReserveChangeText"
                  for="oneceReserveChange"
                  >一次性預約</label
                >
              </div>
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="radio"
                  name="reserveType"
                  id="dailyReserveChange"
                />
                <label
                  class="form-check-label text-dark"
                  id="dailyReserveChangeText"
                  for="dailyReserveChange"
                  >每日預約</label
                >
              </div>
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="radio"
                  name="reserveType"
                  id="reserveChange"
                />
                <label
                  class="form-check-label text-dark"
                  id="reserveChangeText"
                  for="reserveChange"
                  >每週預約</label
                >
              </div>
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="radio"
                  name="reserveType"
                  id="monthReserveChange"
                />
                <label
                  class="form-check-label text-dark"
                  id="monthReserveChangeText"
                  for="monthReserveChange"
                  >每月預約</label
                >
              </div>

              <button
                class="btn btn-danger"
                id="cleanBtn"
                style="font-size: 0.75rem; margin-right: 15px"
              >
                全部清除
              </button>
            </div>
            <div
              style="
                display: flex;
                flex-direction: row;
                justify-content: space-around;
                align-items: stretch;
                margin: 0 5px 5px 5px;
              "
            >
              <div class="card" style="margin: 8px; height: auto; width: 38%">
                <div class="card-body">
                  <h5 class="card-title">會議基本資訊</h5>

                  <div class="row">
                    <div class="col-12 mb-3">
                      <div
                        style="
                          display: flex;
                          flex-direction: row;
                          justify-content: space-between;
                        "
                      >
                        <label class="required" for="theme">會議主題</label>
                        <span
                          id="theme_error"
                          style="
                            color: red;
                            font-size: 0.75rem;
                            align-items: flex-end;
                          "
                        ></span>
                      </div>
                      <input
                        type="text"
                        class="form-control"
                        placeholder="會議主題"
                        id="theme"
                        autocomplete="off"
                      />
                    </div>
                    <div class="col-12 mb-3">
                      <div class="row">
                        <div class="col-md-6" id="startDate">
                          <label class="required" for="reserveDate">
                            開始日期
                          </label>
                          <span
                            id="reserveDate_error"
                            style="
                              color: red;
                              font-size: 0.75rem;
                              align-items: flex-end;
                            "
                          ></span>
                          <div>
                            <input
                              type="date"
                              class="form-control flatp-date-picker"
                              id="reserveDate"
                            />
                          </div>
                        </div>
                        <div class="col-md-6" id="endDate">
                          <label class="required" for="reserveDateEnd">
                            週期性結束日期
                          </label>
                          <span
                            id="reserveDateEnd_error"
                            style="
                              color: red;
                              font-size: 0.75rem;
                              align-items: flex-end;
                            "
                          ></span>
                          <div>
                            <input
                              type="date"
                              class="form-control flatp-date-picker"
                              id="reserveDateEnd"
                              disabled="disabled"
                            />
                          </div>
                        </div>
                      </div>
                      <div class="row">
                        <div
                          class="col-md-6"
                          id="weekOfMonthSelect"
                          style="display: none"
                        >
                          <label class="required" for="weekOfMonth"
                            >每月第幾週
                          </label>
                          <span
                            id="weekOfMonth_error"
                            style="
                              color: red;
                              font-size: 0.75rem;
                              align-items: flex-end;
                            "
                          ></span>
                          <div>
                            <select
                              id="weekOfMonth"
                              class="form-select shadow-gray"
                            >
                              <option value="" selected>請選擇</option>
                              <option value="1">第 1 週</option>
                              <option value="2">第 2 週</option>
                              <option value="3">第 3 週</option>
                              <option value="4">第 4 週</option>
                              <option value="5">最後一週</option>
                            </select>
                          </div>
                        </div>

                        <div
                          class="col-md-6"
                          id="dayOfWeekSelect"
                          style="display: none"
                        >
                          <label class="required" for="dayOfWeek">
                            星期幾</label
                          >
                          <span
                            id="dayOfWeek_error"
                            style="
                              color: red;
                              font-size: 0.75rem;
                              align-items: flex-end;
                            "
                          ></span>
                          <div>
                            <select
                              id="dayOfWeek"
                              class="form-select shadow-gray"
                            >
                              <option value="" selected>請選擇</option>
                              <option value="1">星期一</option>
                              <option value="2">星期二</option>
                              <option value="3">星期三</option>
                              <option value="4">星期四</option>
                              <option value="5">星期五</option>
                              <option value="6">星期六</option>
                              <option value="7">星期日</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-12 mb-3">
                      <span
                        id="timeSet_error"
                        style="
                          color: red;
                          font-size: 0.75rem;
                          align-items: flex-end;
                        "
                      ></span>
                      <div class="row">
                        <div class="col-md-6">
                          <label class="required"> 開始時間 </label>
                          <span
                            id="beginTime_error"
                            style="
                              color: red;
                              font-size: 0.75rem;
                              align-items: flex-end;
                            "
                          ></span>
                          <div>
                            <input
                              type="date"
                              id="beginTime"
                              class="form-control flatp-time-picker"
                            />
                          </div>
                        </div>
                        <div class="col-md-6">
                          <label class="required"> 結束時間 </label>
                          <span
                            id="endTime_error"
                            style="
                              color: red;
                              font-size: 0.75rem;
                              align-items: flex-end;
                            "
                          ></span>
                          <div>
                            <input
                              type="date"
                              id="endTime"
                              class="form-control flatp-time-picker"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-12 mb-3">
                      <label class="required">會議室名稱</label>
                      <span
                        id="roomId_error"
                        style="
                          color: red;
                          font-size: 0.75rem;
                          align-items: flex-end;
                        "
                      ></span>
                      <div class="select-div-long">
                        <select id="room" class="form-select shadow-gray">
                          <option value="" selected>請選擇</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-12 mb-3">
                      <label>樓層</label>
                      <span
                        id="floor_error"
                        style="
                          color: red;
                          font-size: 0.75rem;
                          align-items: flex-end;
                        "
                      ></span>
                      <div class="select-div">
                        <input
                          type="text"
                          id="floor"
                          class="form-control shadow-gray"
                          disabled="disabled"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card" style="margin: 8px; height: auto; width: 58%">
                <div class="card-body" style="z-index: 3">
                  <h5 class="card-title"></h5>
                  <div class="row" style="z-index: 4">
                    <div class="col-md-4 mb-3">
                      <div
                        style="
                          display: flex;
                          flex-direction: row;
                          justify-content: space-between;
                        "
                      >
                        <label class="required" for="host">主持人</label>
                        <span
                          id="hostName_error"
                          style="
                            color: red;
                            font-size: 0.75rem;
                            align-items: flex-end;
                          "
                        ></span>
                      </div>

                      <div class="input-group">
                        <input
                          type="text"
                          class="form-control"
                          aria-label="Text input with 2 dropdown buttons"
                          id="hostName"
                          placeholder="主持人姓名"
                          autocomplete="off"
                        />
                      </div>
                    </div>
                    <div class="col-md-4 mb-3">
                      <div
                        style="
                          display: flex;
                          flex-direction: row;
                          justify-content: space-between;
                          align-items: flex-end;
                        "
                      >
                        <label class="required" for="contact">聯絡人</label>
                        <span
                          id="contact_error"
                          style="color: red; font-size: 0.75rem"
                        ></span>
                      </div>

                      <div class="input-group mb-2">
                        <input
                          type="text"
                          class="form-control"
                          aria-label="Text input with 2 dropdown buttons"
                          id="contact-name"
                          placeholder="聯絡人姓名"
                          autocomplete="off"
                        />
                        <button
                          type="button"
                          class="btn btn-primary dropdown-toggle"
                          id="contact-select-btn"
                          data-bs-toggle="dropdown"
                          aria-haspopup="true"
                          aria-expanded="false"
                        ></button>
                        <div class="dropdown-menu" style="width: 400px">
                          <div class="card-body">
                            <h5 class="card-title">快速帶入 - 聯絡人</h5>
                            <div class="row">
                              <div class="col-12 mb-3">
                                <label class="required" for="contact-company"
                                  >公司</label
                                >
                                <div class="select-div">
                                  <select
                                    id="contact-company"
                                    class="form-select shadow-gray"
                                  >
                                    <option value="" selected>請選擇</option>
                                    <option
                                      th:each="companySelect : ${resp.companySelects}"
                                      th:value="${companySelect.value}"
                                      th:text="${companySelect.label}"
                                    ></option>
                                  </select>
                                </div>
                              </div>
                              <div class="col-12 mb-3">
                                <label class="required" for="contact-depart"
                                  >單位</label
                                >
                                <div class="select-div">
                                  <select
                                    id="contact-depart"
                                    class="form-select shadow-gray"
                                  >
                                    <option value="" selected>請選擇</option>
                                  </select>
                                </div>
                              </div>
                              <div class="col-12 mb-3">
                                <label class="required" for="contact-user"
                                  >人員</label
                                >
                                <div class="select-div">
                                  <select
                                    id="contact-user"
                                    class="form-select shadow-gray"
                                  >
                                    <option value="" selected>請選擇</option>
                                  </select>
                                </div>
                              </div>
                              <div
                                class="col-12 mb-3"
                                style="text-align: right"
                              >
                                <button
                                  type="button"
                                  class="btn btn-secondary"
                                  id="contact-user-confirm"
                                  disabled="disabled"
                                >
                                  確定
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <input
                        type="text"
                        class="form-control"
                        placeholder="聯絡電話"
                        id="contact-phone"
                        autocomplete="off"
                      />
                    </div>
                    <div class="col-md-4 mb-3">
                      <div
                        style="
                          display: flex;
                          flex-direction: row;
                          justify-content: space-between;
                          align-items: flex-end;
                        "
                      >
                        <label class="required" for="reserveDept"
                          >預約單位</label
                        >
                        <span
                          id="reserveDeptName_error"
                          style="color: red; font-size: 0.75rem"
                        ></span>
                      </div>

                      <div class="input-group mb-2">
                        <input type="hidden" id="reserveCompanyId" />
                        <input type="hidden" id="reserveDeptId" />
                        <input
                          type="text"
                          class="form-control"
                          aria-label="Text input with 2 dropdown buttons"
                          id="reserveDeptName"
                          placeholder="預約單位"
                          readonly
                        />
                        <button
                          type="button"
                          class="btn btn-primary dropdown-toggle"
                          id="reserveDept-select-btn"
                          data-bs-toggle="dropdown"
                          aria-haspopup="true"
                          aria-expanded="false"
                        ></button>
                        <div class="dropdown-menu" style="width: 400px">
                          <div class="card-body">
                            <h5 class="card-title">快速帶入 - 預約單位</h5>
                            <div class="row">
                              <div class="col-12 mb-3">
                                <label
                                  class="required"
                                  for="reserveDept-company"
                                  >公司</label
                                >
                                <div class="select-div">
                                  <select
                                    id="reserveDept-company"
                                    class="form-select shadow-gray"
                                  >
                                    <option value="" selected>請選擇</option>
                                    <option
                                      th:each="companySelect : ${resp.companySelects}"
                                      th:value="${companySelect.value}"
                                      th:text="${companySelect.label}"
                                    ></option>
                                  </select>
                                </div>
                              </div>
                              <div class="col-12 mb-3">
                                <label class="required" for="reserveDept-depart"
                                  >單位</label
                                >
                                <div class="select-div">
                                  <select
                                    id="reserveDept-depart"
                                    class="form-select shadow-gray"
                                  >
                                    <option value="" selected>請選擇</option>
                                  </select>
                                </div>
                              </div>
                              <div
                                class="col-12 mb-3"
                                style="text-align: right"
                              >
                                <button
                                  type="button"
                                  class="btn btn-primary"
                                  id="reserveDept-depart-confirm"
                                >
                                  確定
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-12 mb-3">
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          margin-bottom: 8px;
                        "
                      >
                        <div class="col-2">
                          <label class="required"> 與會單位 </label>
                          <span
                            id="attendDept_erro"
                            style="color: red; font-size: 0.75rem"
                          ></span>
                        </div>
                        <div
                          style="
                            display: flex;
                            flex-direction: row;
                            justify-content: flex-end;
                            align-items: flex-end;
                          "
                        >
                          <div
                            class="select-div row"
                            style="margin-right: 10px"
                          >
                            <select
                              id="attendDept"
                              class="form-select shadow-gray col"
                            >
                              <option value="" selected>請選擇</option>
                              <option
                                th:each="departSelect : ${resp.departmentSelects}"
                                th:value="${departSelect.value}"
                                th:text="${departSelect.label}"
                              ></option>
                            </select>
                            <input
                              type="text"
                              id="attendDeptInput"
                              value=""
                              placeholder="手動輸入與會單位"
                              class="form-control col ms-2"
                              aria-label="Text input with 2 dropdown buttons"
                            />
                          </div>
                          <button
                            class="form-control"
                            id="addBtn"
                            type="button"
                            style="width: 40px"
                          >
                            +
                          </button>
                        </div>
                      </div>
                      <div
                        class="form-control"
                        id="dept-name"
                        style="
                          display: flex;
                          flex-direction: row;
                          flex-wrap: wrap;
                          min-height: 45px;
                        "
                      ></div>
                    </div>

                    <div class="col-md-4 mb-3">
                      <div
                        style="
                          display: flex;
                          flex-direction: row;
                          justify-content: space-between;
                        "
                      >
                        <label class="required" for="members">預估人數</label>
                        <span
                          id="members_error"
                          style="
                            color: red;
                            font-size: 0.75rem;
                            align-items: flex-end;
                          "
                        ></span>
                      </div>
                      <input
                        type="text"
                        class="form-control"
                        placeholder="預估人數"
                        id="members"
                        autocomplete="off"
                      />
                    </div>

                    <div class="col-md-4 mb-3">
                      <div
                        style="
                          display: flex;
                          flex-direction: row;
                          justify-content: space-between;
                        "
                      >
                        <label class="required" for="meetingMode"
                          >會議模式</label
                        >
                        <span
                          id="meetingMode_error"
                          style="
                            color: red;
                            font-size: 0.75rem;
                            align-items: flex-end;
                          "
                        ></span>
                      </div>

                      <div class="form-check">
                        <div
                          th:each="meetModeSelect : ${resp.meetingModeSelects}"
                        >
                          <input
                            type="radio"
                            class="form-check-input"
                            name="meetingMode"
                            th:value="${meetModeSelect.value}"
                            onclick="meetingUrl(this)"
                          />
                          <label
                            class="form-check-label"
                            th:text="${meetModeSelect.label}"
                            >會議模式</label
                          >
                        </div>
                        <input
                          type="text"
                          id="url"
                          class="form-control mt-2"
                          placeholder="請輸入會議連結"
                          style="display: none"
                        />
                      </div>
                    </div>
                    <div class="col-md-4 mb-3">
                      <div
                        style="
                          display: flex;
                          flex-direction: row;
                          justify-content: space-between;
                        "
                      >
                        <label class="required">會議情境</label>
                        <span
                          id="situationMode_error"
                          style="
                            color: red;
                            font-size: 0.75rem;
                            align-items: flex-end;
                          "
                        ></span>
                      </div>

                      <div class="form-check">
                        <div
                          th:each="situationModeSelect : ${resp.situationModeSelects}"
                        >
                          <input
                            type="radio"
                            class="form-check-input"
                            name="situationMode"
                            th:value="${situationModeSelect.value}"
                          />
                          <label
                            class="form-check-label"
                            th:text="${situationModeSelect.label}"
                            >會議情境</label
                          >
                        </div>
                      </div>
                    </div>

                    <div class="col-md-4 mb-3">
                      <div
                        style="
                          display: flex;
                          flex-direction: row;
                          justify-content: space-between;
                        "
                      >
                        <label class="required" for="personal">是否公開 </label>
                        <span
                          id="personal_error"
                          style="
                            color: red;
                            font-size: 0.75rem;
                            align-items: flex-end;
                          "
                        ></span>
                      </div>
                      <div>
                        <input
                          type="radio"
                          class="form-check-input"
                          name="personal"
                          value="false"
                        />
                        <label class="form-check-label">公開</label>
                      </div>
                      <div>
                        <input
                          type="radio"
                          class="form-check-input"
                          name="personal"
                          value="true"
                        />
                        <label class="form-check-label">不公開</label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button class="btn rounded-pill btn-purple" id="confirmBtn">
              預約
            </button>
          </div>
        </div>
      </div>
    </div>
    <!--預約MODAL END-->

    <!--取消MODAL START-->
    <div
      class="modal fade"
      id="cancelModal"
      aria-hidden="true"
      aria-labelledby="exampleModalToggleLabel2"
      tabindex="-1"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-body">
            <h3>是否捨棄尚未儲存的變更內容</h3>
          </div>
          <div class="modal-footer">
            <button
              class="btn rounded-pill btn-secondary"
              id="cancelBtn"
              data-bs-dismiss="modal"
            >
              取消
            </button>
            <button
              class="btn rounded-pill btn-purple"
              id="cancelConfirmBtn"
              data-bs-dismiss="modal"
            >
              確定
            </button>
          </div>
        </div>
      </div>
    </div>
    <!--取消MODAL END-->

    <!--回傳MODAL START-->
    <div
      class="modal fade"
      id="editCallBack"
      aria-hidden="true"
      aria-labelledby="exampleModalToggleLabel2"
      tabindex="-1"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-body">
            <div class="d-flex justify-content-center mb-2" id="callBack"></div>
            <div class="d-flex justify-content-center mb-2">
              <input
                type="image"
                id="checkPic"
                data-bs-dismiss="modal"
                th:src="@{/assets/meeting/images/check.svg}"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--回傳MODAL END-->

    <div
      class="modal fade"
      id="deleteConfirmModal"
      aria-hidden="true"
      aria-labelledby="exampleModalToggleLabel2"
      tabindex="-1"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-body">
            <h3>請再次確認是否刪除該會議預約</h3>
          </div>
          <div class="modal-footer">
            <button
              class="btn rounded-pill btn-secondary"
              data-bs-target="#reserveModal"
              data-bs-toggle="modal"
              data-bs-dismiss="modal"
            >
              取消
            </button>
            <button
              class="btn rounded-pill btn-purple"
              id="deleteConfirmBtn"
              data-bs-dismiss="modal"
            >
              確定
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 會議細節MODAL START -->
    <div
      class="modal fade"
      id="reserveDetailModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="reserveModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered" style="max-width: 1200px">
        <div class="modal-content">
          <div class="modal-header">
            <h2 class="modal-title">會議細節</h2>
            <div class="d-flex align-items-center">
              <input
                type="image"
                id="editBtn"
                style="
                  display: none;
                  width: 25px;
                  height: 25px;
                  margin-right: 15px;
                "
                src="../assets/meeting/images/edit.png"
              />
              <input
                type="image"
                id="QRCodeBtn"
                style="
                  display: none;
                  width: 25px;
                  height: 25px;
                  margin-right: 15px;
                "
                src="../assets/meeting/images/QRCode.png"
              />
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
          </div>

          <div class="modal-body">
            <div class="row">
              <div class="col-6 mb-3">
                <label>會議名稱 : </label>
                <h5 id="detail-theme"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>預約時間 : </label>
                <h5 id="detail-time"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>樓層 : </label>
                <h5 id="detail-floor"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議室名稱 : </label>
                <h5 id="detail-roomname"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>主持人 : </label>
                <h5 id="detail-hostname"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>聯絡人 : </label>
                <h5 id="detail-contactname"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>聯絡人電話 : </label>
                <h5 id="detail-contactphone"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>預約單位 : </label>
                <h5 id="detail-deptart"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>與會單位 : </label>
                <h5 id="detail-attenddeptart"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議模式 : </label>
                <h5 id="detail-meetingmode"></h5>
                <h5 id="detail-meetingmode-link"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>會議情境 : </label>
                <h5 id="detail-situationmode"></h5>
              </div>
              <div class="col-6 mb-3">
                <label>預估與會人數 : </label>
                <h5 id="detail-members"></h5>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 會議細節MODAL END -->

    <!--明細MODAL START-->
    <div class="modal fade" id="detailModal" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-body">
            <div class="row justify-content-center">
              <div class="col align-self-center" id="details"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--明細MODAL END-->
    <!--QRCodeMODAL START-->
    <div class="modal fade" id="qrCodeModal" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h4>會議預約 QR Code</h4>
            <div class="d-flex align-items-center">
              <!-- 下載 QR Code -->
              <input
                type="image"
                id="qrCodeDownloadBtn"
                th:src="@{/assets/meeting/images/download-black.png}"
              />
              <!-- 關閉按鈕 -->
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
          </div>
          <!-- QR Code 圖檔 -->
          <div class="modal-body">
            <div class="row justify-content-center" id="qrCodeContainer">
              <img id="qrCodeImage" style="width: 200px; height: 200px" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--QRCodeMODAL END-->
  </div>

  <th:block layout:fragment="custom-script">
    <script th:src="@{/assets/meeting/js/calendar.js}"></script>
    <script th:inline="javascript">
      var resp = [[${resp}]];
    </script>
    <script th:src="@{/assets/meeting/js/reserve.js}"></script>
  </th:block>
</html>
