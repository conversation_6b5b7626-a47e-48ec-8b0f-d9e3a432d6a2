rootLogger.level = INFO
property.basePath = C:\\Program Files\\Apache Software Foundation\\Tomcat 10.1\\logs
property.filename = application.log
property.charset=utf-8
appenders = R, console


logger.app.name = com.twport
logger.app.level = DEBUG

appender.FILE.encoding=UTF-8

appender.console.type = Console
appender.console.name = STDOUT
appender.console.layout.type = PatternLayout
appender.console.layout.charset = utf-8
appender.console.layout.pattern = %d{yy-MM-dd HH:mm:ss} [%-5p] %C{3}:%L - %m%n

appender.R.type = RollingFile
appender.R.name = File
appender.R.fileName = ${basePath}/${filename}
appender.R.filePattern = ${basePath}/${filename}.%d{yyyy-MM-dd}
appender.R.layout.type = PatternLayout
appender.R.layout.charset = utf-8
appender.R.layout.pattern = %d{yy-MM-dd HH:mm:ss} [%-5p] %C{3}:%L - %m%n
appender.R.policies.type = Policies
appender.R.policies.time.type = TimeBasedTriggeringPolicy
appender.R.policies.time.interval = 1
appender.R.policies.size.type = SizeBasedTriggeringPolicy
appender.R.policies.size.size = 20MB
appender.R.strategy.type = DefaultRolloverStrategy
appender.R.strategy.max = 30

rootLogger.appenderRefs = R, console

rootLogger.appenderRef.console.ref = STDOUT
rootLogger.appenderRef.R.ref = File
