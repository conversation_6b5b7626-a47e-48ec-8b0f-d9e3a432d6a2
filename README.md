# 高雄港旅運中心會議室預約系統

## 專案概述

高雄港旅運中心會議室預約系統是一個基於 Spring Boot 開發的會議室預約管理平台，提供會議室預約、管理、統計等功能。系統支援多種預約模式（一般、線上、混合會議）和情境模式（一般、簡報、視訊會議），並整合了 LDAP 身份驗證。

## 技術棧

- **後端框架**: Spring Boot 3.3.1
- **資料庫**: PostgreSQL
- **ORM**: Spring Data JPA
- **前端模板**: Thymeleaf
- **安全框架**: Spring Security
- **身份驗證**: LDAP
- **日誌**: Log4j2
- **其他工具**:
  - iText PDF (文件生成)
  - Apache POI (Excel 處理)
  - Lombok (簡化代碼)

## 系統功能

### 會議室管理
- 會議室新增、編輯、刪除
- 會議室啟用/停用
- 會議室設備管理（環控設備、線上會議設備）

### 會議預約
- 多種預約模式：一般會議、線上會議、混合會議
- 多種情境模式：一般會議、簡報會議、視訊會議
- 支援週期性預約：每日、每週、每月
- 預約衝突檢查
- 會議 QR 碼生成

### 會議記錄
- 會議歷史記錄查詢
- 會議狀態追蹤（預約中、進行中、已結束、已取消）

### 統計報表
- 會議室使用率統計
- 各類會議模式統計
- 各部門會議統計

### 用戶管理
- LDAP 身份驗證
- 角色權限管理
- 部門管理

## 系統架構

### 主要模組

- **控制器層 (Controller)**: 處理 HTTP 請求，提供 API 接口
- **服務層 (Service)**: 實現業務邏輯
- **數據訪問層 (Repository)**: 與資料庫交互
- **實體層 (Entity)**: 定義資料庫表結構
- **DTO/VO 層**: 數據傳輸對象和視圖對象
- **配置層 (Config)**: 系統配置

### 資料庫設計

主要資料表：
- `meeting_room`: 會議室資訊
- `meeting_reserve`: 會議預約資訊
- `meeting_reserve_department`: 會議與部門關聯
- `department_list`: 部門資訊
- `company_list`: 公司資訊
- `app_user`: 用戶資訊

## 環境配置

系統支援多環境配置：
- `application-dev.properties`: 開發環境
- `application-frontend.properties`: 正式環境

## 快速開始

### 系統需求
- JDK 17+
- PostgreSQL 資料庫
- Gradle 7.0+

### 編譯與運行

1. 克隆專案
```bash
git clone [repository-url]
cd meeting_backend
```

2. 配置資料庫
   - 創建 PostgreSQL 資料庫
   - 修改 `application-dev.properties` 中的資料庫連接配置

3. 編譯專案
```bash
./gradlew build
```

4. 運行專案
```bash
./gradlew bootRun
```
或
```bash
java -jar build/libs/meeting_backend-0.0.1-SNAPSHOT.war
```

5. 訪問系統
```
http://localhost:8080/meeting-reserve
```

## 部署指南

### WAR 檔部署
系統支援 WAR 檔部署到外部 Tomcat 伺服器：

1. 生成 WAR 檔
```bash
./gradlew build
```

2. 更改環境變數（請參考環境變數配置）

3. 將生成的 WAR 檔部署到 Tomcat 伺服器
- 上傳WAR檔
![上傳WAR檔](./image/upload.png)
- 前往Tomcat
![前往Tomcat](./image/toTomcat.png)
- 進入Tomcat管理介面（帳號：admin，密碼：123456）
![進入Tomcat管理介面](./image/manage.png)
- 部署WAR檔（檔名要改成meeting-reserve.war）
![部署WAR檔](./image/deploy.png)


### 環境變數配置
可通過環境變數覆蓋配置：
- 開發環境
- `spring.profile.active=dev`
- `database-schema=public`
- 正式環境
- `spring.profile.active=frontend`
- `database-schema=develop`

## 授權

本專案為台灣港務公司內部使用，未經授權不得外部使用或分發。

## 聯絡方式

如有任何問題或建議，請聯繫系統管理員。
