# AOP Controller 監控功能說明

## 概述

本專案已實作 AOP (Aspect-Oriented Programming) 來監控所有 Controller 的執行情況，自動記錄每個 API 請求的詳細資訊。

## 功能特色

### 1. 自動監控所有 Controller
- 攔截 `com.twport.meeting_backend.controller` 包下的所有方法
- 無需手動添加日誌代碼，自動記錄所有 API 請求

### 2. 詳細的請求資訊記錄
- **請求基本資訊**: HTTP 方法、URI、IP 地址
- **執行時間**: 精確計算每個請求的執行時間（毫秒）
- **方法資訊**: Controller 類別名稱、方法名稱
- **請求參數**: URL 參數、請求體參數
- **返回結果**: 返回值類型和內容
- **異常處理**: 自動捕獲並記錄異常資訊

### 3. 多層次日誌記錄
- **INFO 級別**: 請求開始、完成、返回結果
- **DEBUG 級別**: 詳細的請求參數、請求頭資訊
- **ERROR 級別**: 異常資訊和錯誤詳情

## 日誌輸出範例

### 正常請求
```
2024-12-19 10:30:15 [INFO ] ControllerLoggingAspect - === 請求開始 === [GET] /meeting-reserve/test/aop/get | 控制器: TestAopController.testGet | IP: 127.0.0.1
2024-12-19 10:30:15 [DEBUG] ControllerLoggingAspect - 請求詳情 [GET] /meeting-reserve/test/aop/get | User-Agent: Mozilla/5.0... | 請求參數: message: [test]; | 方法參數: 參數1: String;
2024-12-19 10:30:15 [INFO ] ControllerLoggingAspect - 返回結果 [GET] /meeting-reserve/test/aop/get | 結果類型: AjaxRestResponse
2024-12-19 10:30:15 [INFO ] ControllerLoggingAspect - === 請求完成 === [GET] /meeting-reserve/test/aop/get | 控制器: TestAopController.testGet | IP: 127.0.0.1 | 執行時間: 25ms
```

### 異常請求
```
2024-12-19 10:35:20 [INFO ] ControllerLoggingAspect - === 請求開始 === [GET] /meeting-reserve/test/aop/error | 控制器: TestAopController.testError | IP: 127.0.0.1
2024-12-19 10:35:20 [ERROR] ControllerLoggingAspect - 異常發生 [GET] /meeting-reserve/test/aop/error | 異常類型: RuntimeException | 異常訊息: 這是一個測試異常
2024-12-19 10:35:20 [ERROR] ControllerLoggingAspect - === 請求異常 === [GET] /meeting-reserve/test/aop/error | 控制器: TestAopController.testError | IP: 127.0.0.1 | 執行時間: 5ms | 異常: 這是一個測試異常
```

## 配置說明

### 1. 依賴配置
已在 `build.gradle` 中添加 AOP 依賴：
```gradle
implementation 'org.springframework.boot:spring-boot-starter-aop'
```

### 2. AOP 啟用
在主應用程式類別中啟用 AOP：
```java
@SpringBootApplication
@EnableAspectJAutoProxy
public class MettingBackendApplication {
    // ...
}
```

### 3. 日誌配置
- 主要日誌文件: `application.log`
- AOP 專用日誌文件: `aop.log`
- 日誌級別: AOP 相關為 INFO，應用程式為 DEBUG

## 測試 API 端點

為了測試 AOP 功能，已提供以下測試端點：

### 1. GET 請求測試
```
GET /meeting-reserve/test/aop/get?message=hello
```

### 2. POST 請求測試
```
POST /meeting-reserve/test/aop/post
Content-Type: application/json
Body: "test message"
```

### 3. 異常測試
```
GET /meeting-reserve/test/aop/error
```

### 4. 長時間執行測試
```
GET /meeting-reserve/test/aop/slow
```

## 監控的 Controller 列表

目前系統中的所有 Controller 都會被自動監控：

1. **ApiController** (`/api/*`)
   - 心跳檢測、測試用戶創建等

2. **ApiInfoController** (`/infoApi/*`)
   - 會議詳情查詢、會議搜尋、設定會議室等

3. **ApiSignController** (`/signApi/*`)
   - 會議查詢、簽到上傳、簽到名單、會議室清單等

4. **ReserveController** (`/meeting/reserve/*`)
   - 會議預約、查詢、新增、刪除、更新等

5. **RecordController** (`/meeting/record/*`)
   - 會議記錄查詢

6. **ManageController** (`/meeting/manage/*`)
   - 會議室管理

7. **StatisticsController** (`/meeting/statistics/*`)
   - 統計資料查詢、圖表資料

8. **UserController** (`/user/*`)
   - 用戶登入、登出

9. **TestAopController** (`/test/aop/*`)
   - AOP 功能測試

## 注意事項

1. **效能影響**: AOP 監控會增加少量的執行時間（通常 < 5ms），但提供了寶貴的監控資訊
2. **日誌大小**: 建議定期清理日誌文件，避免磁碟空間不足
3. **敏感資訊**: 系統會自動過濾 Authorization 和 Cookie 等敏感請求頭資訊
4. **客戶端 IP**: 支援透過 X-Forwarded-For 和 X-Real-IP 獲取真實客戶端 IP

## 自定義配置

如需調整監控範圍或日誌級別，可以修改：

1. **切點範圍**: 修改 `ControllerLoggingAspect.java` 中的 `@Pointcut` 註解
2. **日誌級別**: 修改 `log4j2.xml` 中的 Logger 配置
3. **日誌格式**: 調整 `PatternLayout` 的 pattern 屬性

## 故障排除

如果 AOP 監控沒有生效，請檢查：

1. 確認 `spring-boot-starter-aop` 依賴已正確添加
2. 確認 `@EnableAspectJAutoProxy` 註解已添加到主應用程式類別
3. 確認 `ControllerLoggingAspect` 類別有 `@Component` 註解
4. 檢查日誌級別配置是否正確
